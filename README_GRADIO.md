# SQL Tool - Giao diện Gradio

## 🎯 Tổng quan

Ứng dụng SQL Tool hiện đã được tích hợp giao diện web thân thiện với Gradio, cho phép người dùng tương tác dễ dàng thông qua trình duyệt web thay vì chỉ sử dụng command line.

## 🚀 Cách chạy ứng dụng

### Phương pháp 1: Sử dụng script tự động
```bash
python run_gradio.py
```

### Phương pháp 2: Chạy trực tiếp
```bash
python gradio_app.py
```

### Phương pháp 3: Command line (gi<PERSON> nguyên)
```bash
python main.py --question "Câu hỏi của bạn"
```

## 🌐 Truy cập ứng dụng

Sau khi chạy, ứng dụng sẽ khả dụng tại:
- **Local**: http://localhost:7860
- **Network**: http://0.0.0.0:7860 (c<PERSON> thể truy cập từ các máy khác trong mạng)

## 📋 Tính năng giao diện Gradio

### 🎨 Giao diện chính
- **Input**: Ô nhập câu hỏi tiếng Việt
- **Model Configuration**: Cấu hình các AI models
- **Output**: Hiển thị SQL query, kết quả thô và câu trả lời cuối cùng
- **Status**: Trạng thái xử lý (thành công/lỗi)

### ⚙️ Cấu hình Models
- **SQL LLM Model**: Model để sinh SQL (mặc định: deepseek-v3.1:671b-cloud)
- **Fix LLM Model**: Model để sửa lỗi SQL (mặc định: qwen3-coder:480b-cloud)  
- **Answer LLM Model**: Model để tạo câu trả lời (mặc định: kimi-k2:1t-cloud)
- **Translation Model**: Model dịch thuật (mặc định: VietAI/envit5-translation)

### 🔄 Tính năng bổ sung
- **Refresh Schema**: Làm mới knowledge graph và schema
- **Examples**: Các câu hỏi mẫu để test nhanh
- **Real-time Processing**: Xử lý và hiển thị kết quả ngay lập tức

## 📝 Hướng dẫn sử dụng

1. **Khởi động ứng dụng**:
   ```bash
   python run_gradio.py
   ```

2. **Mở trình duyệt** và truy cập http://localhost:7860

3. **Nhập câu hỏi** tiếng Việt vào ô input

4. **Cấu hình models** (tùy chọn) nếu muốn thay đổi

5. **Nhấn "🚀 Thực hiện"** để xử lý

6. **Xem kết quả**:
   - SQL Query được sinh ra
   - Kết quả thô từ database
   - Câu trả lời tiếng Việt cuối cùng
   - Trạng thái xử lý

## 🔧 Cài đặt Dependencies

Gradio đã được thêm vào `requirements.txt`. Để cài đặt:

```bash
pip install -r requirements.txt
```

Hoặc chỉ cài Gradio:
```bash
pip install gradio>=4.0.0
```

## 📊 Ví dụ câu hỏi

- "Doanh thu của sản phẩm Shopee trong năm 2025?"
- "Có bao nhiêu khách hàng đã mua hàng trong tháng này?"
- "Top 5 sản phẩm bán chạy nhất?"
- "Tổng số đơn hàng trong quý 1 năm 2024?"

## 🔍 So sánh với Command Line

| Tính năng | Command Line | Gradio Web UI |
|-----------|--------------|---------------|
| Nhập câu hỏi | Argument `--question` | Text input box |
| Cấu hình models | Arguments | Form inputs |
| Xem SQL | Terminal output | Code block |
| Xem kết quả | Terminal output | Formatted display |
| Lưu lịch sử | CSV file | CSV file + UI display |
| Ease of use | Technical users | All users |
| Accessibility | Command line only | Web browser |

## 🛠️ Cấu trúc file mới

```
sql_tool/
├── main.py              # Command line interface (giữ nguyên)
├── gradio_app.py        # Gradio web interface (mới)
├── run_gradio.py        # Script chạy Gradio (mới)
├── README_GRADIO.md     # Hướng dẫn Gradio (mới)
├── requirements.txt     # Đã thêm gradio
└── ...                  # Các file khác giữ nguyên
```

## 🔒 Bảo mật

- Ứng dụng chạy local trên port 7860
- Không tạo public link mặc định
- Có thể cấu hình để chỉ cho phép truy cập local

## 🐛 Troubleshooting

### Lỗi port đã được sử dụng
```bash
# Thay đổi port trong gradio_app.py
demo.launch(server_port=7861)  # Thay 7860 thành port khác
```

### Lỗi import gradio
```bash
pip install gradio>=4.0.0
```

### Lỗi kết nối database
- Kiểm tra cấu hình trong `config/settings.py`
- Đảm bảo database Odoo đang chạy
- Kiểm tra network connectivity

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra log trong terminal
2. Đảm bảo tất cả dependencies đã được cài đặt
3. Kiểm tra cấu hình database và models
