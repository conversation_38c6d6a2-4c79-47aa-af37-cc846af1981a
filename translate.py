# translate.py

from transformers import AutoTokenizer, AutoModelForSeq2SeqLM

# Tải Tokenizer và Model từ Hugging Face Hub
# Lần đầu chạy, nó sẽ tải model về và lưu vào cache (~2.4GB)
# Những lần sau sẽ load từ cache nên sẽ nhanh hơn.
print("Đang tải model và tokenizer...")
tokenizer = AutoTokenizer.from_pretrained("VietAI/envit5-translation")
model = AutoModelForSeq2SeqLM.from_pretrained("VietAI/envit5-translation")
print("Tải xong!")

# Danh sách các câu cần dịch (bao gồm cả Anh-Việt và Việt-Anh)
sentences_to_translate = [
    # Dịch Anh -> Việt
    "en: The quick brown fox jumps over the lazy dog.",
    "en: I love to use open-source models for my projects.",
    
    # Dịch Việt -> Anh
    "vi: Tôi muốn sử dụng mô hình mã nguồn mở cho các dự án của mình.",
    "vi: Trí tuệ nhân tạo đang thay đổi thế giới."
]

# Chuẩn bị dữ liệu đầu vào
# Tokenizer sẽ chuyển các câu văn thành các con số mà model có thể hiểu được
inputs = tokenizer(
    sentences_to_translate, 
    return_tensors="pt",  # Trả về Pytorch tensors
    padding=True          # Thêm padding để các câu có độ dài bằng nhau
)

# Thực hiện dịch
# model.generate() sẽ tạo ra các chuỗi token kết quả
print("\nBắt đầu dịch...")
output_tokens = model.generate(
    inputs["input_ids"], 
    attention_mask=inputs["attention_mask"],
    max_length=512, # Độ dài tối đa của câu dịch
    num_beams=5     # Sử dụng beam search để kết quả tốt hơn
)
print("Dịch xong!")

# Giải mã kết quả từ token về lại văn bản
translated_texts = tokenizer.batch_decode(output_tokens, skip_special_tokens=True)

# In kết quả
print("\n--- KẾT QUẢ DỊCH ---")
for original, translated in zip(sentences_to_translate, translated_texts):
    print(f"Câu gốc: {original}")
    print(f"Bản dịch: {translated}\n")