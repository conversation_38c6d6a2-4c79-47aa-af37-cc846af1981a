<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> đơn hàng shopee đ<PERSON> giao hàng,"
            You are an expert in Odoo 18 and PostgreSQL databases.
            Your task is to translate a natural language question and business concepts into a valid SQL query that can be executed within an Odoo 18 environment.

            ### System context:
            - Odoo version: 18
            - Database: PostgreSQL
            - Table names usually start with prefixes like `res_`, `hr_`, `account_`, `sale_`, `stock_`, `purchase_`, `mrp_`, `product_`, `crm_`, `project_`, `report_`, `ecommerce_`, etc.
            - Always follow standard PostgreSQL SQL syntax.
            - Avoid using any data or columns not defined in the provided DDL.
            - For columns of textual types (char, text, selection, etc.):
                + Normalize both sides by removing diacritics (accent removal),
                + Convert to lowercase,
                + And use the ILIKE operator for comparison instead of =.
            - When table has column state use it to filter data.
            - When table has column active use it to filter data.

            ### Database schema (DDL of relevant tables):
            -- E-commerce Statement model stores financial statement data synchronized from various e-commerce platforms such as Shopee, Lazada, and TikTok Shop, Tiki. Each record represents a summarized transaction report or settlement period, including details like total revenue, commissions, fees, discounts, and payout amounts. |
CREATE TABLE ecommerce_statement (
    name VARCHAR,
    company_id BIGINT,
    -- company_id is a foreign key to res_company.id,
    currency_id BIGINT,
    -- currency_id is a foreign key to res_currency.id,
    utm_medium_id BIGINT,
    -- utm_medium_id is a foreign key to utm_medium.id,
    ecommerce_order_id BIGINT,
    -- ecommerce_order_id is a foreign key to ecommerce_order.id,
    ecommerce_shop_id BIGINT,
    -- ecommerce_shop_id is a foreign key to ecommerce_shop.id,
    order_sn VARCHAR,
    buyer_user_name VARCHAR,
    return_order_sn_list VARCHAR,
    voucher_from_platform DOUBLE,
    display_name VARCHAR,
    escrow_amount DOUBLE,
    commission_fee DOUBLE,
    service_fee DOUBLE,
    platform_discount DOUBLE,
    voucher_from_seller DOUBLE,
    coins DOUBLE,
    total_seller_discount_amount DOUBLE,
    total_seller_shipping_discount_amount DOUBLE,
    instalment_plan VARCHAR,
    buyer_payment_method VARCHAR,
    seller_voucher_code VARCHAR,
    tax_registration_code VARCHAR,
    rsf_seller_prot_claim_amt DOUBLE,
    fsf_seller_prot_claim_amt DOUBLE,
    seller_lost_comp DOUBLE,
    drc_adj_refund DOUBLE,
    seller_return_refund DOUBLE,
    order_ams_comm_fee DOUBLE,
    trade_in_bonus_seller DOUBLE,
    original_price DOUBLE,
    cogs DOUBLE,
    orig_cogs DOUBLE,
    order_dsc_price DOUBLE,
    order_orig_price DOUBLE,
    order_seller_dsc DOUBLE,
    order_selling_price DOUBLE,
    seller_txn_fee DOUBLE,
    credit_card_txn_fee DOUBLE,
    campaign_fee DOUBLE,
    seller_order_proc_fee DOUBLE,
    seller_discount DOUBLE,
    seller_coin_cash_back DOUBLE,
    payment_promotion DOUBLE,
    credit_card_promotion DOUBLE,
    buyer_paid_ship_fee DOUBLE,
    actual_ship_fee DOUBLE,
    platform_ship_rebate DOUBLE,
    seller_ship_dsc DOUBLE,
    reverse_ship_fee DOUBLE,
    estimated_ship_fee DOUBLE,
    ship_fee_dsc_from_3pl DOUBLE,
    final_return_seller_ship_fee DOUBLE,
    overseas_return_service_fee DOUBLE,
    buyer_paid_packaging_fee DOUBLE,
    cross_border_tax DOUBLE,
    escrow_tax DOUBLE,
    final_escrow_prod_gst DOUBLE,
    final_escrow_ship_gst DOUBLE,
    final_prod_vat_tax DOUBLE,
    final_ship_vat_tax DOUBLE,
    sales_tax_on_lvg DOUBLE,
    ship_fee_sst DOUBLE,
    reverse_ship_fee_sst DOUBLE,
    vat_on_imported_goods DOUBLE,
    withholding_tax DOUBLE,
    withholding_vat_tax DOUBLE,
    withholding_pit_tax DOUBLE,
    delivery_seller_prot_fee_prem_amt DOUBLE,
    ship_seller_prot_fee_amt DOUBLE,
    total_seller_fee DOUBLE,
    total_seller_tax_amount DOUBLE,
    other_fees DOUBLE
);

-- Contact|Liên hệ
CREATE TABLE res_partner (
    loyalty_card_count BIGINT,
    partner_gid BIGINT,
    on_time_rate DOUBLE,
    property_delivery_carrier_id BIGINT,
    property_account_position_id BIGINT,
    city VARCHAR,
    country_id BIGINT,
    -- country_id is a foreign key to res_country.id,
    email VARCHAR,
    type VARCHAR,
    -- type has values: contact is Contact|Liên hệ, invoice is Invoice Address|Địa chỉ xuất hoá đơn, delivery is Delivery Address|Địa chỉ giao hàng, other is Other Address|Địa chỉ khác,
    partner_latitude DOUBLE,
    partner_longitude DOUBLE,
    is_public BOOLEAN,
    industry_id BIGINT,
    -- industry_id is a foreign key to res_partner_industry.id,
    company_type VARCHAR,
    -- company_type has values: person is Individual|Cá nhân, company is Company|Công ty,
    company_id BIGINT,
    -- company_id is a foreign key to res_company.id,
    purchase_order_count BIGINT,
    supplier_invoice_count BIGINT,
    buyer_id BIGINT,
    -- buyer_id is a foreign key to res_users.id,
    purchase_warn_msg VARCHAR,
    sale_warn VARCHAR,
    -- sale_warn has values: warning is Warning|Cảnh báo, block is Blocking Message|Chặn tin nhắn, no-message is No Message|Không có tin nhắn,
    same_vat_partner_id BIGINT,
    same_company_registry_partner_id BIGINT,
    website VARCHAR,
    street VARCHAR,
    street2 VARCHAR,
    zip VARCHAR,
    state_id BIGINT,
    -- state_id is a foreign key to res_country_state.id,
    mobile VARCHAR,
    payment_amount_due DOUBLE,
    payment_amount_overdue DOUBLE,
    payment_earliest_due_date DATE,
    latest_followup_sequence BIGINT,
    latest_followup_date DATE,
    payment_responsible_id BIGINT,
    -- payment_responsible_id is a foreign key to res_users.id,
    payment_note VARCHAR,
    latest_followup_level_id BIGINT,
    latest_followup_level_id_without_lit BIGINT,
    payment_next_action VARCHAR,
    is_coa_installed BOOLEAN,
    payment_next_action_date DATE,
    employees_count BIGINT,
    specific_property_product_pricelist BIGINT,
    message_is_follower BOOLEAN,
    message_attachment_count BIGINT,
    vendor_code VARCHAR,
    payment_token_count BIGINT,
    fiscal_country_codes VARCHAR,
    credit_to_invoice DOUBLE,
    credit_limit DOUBLE,
    days_sales_outstanding DOUBLE,
    currency_id BIGINT,
    journal_item_count BIGINT,
    property_account_payable_id BIGINT,
    property_account_receivable_id BIGINT,
    property_payment_term_id BIGINT,
    bank_account_count BIGINT,
    trust VARCHAR,
    -- trust has values: good is Good Debtor|Nợ tốt, normal is Normal Debtor|Nợ thông thường, bad is Bad Debtor|Đối tượng nợ xấu,
    ignore_abnormal_invoice_date BOOLEAN,
    ignore_abnormal_invoice_amount BOOLEAN,
    invoice_warn VARCHAR,
    -- invoice_warn has values: no-message is No Message|Không có tin nhắn, block is Blocking Message|Chặn tin nhắn, warning is Warning|Cảnh báo,
    invoice_sending_method VARCHAR,
    -- invoice_sending_method has values: manual is Download|Tải xuống, email is by Email|bằng email, snailmail is by Post|bằng đường bưu điện,
    picking_warn_msg VARCHAR,
    invoice_edi_format_store VARCHAR,
    display_invoice_edi_format BOOLEAN,
    additional_info VARCHAR,
    invoice_template_pdf_report_id BIGINT,
    -- invoice_template_pdf_report_id is a foreign key to ir_act_report_xml.id,
    display_invoice_template_pdf_report_id BOOLEAN,
    autopost_bills VARCHAR,
    -- autopost_bills has values: always is Always|Luôn luôn, ask is Ask after 3 validations without edits|Hỏi sau 3 lần xác thực mà không có chỉnh sửa, never is Never|Không bao giờ,
    duplicated_bank_account_partners_count BIGINT,
    default_consume BOOLEAN,
    company_registry VARCHAR,
    complete_name VARCHAR,
    active_lang_count BIGINT,
    active BOOLEAN,
    color BIGINT,
    company_name VARCHAR,
    contact_address VARCHAR,
    commercial_partner_id BIGINT,
    -- commercial_partner_id is a foreign key to res_partner.id,
    commercial_company_name VARCHAR,
    display_name VARCHAR,
    activity_date_deadline DATE,
    activity_exception_decoration VARCHAR,
    -- activity_exception_decoration has values: warning is Alert|Cảnh báo, danger is Error|Lỗi,
    activity_exception_icon VARCHAR,
    self BIGINT,
    parent_name VARCHAR,
    phone VARCHAR,
    title BIGINT,
    -- title is a foreign key to res_partner_title.id,
    partner_vat_placeholder VARCHAR,
    credit DOUBLE,
    use_partner_credit_limit BOOLEAN,
    show_credit_limit BOOLEAN,
    debit DOUBLE,
    debit_limit DOUBLE,
    total_invoiced DOUBLE,
    mobile_blacklisted BOOLEAN,
    phone_blacklisted BOOLEAN,
    ref VARCHAR,
    tz_offset VARCHAR,
    picking_warn VARCHAR,
    -- picking_warn has values: no-message is No Message|Không có tin nhắn, warning is Warning|Cảnh báo, block is Blocking Message|Chặn tin nhắn,
    customer_rank BIGINT,
    has_message BOOLEAN,
    signup_type VARCHAR,
    phone_mobile_search VARCHAR,
    property_stock_customer BIGINT,
    property_stock_supplier BIGINT,
    is_peppol_edi_format BOOLEAN,
    is_ubl_format BOOLEAN,
    invoice_edi_format VARCHAR,
    -- invoice_edi_format has values: ubl_bis3 is BIS Billing 3.0|BIS Billing 3.0, ubl_a_nz is BIS Billing 3.0 A-NZ|BIS Billing 3.0 A-NZ, ubl_sg is BIS Billing 3.0 SG|BIS Billing 3.0 SG, facturx is Factur-X (CII)|Factur-X (CII), nlcius is NLCIUS|NLCIUS, xrechnung is XRechnung CIUS|XRechnung CIUS,
    peppol_endpoint VARCHAR,
    lang VARCHAR,
    employee BOOLEAN,
    country_code VARCHAR,
    email_formatted VARCHAR,
    is_company BOOLEAN,
    partner_share BOOLEAN,
    barcode VARCHAR,
    sale_order_count BIGINT,
    sale_warn_msg VARCHAR,
    im_status VARCHAR,
    function VARCHAR,
    user_id BIGINT,
    -- user_id is a foreign key to res_users.id,
    property_supplier_payment_term_id BIGINT,
    invoice_warn_msg VARCHAR,
    supplier_rank BIGINT,
    property_outbound_payment_method_line_id BIGINT,
    property_inbound_payment_method_line_id BIGINT,
    parent_id BIGINT,
    -- parent_id is a foreign key to res_partner.id,
    email_normalized VARCHAR,
    is_blacklisted BOOLEAN,
    message_bounce BIGINT,
    activity_state VARCHAR,
    -- activity_state has values: overdue is Overdue|Quá hạn, today is Today|Hôm nay, planned is Planned|Đã lên kế hoạch,
    activity_user_id BIGINT,
    activity_type_id BIGINT,
    activity_type_icon VARCHAR,
    my_activity_date_deadline DATE,
    activity_summary VARCHAR,
    contact_address_inline VARCHAR,
    property_product_pricelist BIGINT,
    message_needaction BOOLEAN,
    phone_sanitized VARCHAR,
    message_has_error BOOLEAN,
    message_has_sms_error BOOLEAN,
    phone_sanitized_blacklisted BOOLEAN,
    message_needaction_counter BIGINT,
    message_has_error_counter BIGINT,
    peppol_eas VARCHAR,
    -- peppol_eas has values: 0130 is 0130 - Directorates of the European Commission|0130 - Đoàn uỷ viên của Ủy ban Châu Âu, 0142 is 0142 - SECETI Object Identifiers|0142 - Mã định danh đối tượng SECETI, 0151 is 0151 - Australian Business Number (ABN) Scheme|0151 - Cơ chế mã số doanh nghiệp Úc (ABN), 0183 is 0183 - Swiss Unique Business Identification Number (UIDB)|0183 - Mã số doanh nghiệp duy nhất của Thụy Sĩ (UIDB), 0188 is 0188 - Corporate Number of The Social Security and Tax Number System|0188 - Mã số doanh nghiệp của Hệ thống mã số thuế và an sinh xã hội, 0190 is 0190 - Dutch Originator's Identification Number|0190 - Mã số tổ chức của Hà Lan, 0191 is 0191 - Centre of Registers and Information Systems of the Ministry of Justice|0191 - Trung tâm Đăng ký và Hệ thống Thông tin Bộ Tư pháp, 0192 is 0192 - Enhetsregisteret ved Bronnoysundregisterne|0192 - Enhetsregisteret ved Bronnoysundregisterne, 0193 is 0193 - UBL.BE party identifier|0193 - Mã số tổ chức UBL.BE, 0195 is 0195 - Singapore UEN identifier|0195 - Mã số UEN của Singapore, 0196 is 0196 - Kennitala - Iceland legal id for individuals and legal entities|0196 - Kennitala - ID pháp lý cho cá nhân và pháp nhân của Iceland, 0198 is 0198 - ERSTORG|0198 - ERSTORG, 0199 is 0199 - Legal Entity Identifier (LEI)|0199 - Mã phân định pháp nhân (LEI), 0201 is 0201 - Codice Univoco Unità Organizzativa iPA|0201 - Codice Univoco Unità Organizzativa iPA, 9919 is 9919 - Kennziffer des Unternehmensregisters|9919 - Kennziffer des Unternehmensregisters, 9920 is 9920 - Agencia Española de Administración Tributaria|9920 - Agencia Española de Administración Tributaria, 9922 is 9922 - Andorra VAT number|9922 - Mã số thuế GTGT của Andorra, 0202 is 0202 - Indirizzo di Posta Elettronica Certificata|0202 - Indirizzo di Posta Elettronica Certificata, 0204 is 0204 - Leitweg-ID|0204 - Leitweg-ID, 0208 is 0208 - Numero d'entreprise / ondernemingsnummer / Unternehmensnummer|0208 - Numero d'entreprise / ondernemingsnummer / Unternehmensnummer, 9926 is 9926 - Bulgaria VAT number|9926 - Mã số thuế GTGT của Bulgaria, 9923 is 9923 - Albania VAT number|9923 - Mã số thuế GTGT của Albania, 0200 is 0200 - Legal entity code (Lithuania)|0200 - Mã định danh pháp nhân (Lithuania), 9924 is 9924 - Bosnia and Herzegovina VAT number|9924 - Mã số thuế GTGT của Bosnia và Herzegovina, 9933 is 9933 - Greece VAT number|9933 - Mã số thuế GTGT của Hy Lạp, 9934 is 9934 - Croatia VAT number|9934 - Mã số thuế GTGT của Croatia, 9935 is 9935 - Ireland VAT number|9935 - Mã số thuế GTGT của Ireland, 9910 is 9910 - Hungary VAT number|9910 - Mã số thuế GTGT của Hungary, 9913 is 9913 - Business Registers Network|9913 - Mạng lưới đăng ký kinh doanh, 9914 is 9914 - Österreichische Umsatzsteuer-Identifikationsnummer|9914 - Österreichische Umsatzsteuer-Identifikationsnummer, 9915 is 9915 - Österreichisches Verwaltungs bzw. Organisationskennzeichen|9915 - Österreichisches Verwaltungs bzw. Organisationskennzeichen, 9918 is 9918 - SOCIETY FOR WORLDWIDE INTERBANK FINANCIAL, TELECOMMUNICATION S.W.I.F.T|9918 - HIỆP HỘI VIỄN THÔNG TÀI CHÍNH LIÊN NGÂN HÀNG TOÀN CẦU S.W.I.F.T, 0096 is 0096 - DANISH CHAMBER OF COMMERCE Scheme (EDIRA compliant)|0096 - Cơ chế PHÒNG THƯƠNG MẠI ĐAN MẠCH (tuân thủ EDIRA), 0097 is 0097 - FTI - Ediforum Italia, (EDIRA compliant)|0097 - FTI - Ediforum Ý, (tuân thủ EDIRA), 0002 is 0002 - System Information et Repertoire des Entreprise et des Etablissements: SIRENE|0002 - System Information et Repertoire des Entreprise et des Etablissements: SIRENE, 0007 is 0007 - Organisationsnummer (Swedish legal entities)|0007 - Mã số tổ chức (Pháp nhân Thụy Điển), 0009 is 0009 - SIRET-CODE|0009 - MÃ-SIRET, 0060 is 0060 - Data Universal Numbering System (D-U-N-S Number)|0060 - Hệ thống mã số dữ liệu toàn cầu (Số D-U-N-S), 0088 is 0088 - EAN Location Code|0088 - Mã số vị trí EAN, 0106 is 0106 - Association of Chambers of Commerce and Industry in the Netherlands, (EDIRA compliant)|0106 - Hiệp hội Phòng Thương mại và Công nghiệp Hà Lan, (tuân thủ EDIRA), 0135 is 0135 - SIA Object Identifiers|0135 - Mã định danh đối tượng SIA, 9930 is 9930 - Germany VAT number|9930 - Mã số thuế GTGT của Đức, 9931 is 9931 - Estonia VAT number|9931 - Mã số thuế GTGT của Estonia, 9932 is 9932 - United Kingdom VAT number|9932 - Mã số thuế GTGT của Vương quốc Anh, 9936 is 9936 - Liechtenstein VAT number|9936 - Mã số thuế GTGT của Liechtenstein, 0209 is 0209 - GS1 identification keys|0209 - khoá định danh GS1, 0210 is 0210 - CODICE FISCALE|0210 - CODICE FISCALE, 0211 is 0211 - PARTITA IVA|0211 - PARTITA IVA, 0216 is 0216 - OVTcode|0216 - mã OVT, 0221 is 0221 - The registered number of the qualified invoice issuer (Japan)|0221 - Số đăng ký của tổ chức phát hành hóa đơn đủ điều kiện (Nhật Bản), 0230 is 0230 - National e-Invoicing Framework (Malaysia)|0230 - Khung hóa đơn điện tử quốc gia (Malaysia), 9925 is 9925 - Belgium VAT number|9925 - Mã số thuế GTGT của Bỉ, 9927 is 9927 - Switzerland VAT number|9927 - Mã số thuế GTGT của Thuỵ Sĩ, 9928 is 9928 - Cyprus VAT number|9928 - Mã số thuế GTGT của Síp, 9929 is 9929 - Czech Republic VAT number|9929 - Mã số thuế GTGT của Cộng hoà Séc, 9941 is 9941 - Montenegro VAT number|9941 - Mã số thuế GTGT của Montenegro, 9942 is 9942 - Macedonia, the former Yugoslav Republic of VAT number|9942 - Mã số thuế GTGT của Cộng hoà Macedonia thuộc Nam Tư cũ, 9943 is 9943 - Malta VAT number|9943 - Mã số thuế GTGT của Malta, 9944 is 9944 - Netherlands VAT number|9944 - Mã số thuế GTGT của Hà Lan, 9945 is 9945 - Poland VAT number|9945 - Mã số thuế GTGT của Ba Lan, 9946 is 9946 - Portugal VAT number|9946 - Mã số thuế GTGT của Bồ Đào Nha, 9947 is 9947 - Romania VAT number|9947 - Mã số thuế GTGT của Romania, 9948 is 9948 - Serbia VAT number|9948 - Mã số thuế GTGT của Serbia, 9949 is 9949 - Slovenia VAT number|9949 - Mã số thuế GTGT của Slovenia, 9950 is 9950 - Slovakia VAT number|9950 - Mã số thuế GTGT của Slovakia, 9937 is 9937 - Lithuania VAT number|9937 - Mã số thuế GTGT của Lithuania, 9938 is 9938 - Luxemburg VAT number|9938 - Mã số thuế GTGT của Luxemburg, 9939 is 9939 - Latvia VAT number|9939 - Mã số thuế GTGT của Latvia, 9940 is 9940 - Monaco VAT number|9940 - Mã số thuế GTGT của Monaco, 9951 is 9951 - San Marino VAT number|9951 - Mã số thuế GTGT của San Marino, 9952 is 9952 - Türkiye VAT number|9952 - Mã số thuế GTGT của Thổ Nhĩ Kỳ, 9953 is 9953 - Holy See (Vatican City State) VAT number|9953 - Mã số thuế GTGT của Holy See (Thành quốc Vatican), 9957 is 9957 - French VAT number|9957 - Mã số thuế GTGT của Pháp, 9959 is 9959 - Employer Identification Number (EIN, USA)|9959 - Mã số thuế của chủ lao động (EIN, Hoa Kỳ), 0184 is 0184 - DIGSTORG|0184 - DIGSTORG,
    receipt_reminder_email BOOLEAN,
    reminder_date_before_receipt BIGINT,
    purchase_warn VARCHAR,
    -- purchase_warn has values: no-message is No Message|Không có tin nhắn, warning is Warning|Cảnh báo, block is Blocking Message|Chặn tin nhắn,
    property_purchase_currency_id BIGINT,
    is_published BOOLEAN,
    website_published BOOLEAN,
    can_publish BOOLEAN,
    website_id BIGINT,
    -- website_id is a foreign key to website.id,
    website_url VARCHAR,
    vat VARCHAR,
    name VARCHAR,
    last_website_so_id BIGINT
);

-- Ecommerce Order Line model stores detailed item-level information for each order from various e-commerce platforms such as Shopee, Lazada, Tiki and TikTok Shop. Each record represents a single product line belonging to an ecommerce.order. Typical fields include product details, quantity, unit price, subtotal, and associated platform (via the related order's utm_name).|Dòng đơn hàng TMĐT
CREATE TABLE ecommerce_order_line (
    order_item_id VARCHAR,
    weight DOUBLE,
    combo_mapping_name VARCHAR,
    ecommerce_order_id BIGINT,
    -- ecommerce_order_id is a foreign key to ecommerce_order.id,
    utm_medium_id BIGINT,
    -- utm_medium_id is a foreign key to utm_medium.id,
    company_id BIGINT,
    currency_id BIGINT,
    item_id VARCHAR,
    sku VARCHAR,
    product_name VARCHAR,
    quantity BIGINT,
    price DOUBLE,
    original_price DOUBLE,
    voucher_amount DOUBLE,
    platform_discount DOUBLE,
    seller_discount DOUBLE,
    product_image VARCHAR,
    confirm_status VARCHAR,
    confirm_at TIMESTAMP,
    model_id VARCHAR,
    model_name VARCHAR,
    wh_partner_code VARCHAR,
    ref_virtual_id VARCHAR,
    display_name VARCHAR,
    ecommerce_product_product_id BIGINT,
    -- ecommerce_product_product_id is a foreign key to ecommerce_product_product.id,
    has_ec_product BOOLEAN
);

-- Ecommerce Order model stores all order data collected from multiple e-commerce platforms such as Shopee, Lazada, Tiki, TikTok Shop, etc. Each record represents a unique order synchronized from these marketplaces. The field 'utm_name' identifies the source platform (e.g., 'Shopee', 'Lazada', Tiki, 'TikTok').|Đơn hàng TMĐT
CREATE TABLE ecommerce_order (
    delivery_partner_id BIGINT,
    -- delivery_partner_id is a foreign key to delivery_carrier.id,
    ec_warehouse_id BIGINT,
    -- ec_warehouse_id is a foreign key to ecommerce_warehouse_mapping.id,
    utm_medium_id BIGINT,
    -- utm_medium_id is a foreign key to utm_medium.id,
    utm_medium_name VARCHAR,
    order_code VARCHAR,
    sale_order_id BIGINT,
    -- sale_order_id is a foreign key to sale_order.id,
    company_id BIGINT,
    -- company_id is a foreign key to res_company.id,
    currency_id BIGINT,
    -- currency_id is a foreign key to res_currency.id,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    shipping_name VARCHAR,
    shipping_phone VARCHAR,
    shipping_town VARCHAR,
    payment_status VARCHAR,
    total_amount DOUBLE,
    voucher_amount DOUBLE,
    payment_time TIMESTAMP,
    note VARCHAR,
    is_virtual BOOLEAN,
    status_histories VARCHAR,
    is_cod BOOLEAN,
    has_printed BOOLEAN,
    has_product_not_linked BOOLEAN,
    activity_state VARCHAR,
    -- activity_state has values: overdue is Overdue|, today is Today|, planned is Planned|,
    activity_user_id BIGINT,
    activity_type_id BIGINT,
    activity_type_icon VARCHAR,
    activity_date_deadline DATE,
    packed_date TIMESTAMP,
    draft_date TIMESTAMP,
    waiting_date TIMESTAMP,
    shipped_date TIMESTAMP,
    done_date TIMESTAMP,
    ref_order_number VARCHAR,
    checkout_shipping_carrier VARCHAR,
    -- checkout_shipping_carrier has values: standard is Standard Delivery|, fast is Fast Delivery|,
    res_partner_id BIGINT,
    -- res_partner_id is a foreign key to res_partner.id,
    res_partner_invoice_id BIGINT,
    -- res_partner_invoice_id is a foreign key to res_partner.id,
    res_partner_shipping_id BIGINT,
    -- res_partner_shipping_id is a foreign key to res_partner.id,
    customer_name VARCHAR,
    customer_phone VARCHAR,
    customer_email VARCHAR,
    shipping_district VARCHAR,
    shipping_state VARCHAR,
    shipping_address_full VARCHAR,
    shipping_city VARCHAR,
    shipping_region VARCHAR,
    shipping_zipcode VARCHAR,
    shipping_ward VARCHAR,
    shipping_carrier VARCHAR,
    tracking_code VARCHAR,
    shipping_status VARCHAR,
    shipping_fee DOUBLE,
    shipping_fee_discount DOUBLE,
    promised_delivery_date TIMESTAMP,
    warehouse_code VARCHAR,
    warehouse_name VARCHAR,
    warehouse_id BIGINT,
    -- warehouse_id is a foreign key to stock_warehouse.id,
    payment_method VARCHAR,
    locked BOOLEAN,
    order_type VARCHAR,
    -- order_type has values: cancel is CANCEL|HỦY, normal is NORMAL|Đơn bán, refund is REFUND|HOÀN TIỀN, return is RETURN|Đơn trả,
    reason VARCHAR,
    text_reason VARCHAR,
    return_order_code VARCHAR,
    refund_status VARCHAR,
    images VARCHAR,
    videos VARCHAR,
    sale_order_count BIGINT,
    ecommerce_shop_id BIGINT,
    -- ecommerce_shop_id is a foreign key to ecommerce_shop.id,
    display_name VARCHAR,
    order_status VARCHAR,
    -- order_status has values: UNPAID is UNPAID|, READY_TO_SHIP is READY_TO_SHIP|, PROCESSED is PROCESSED|, RETRY_SHIP is RETRY_SHIP|, SHIPPED is SHIPPED|, TO_CONFIRM_RECEIVE is TO_CONFIRM_RECEIVE|, IN_CANCEL is IN_CANCEL|, CANCELLED is CANCELLED|, TO_RETURN is TO_RETURN|, COMPLETED is COMPLETED|, UNKNOWN is UNKNOWN|, ACCEPTED is ACCEPTED|, REQUESTED is REQUESTED|, PROCESSING is PROCESSING|, JUDGING is JUDGING|, SELLER_DISPUTE is SELLER_DISPUTE|, CLOSED is CLOSED|,
    my_activity_date_deadline DATE,
    activity_summary VARCHAR,
    activity_exception_decoration VARCHAR,
    -- activity_exception_decoration has values: warning is Alert|, danger is Error|,
    activity_exception_icon VARCHAR,
    message_is_follower BOOLEAN,
    order_status_vn VARCHAR,
    has_message BOOLEAN,
    message_needaction BOOLEAN,
    message_needaction_counter BIGINT,
    message_has_error BOOLEAN,
    message_has_error_counter BIGINT,
    message_attachment_count BIGINT,
    message_has_sms_error BOOLEAN,
    state VARCHAR,
    -- state has values: draft is Draft|Đơn bán hàng, waiting is Waiting|Đang chờ, done is Done|Hoàn tất, cancelled is Cancelled|Đã hủy, packed is Packed|Đóng gói, shipped is Shipped|Giao hàng,
    packing_date TIMESTAMP,
    state_history_count BIGINT
);

-- Analytic Account|Tài khoản phân tích
CREATE TABLE account_analytic_account (
    production_count BIGINT,
    credit DOUBLE,
    has_message BOOLEAN,
    message_is_follower BOOLEAN,
    purchase_order_count BIGINT,
    plan_id BIGINT,
    -- plan_id is a foreign key to account_analytic_plan.id,
    currency_id BIGINT,
    message_attachment_count BIGINT,
    color BIGINT,
    debit DOUBLE,
    message_needaction_counter BIGINT,
    name VARCHAR,
    vendor_bill_count BIGINT,
    workorder_count BIGINT,
    bom_count BIGINT,
    invoice_count BIGINT,
    partner_id BIGINT,
    -- partner_id is a foreign key to res_partner.id,
    code VARCHAR,
    root_plan_id BIGINT,
    -- root_plan_id is a foreign key to account_analytic_plan.id,
    active BOOLEAN,
    message_needaction BOOLEAN,
    message_has_error BOOLEAN,
    message_has_error_counter BIGINT,
    balance DOUBLE,
    company_id BIGINT,
    -- company_id is a foreign key to res_company.id,
    display_name VARCHAR,
    message_has_sms_error BOOLEAN
);

-- Bank Statement Line|Dòng sao kê ngân hàng
CREATE TABLE account_bank_statement_line (
    wip_production_count BIGINT,
    stock_move_id BIGINT,
    sequence BIGINT,
    medium_id BIGINT,
    l10n_vn_e_invoice_number VARCHAR,
    company_price_include VARCHAR,
    sequence_number BIGINT,
    always_tax_exigible BOOLEAN,
    campaign_id BIGINT,
    source_id BIGINT,
    invoice_payment_term_id BIGINT,
    user_id BIGINT,
    statement_complete BOOLEAN,
    account_number VARCHAR,
    inalterable_hash VARCHAR,
    name VARCHAR,
    move_sent_values VARCHAR,
    purchase_order_name VARCHAR,
    move_id BIGINT,
    -- move_id is a foreign key to account_move.id,
    journal_id BIGINT,
    -- journal_id is a foreign key to account_journal.id,
    company_id BIGINT,
    -- company_id is a foreign key to res_company.id,
    payment_ref VARCHAR,
    currency_id BIGINT,
    -- currency_id is a foreign key to res_currency.id,
    amount DOUBLE,
    foreign_currency_id BIGINT,
    -- foreign_currency_id is a foreign key to res_currency.id,
    amount_currency DOUBLE,
    country_code VARCHAR,
    internal_index VARCHAR,
    is_reconciled BOOLEAN,
    statement_valid BOOLEAN,
    activity_state VARCHAR,
    activity_type_icon VARCHAR,
    activity_exception_decoration VARCHAR,
    activity_exception_icon VARCHAR,
    message_is_follower BOOLEAN,
    has_message BOOLEAN,
    message_needaction BOOLEAN,
    access_warning VARCHAR,
    date DATE,
    is_storno BOOLEAN,
    tax_cash_basis_origin_move_id BIGINT,
    auto_post VARCHAR,
    auto_post_until DATE,
    auto_post_origin_id BIGINT,
    hide_post_button BOOLEAN,
    checked BOOLEAN,
    highest_name VARCHAR,
    secure_sequence_number BIGINT,
    invoice_date DATE,
    commercial_partner_id BIGINT,
    partner_shipping_id BIGINT,
    fiscal_position_id BIGINT,
    invoice_has_outstanding BOOLEAN,
    company_currency_id BIGINT,
    invoice_currency_rate DOUBLE,
    direction_sign BIGINT,
    amount_residual_signed DOUBLE,
    amount_total_words VARCHAR,
    invoice_partner_display_name VARCHAR,
    is_manually_modified BOOLEAN,
    is_move_sent BOOLEAN,
    is_being_sent BOOLEAN,
    invoice_incoterm_id BIGINT,
    incoterm_location VARCHAR,
    invoice_cash_rounding_id BIGINT,
    invoice_filter_type_domain VARCHAR,
    bank_partner_id BIGINT,
    has_reconciled_entries BOOLEAN,
    show_update_fpos BOOLEAN,
    abnormal_amount_warning VARCHAR,
    abnormal_date_warning VARCHAR,
    display_qr_code BOOLEAN,
    display_inactive_currency_warning BOOLEAN,
    statement_id BIGINT,
    -- statement_id is a foreign key to account_bank_statement.id,
    partner_id BIGINT,
    -- partner_id is a foreign key to res_partner.id,
    partner_name VARCHAR,
    transaction_type VARCHAR,
    running_balance DOUBLE,
    amount_residual DOUBLE,
    statement_name VARCHAR,
    sequence_prefix VARCHAR,
    activity_user_id BIGINT,
    activity_type_id BIGINT,
    activity_date_deadline DATE,
    my_activity_date_deadline DATE,
    activity_summary VARCHAR,
    message_needaction_counter BIGINT,
    message_has_error BOOLEAN,
    message_has_error_counter BIGINT,
    message_has_sms_error BOOLEAN,
    message_main_attachment_id BIGINT,
    access_url VARCHAR,
    ref VARCHAR,
    state VARCHAR,
    move_type VARCHAR,
    journal_group_id BIGINT,
    origin_payment_id BIGINT,
    payment_count BIGINT,
    statement_line_id BIGINT,
    tax_cash_basis_rec_id BIGINT,
    posted_before BOOLEAN,
    made_sequence_gap BOOLEAN,
    show_name_warning BOOLEAN,
    type_name VARCHAR,
    restrict_mode_hash_table BOOLEAN,
    secured BOOLEAN,
    show_delivery_date BOOLEAN,
    needed_terms_dirty BOOLEAN,
    tax_calculation_rounding_method VARCHAR,
    partner_bank_id BIGINT,
    payment_reference VARCHAR,
    qr_code_method VARCHAR,
    preferred_payment_method_line_id BIGINT,
    amount_untaxed DOUBLE,
    amount_tax DOUBLE,
    amount_total DOUBLE,
    amount_untaxed_signed DOUBLE,
    amount_untaxed_in_currency_signed DOUBLE,
    amount_tax_signed DOUBLE,
    amount_total_signed DOUBLE,
    amount_total_in_currency_signed DOUBLE,
    payment_state VARCHAR,
    status_in_payment VARCHAR,
    reversed_entry_id BIGINT,
    invoice_vendor_bill_id BIGINT,
    invoice_source_email VARCHAR,
    quick_edit_mode BOOLEAN,
    quick_edit_total_amount DOUBLE,
    invoice_user_id BIGINT,
    invoice_origin VARCHAR,
    invoice_pdf_report_id BIGINT,
    tax_lock_date_message VARCHAR,
    tax_country_id BIGINT,
    tax_country_code VARCHAR,
    show_reset_to_draft_button BOOLEAN,
    partner_credit_warning VARCHAR,
    partner_credit DOUBLE,
    need_cancel_request BOOLEAN,
    show_payment_term_details BOOLEAN,
    show_discount_details BOOLEAN,
    next_payment_date DATE,
    access_token VARCHAR,
    ubl_cii_xml_id BIGINT,
    transaction_count BIGINT,
    amount_paid DOUBLE,
    is_purchase_matched BOOLEAN,
    purchase_order_count BIGINT,
    team_id BIGINT,
    sale_order_count BIGINT,
    statement_balance_end_real DOUBLE,
    display_name VARCHAR,
    invoice_date_due DATE,
    delivery_date DATE,
    message_attachment_count BIGINT,
    purchase_vendor_bill_id BIGINT,
    purchase_id BIGINT,
    website_id BIGINT
);

-- Lot/Serial|Lô/sê-ri
CREATE TABLE stock_lot (
    avg_cost DOUBLE,
    total_value DOUBLE,
    standard_price DOUBLE,
    product_expiry_reminded BOOLEAN,
    value_svl DOUBLE,
    purchase_order_count BIGINT,
    product_id BIGINT,
    -- product_id is a foreign key to product_product.id,
    message_is_follower BOOLEAN,
    message_attachment_count BIGINT,
    company_currency_id BIGINT,
    quantity_svl DOUBLE,
    activity_exception_icon VARCHAR,
    name VARCHAR,
    activity_state VARCHAR,
    -- activity_state has values: overdue is Overdue|, today is Today|, planned is Planned|,
    activity_user_id BIGINT,
    activity_type_id BIGINT,
    activity_type_icon VARCHAR,
    activity_date_deadline DATE,
    my_activity_date_deadline DATE,
    activity_summary VARCHAR,
    activity_exception_decoration VARCHAR,
    -- activity_exception_decoration has values: warning is Alert|, danger is Error|,
    sale_order_count BIGINT,
    company_id BIGINT,
    -- company_id is a foreign key to res_company.id,
    has_message BOOLEAN,
    message_needaction BOOLEAN,
    message_needaction_counter BIGINT,
    message_has_error BOOLEAN,
    message_has_error_counter BIGINT,
    message_has_sms_error BOOLEAN,
    ref VARCHAR,
    product_uom_id BIGINT,
    -- product_uom_id is a foreign key to uom_uom.id,
    product_qty DOUBLE,
    display_complete BOOLEAN,
    delivery_count BIGINT,
    last_delivery_partner_id BIGINT,
    location_id BIGINT,
    -- location_id is a foreign key to stock_location.id,
    display_name VARCHAR,
    alert_date TIMESTAMP,
    product_expiry_alert BOOLEAN,
    expiration_date TIMESTAMP,
    removal_date TIMESTAMP,
    use_date TIMESTAMP,
    use_expiration_date BOOLEAN
);

-- User|Người dùng
CREATE TABLE res_users (
    loyalty_card_count BIGINT,
    dialog_size VARCHAR,
    -- dialog_size has values: minimize is Minimize|, maximize is Maximize|,
    sidebar_type VARCHAR,
    -- sidebar_type has values: invisible is Invisible|, small is Small|, large is Large|,
    chatter_position VARCHAR,
    -- chatter_position has values: side is Side|, bottom is Bottom|,
    employee_id BIGINT,
    job_title VARCHAR,
    work_phone VARCHAR,
    mobile_phone VARCHAR,
    work_email VARCHAR,
    department_id BIGINT,
    address_id BIGINT,
    work_contact_id BIGINT,
    work_location_id BIGINT,
    work_location_name VARCHAR,
    work_location_type VARCHAR,
    private_street VARCHAR,
    private_street2 VARCHAR,
    private_city VARCHAR,
    employee_country_id BIGINT,
    identification_id VARCHAR,
    children BIGINT,
    emergency_contact VARCHAR,
    emergency_phone VARCHAR,
    visa_no VARCHAR,
    permit_no VARCHAR,
    visa_expire DATE,
    additional_note VARCHAR,
    certificate VARCHAR,
    study_field VARCHAR,
    employee_count BIGINT,
    hr_presence_state VARCHAR,
    create_employee_id BIGINT,
    is_system BOOLEAN,
    last_lunch_location_id BIGINT,
    -- last_lunch_location_id is a foreign key to lunch_location.id,
    on_time_rate DOUBLE,
    specific_property_product_pricelist BIGINT,
    contextmenu_enabled BOOLEAN,
    private_state_id BIGINT,
    private_zip VARCHAR,
    private_country_id BIGINT,
    private_phone VARCHAR,
    employees_count BIGINT,
    sale_warn VARCHAR,
    property_delivery_carrier_id BIGINT,
    default_consume BOOLEAN,
    gender VARCHAR,
    phone_sanitized VARCHAR,
    phone_sanitized_blacklisted BOOLEAN,
    phone_blacklisted BOOLEAN,
    mobile_blacklisted BOOLEAN,
    phone_mobile_search VARCHAR,
    payment_amount_due DOUBLE,
    payment_amount_overdue DOUBLE,
    payment_earliest_due_date DATE,
    latest_followup_sequence BIGINT,
    active_partner BOOLEAN,
    companies_count BIGINT,
    tz_offset VARCHAR,
    latest_followup_date DATE,
    payment_responsible_id BIGINT,
    payment_note VARCHAR,
    latest_followup_level_id BIGINT,
    latest_followup_level_id_without_lit BIGINT,
    payment_next_action VARCHAR,
    payment_next_action_date DATE,
    totp_enabled BOOLEAN,
    partner_gid BIGINT,
    additional_info VARCHAR,
    is_coa_installed BOOLEAN,
    vendor_code VARCHAR,
    sale_warn_msg VARCHAR,
    property_stock_customer BIGINT,
    rules_count BIGINT,
    lang VARCHAR,
    login_date TIMESTAMP,
    partner_vat_placeholder VARCHAR,
    credit DOUBLE,
    use_partner_credit_limit BOOLEAN,
    contact_address_inline VARCHAR,
    active BOOLEAN,
    show_credit_limit BOOLEAN,
    display_name VARCHAR,
    email VARCHAR,
    complete_name VARCHAR,
    active_lang_count BIGINT,
    function VARCHAR,
    type VARCHAR,
    city VARCHAR,
    country_id BIGINT,
    partner_latitude DOUBLE,
    partner_longitude DOUBLE,
    is_public BOOLEAN,
    industry_id BIGINT,
    company_type VARCHAR,
    color BIGINT,
    contact_address VARCHAR,
    commercial_partner_id BIGINT,
    commercial_company_name VARCHAR,
    company_name VARCHAR,
    odoobot_state VARCHAR,
    -- odoobot_state has values: onboarding_command is Onboarding command|Onboarding Lệnh, onboarding_emoji is Onboarding emoji|Onboarding emoji, onboarding_ping is Onboarding ping|Onboarding ping, disabled is Disabled|Đã vô hiệu, idle is Idle|Rảnh rỗi, not_initialized is Not initialized|Không được khởi tạo, onboarding_attachement is Onboarding attachment|Onboarding Tệp đính kèm, onboarding_canned is Onboarding canned|Onboarding canned,
    odoobot_failed BOOLEAN,
    message_is_follower BOOLEAN,
    signup_type VARCHAR,
    debit DOUBLE,
    debit_limit DOUBLE,
    total_invoiced DOUBLE,
    invoice_sending_method VARCHAR,
    fiscal_country_codes VARCHAR,
    credit_to_invoice DOUBLE,
    credit_limit DOUBLE,
    days_sales_outstanding DOUBLE,
    currency_id BIGINT,
    journal_item_count BIGINT,
    property_account_payable_id BIGINT,
    property_account_receivable_id BIGINT,
    property_account_position_id BIGINT,
    property_payment_term_id BIGINT,
    bank_account_count BIGINT,
    trust VARCHAR,
    ignore_abnormal_invoice_date BOOLEAN,
    ignore_abnormal_invoice_amount BOOLEAN,
    invoice_warn VARCHAR,
    display_invoice_edi_format BOOLEAN,
    invoice_template_pdf_report_id BIGINT,
    display_invoice_template_pdf_report_id BOOLEAN,
    customer_rank BIGINT,
    autopost_bills VARCHAR,
    duplicated_bank_account_partners_count BIGINT,
    peppol_endpoint VARCHAR,
    property_supplier_payment_term_id BIGINT,
    invoice_warn_msg VARCHAR,
    activity_user_id BIGINT,
    supplier_rank BIGINT,
    tour_enabled BOOLEAN,
    invoice_edi_format VARCHAR,
    property_outbound_payment_method_line_id BIGINT,
    property_inbound_payment_method_line_id BIGINT,
    employee_parent_id BIGINT,
    private_email VARCHAR,
    private_lang VARCHAR,
    km_home_work BIGINT,
    distance_home_work BIGINT,
    distance_home_work_unit VARCHAR,
    passport_id VARCHAR,
    birthday DATE,
    place_of_birth VARCHAR,
    country_of_birth BIGINT,
    marital VARCHAR,
    spouse_complete_name VARCHAR,
    spouse_birthdate DATE,
    employee_type VARCHAR,
    employee_resource_calendar_id BIGINT,
    employee_bank_account_id BIGINT,
    barcode VARCHAR,
    pin VARCHAR,
    coach_id BIGINT,
    ssnid VARCHAR,
    activity_type_id BIGINT,
    res_users_settings_id BIGINT,
    name VARCHAR,
    user_group_warning VARCHAR,
    title BIGINT,
    parent_name VARCHAR,
    parent_id BIGINT,
    ref VARCHAR,
    study_school VARCHAR,
    state VARCHAR,
    -- state has values: active is Confirmed|Đã xác nhận, new is Never Connected|Chưa từng kết nối,
    same_vat_partner_id BIGINT,
    same_company_registry_partner_id BIGINT,
    website VARCHAR,
    street VARCHAR,
    street2 VARCHAR,
    zip VARCHAR,
    state_id BIGINT,
    mobile VARCHAR,
    self BIGINT,
    phone VARCHAR,
    last_activity DATE,
    last_activity_time VARCHAR,
    create_employee BOOLEAN,
    can_edit BOOLEAN,
    property_stock_supplier BIGINT,
    picking_warn VARCHAR,
    picking_warn_msg VARCHAR,
    buyer_id BIGINT,
    is_peppol_edi_format BOOLEAN,
    is_ubl_format BOOLEAN,
    partner_id BIGINT,
    -- partner_id is a foreign key to res_partner.id,
    login VARCHAR,
    password VARCHAR,
    new_password VARCHAR,
    action_id BIGINT,
    share BOOLEAN,
    company_id BIGINT,
    -- company_id is a foreign key to res_company.id,
    accesses_count BIGINT,
    sale_order_count BIGINT,
    property_warehouse_id BIGINT,
    groups_count BIGINT,
    company_registry VARCHAR,
    employee BOOLEAN,
    country_code VARCHAR,
    email_formatted VARCHAR,
    is_company BOOLEAN,
    partner_share BOOLEAN,
    totp_secret VARCHAR,
    im_status VARCHAR,
    invoice_edi_format_store VARCHAR,
    message_bounce BIGINT,
    user_id BIGINT,
    vat VARCHAR,
    resource_calendar_id BIGINT,
    notification_type VARCHAR,
    -- notification_type has values: email is Handle by Emails|Xử lý bằng email, inbox is Handle in Odoo|Xử lý trong hệ thống,
    has_message BOOLEAN,
    message_needaction BOOLEAN,
    message_needaction_counter BIGINT,
    message_has_error BOOLEAN,
    message_has_error_counter BIGINT,
    message_attachment_count BIGINT,
    email_normalized VARCHAR,
    is_blacklisted BOOLEAN,
    activity_state VARCHAR,
    activity_type_icon VARCHAR,
    activity_date_deadline DATE,
    my_activity_date_deadline DATE,
    activity_summary VARCHAR,
    activity_exception_decoration VARCHAR,
    activity_exception_icon VARCHAR,
    property_product_pricelist BIGINT,
    sale_team_id BIGINT,
    -- sale_team_id is a foreign key to crm_team.id,
    message_has_sms_error BOOLEAN,
    payment_token_count BIGINT,
    peppol_eas VARCHAR,
    purchase_order_count BIGINT,
    supplier_invoice_count BIGINT,
    purchase_warn_msg VARCHAR,
    receipt_reminder_email BOOLEAN,
    reminder_date_before_receipt BIGINT,
    purchase_warn VARCHAR,
    property_purchase_currency_id BIGINT,
    website_published BOOLEAN,
    is_published BOOLEAN,
    can_publish BOOLEAN,
    website_id BIGINT,
    -- website_id is a foreign key to website.id,
    website_url VARCHAR,
    last_website_so_id BIGINT
);

-- Ecommerce Order Line Fee|Phí dòng đơn hàng TMĐT
CREATE TABLE ecommerce_order_line_fee (
    ecommerce_order_line_id BIGINT,
    -- ecommerce_order_line_id is a foreign key to ecommerce_order_line.id,
    company_id BIGINT,
    currency_id BIGINT,
    fee_type_key VARCHAR,
    fee_type_name VARCHAR,
    fee_type_status VARCHAR,
    quantity BIGINT,
    base_amount DOUBLE,
    total_amount DOUBLE,
    discount_amount DOUBLE,
    final_amount DOUBLE,
    display_name VARCHAR
);

            ### User question:
            How many shipped orders does Shopee have?, What is the total count of delivered orders placed on Shopee?, Which customers have their Shopee orders marked as delivered?

            ### Business concepts:
            Thuộc bảng utm_medium cột name với giá trị là SHOPEE

            ### Output requirements:
            - Return **only one valid SQL query** that correctly answers the question.
            - Use proper table joins based on foreign key relationships shown in the DDL.
            - Avoid assumptions beyond what’s available in the DDL.
            - If the question is ambiguous, interpret it in the most common Odoo business context.

            ### Expected output format:
            ```sql ```
        ","SELECT COUNT(*) AS shipped_orders
FROM   ecommerce_order eo
JOIN   utm_medium um ON um.id = eo.utm_medium_id
WHERE  unaccent(lower(um.name)) ILIKE unaccent(lower('SHOPEE'))
  AND  eo.state = 'shipped';","[(172,)]"
Bao nhiêu đơn hàng shopee đã giao hàng,"
            You are an expert in Odoo 18 and PostgreSQL databases.
            Your task is to translate a natural language question and business concepts into a valid SQL query that can be executed within an Odoo 18 environment.

            ### System context:
            - Odoo version: 18
            - Database: PostgreSQL
            - Table names usually start with prefixes like `res_`, `hr_`, `account_`, `sale_`, `stock_`, `purchase_`, `mrp_`, `product_`, `crm_`, `project_`, `report_`, `ecommerce_`, etc.
            - Always follow standard PostgreSQL SQL syntax.
            - Avoid using any data or columns not defined in the provided DDL.
            - For columns of textual types (char, text, selection, etc.):
                + Normalize both sides by removing diacritics (accent removal),
                + Convert to lowercase,
                + And use the ILIKE operator for comparison instead of =.
            - When table has column state use it to filter data.
            - When table has column active use it to filter data.

            ### Database schema (DDL of relevant tables):
            -- Ecommerce Order model stores all order data collected from multiple e-commerce platforms such as Shopee, Lazada, Tiki, TikTok Shop, etc. Each record represents a unique order synchronized from these marketplaces. The field 'utm_name' identifies the source platform (e.g., 'Shopee', 'Lazada', Tiki, 'TikTok').|Đơn hàng TMĐT
CREATE TABLE ecommerce_order (
    delivery_partner_id BIGINT,
    -- delivery_partner_id is a foreign key to delivery_carrier.id,
    ec_warehouse_id BIGINT,
    -- ec_warehouse_id is a foreign key to ecommerce_warehouse_mapping.id,
    utm_medium_id BIGINT,
    -- utm_medium_id is a foreign key to utm_medium.id,
    utm_medium_name VARCHAR,
    order_code VARCHAR,
    sale_order_id BIGINT,
    -- sale_order_id is a foreign key to sale_order.id,
    company_id BIGINT,
    -- company_id is a foreign key to res_company.id,
    currency_id BIGINT,
    -- currency_id is a foreign key to res_currency.id,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    shipping_name VARCHAR,
    shipping_phone VARCHAR,
    shipping_town VARCHAR,
    payment_status VARCHAR,
    total_amount DOUBLE,
    voucher_amount DOUBLE,
    payment_time TIMESTAMP,
    note VARCHAR,
    is_virtual BOOLEAN,
    status_histories VARCHAR,
    is_cod BOOLEAN,
    has_printed BOOLEAN,
    has_product_not_linked BOOLEAN,
    activity_state VARCHAR,
    -- activity_state has values: overdue is Overdue|, today is Today|, planned is Planned|,
    activity_user_id BIGINT,
    activity_type_id BIGINT,
    activity_type_icon VARCHAR,
    activity_date_deadline DATE,
    packed_date TIMESTAMP,
    draft_date TIMESTAMP,
    waiting_date TIMESTAMP,
    shipped_date TIMESTAMP,
    done_date TIMESTAMP,
    ref_order_number VARCHAR,
    checkout_shipping_carrier VARCHAR,
    -- checkout_shipping_carrier has values: standard is Standard Delivery|, fast is Fast Delivery|,
    res_partner_id BIGINT,
    -- res_partner_id is a foreign key to res_partner.id,
    res_partner_invoice_id BIGINT,
    -- res_partner_invoice_id is a foreign key to res_partner.id,
    res_partner_shipping_id BIGINT,
    -- res_partner_shipping_id is a foreign key to res_partner.id,
    customer_name VARCHAR,
    customer_phone VARCHAR,
    customer_email VARCHAR,
    shipping_district VARCHAR,
    shipping_state VARCHAR,
    shipping_address_full VARCHAR,
    shipping_city VARCHAR,
    shipping_region VARCHAR,
    shipping_zipcode VARCHAR,
    shipping_ward VARCHAR,
    shipping_carrier VARCHAR,
    tracking_code VARCHAR,
    shipping_status VARCHAR,
    shipping_fee DOUBLE,
    shipping_fee_discount DOUBLE,
    promised_delivery_date TIMESTAMP,
    warehouse_code VARCHAR,
    warehouse_name VARCHAR,
    warehouse_id BIGINT,
    -- warehouse_id is a foreign key to stock_warehouse.id,
    payment_method VARCHAR,
    locked BOOLEAN,
    order_type VARCHAR,
    -- order_type has values: cancel is CANCEL|HỦY, normal is NORMAL|Đơn bán, refund is REFUND|HOÀN TIỀN, return is RETURN|Đơn trả,
    reason VARCHAR,
    text_reason VARCHAR,
    return_order_code VARCHAR,
    refund_status VARCHAR,
    images VARCHAR,
    videos VARCHAR,
    sale_order_count BIGINT,
    ecommerce_shop_id BIGINT,
    -- ecommerce_shop_id is a foreign key to ecommerce_shop.id,
    display_name VARCHAR,
    order_status VARCHAR,
    -- order_status has values: UNPAID is UNPAID|, READY_TO_SHIP is READY_TO_SHIP|, PROCESSED is PROCESSED|, RETRY_SHIP is RETRY_SHIP|, SHIPPED is SHIPPED|, TO_CONFIRM_RECEIVE is TO_CONFIRM_RECEIVE|, IN_CANCEL is IN_CANCEL|, CANCELLED is CANCELLED|, TO_RETURN is TO_RETURN|, COMPLETED is COMPLETED|, UNKNOWN is UNKNOWN|, ACCEPTED is ACCEPTED|, REQUESTED is REQUESTED|, PROCESSING is PROCESSING|, JUDGING is JUDGING|, SELLER_DISPUTE is SELLER_DISPUTE|, CLOSED is CLOSED|,
    my_activity_date_deadline DATE,
    activity_summary VARCHAR,
    activity_exception_decoration VARCHAR,
    -- activity_exception_decoration has values: warning is Alert|, danger is Error|,
    activity_exception_icon VARCHAR,
    message_is_follower BOOLEAN,
    order_status_vn VARCHAR,
    has_message BOOLEAN,
    message_needaction BOOLEAN,
    message_needaction_counter BIGINT,
    message_has_error BOOLEAN,
    message_has_error_counter BIGINT,
    message_attachment_count BIGINT,
    message_has_sms_error BOOLEAN,
    state VARCHAR,
    -- state has values: draft is Draft|Đơn bán hàng, waiting is Waiting|Đang chờ, done is Done|Hoàn tất, cancelled is Cancelled|Đã hủy, packed is Packed|Đóng gói, shipped is Shipped|Giao hàng,
    packing_date TIMESTAMP,
    state_history_count BIGINT
);

-- E-commerce Statement model stores financial statement data synchronized from various e-commerce platforms such as Shopee, Lazada, and TikTok Shop, Tiki. Each record represents a summarized transaction report or settlement period, including details like total revenue, commissions, fees, discounts, and payout amounts. |
CREATE TABLE ecommerce_statement (
    name VARCHAR,
    company_id BIGINT,
    -- company_id is a foreign key to res_company.id,
    currency_id BIGINT,
    -- currency_id is a foreign key to res_currency.id,
    utm_medium_id BIGINT,
    -- utm_medium_id is a foreign key to utm_medium.id,
    ecommerce_order_id BIGINT,
    -- ecommerce_order_id is a foreign key to ecommerce_order.id,
    ecommerce_shop_id BIGINT,
    -- ecommerce_shop_id is a foreign key to ecommerce_shop.id,
    order_sn VARCHAR,
    buyer_user_name VARCHAR,
    return_order_sn_list VARCHAR,
    voucher_from_platform DOUBLE,
    display_name VARCHAR,
    escrow_amount DOUBLE,
    commission_fee DOUBLE,
    service_fee DOUBLE,
    platform_discount DOUBLE,
    voucher_from_seller DOUBLE,
    coins DOUBLE,
    total_seller_discount_amount DOUBLE,
    total_seller_shipping_discount_amount DOUBLE,
    instalment_plan VARCHAR,
    buyer_payment_method VARCHAR,
    seller_voucher_code VARCHAR,
    tax_registration_code VARCHAR,
    rsf_seller_prot_claim_amt DOUBLE,
    fsf_seller_prot_claim_amt DOUBLE,
    seller_lost_comp DOUBLE,
    drc_adj_refund DOUBLE,
    seller_return_refund DOUBLE,
    order_ams_comm_fee DOUBLE,
    trade_in_bonus_seller DOUBLE,
    original_price DOUBLE,
    cogs DOUBLE,
    orig_cogs DOUBLE,
    order_dsc_price DOUBLE,
    order_orig_price DOUBLE,
    order_seller_dsc DOUBLE,
    order_selling_price DOUBLE,
    seller_txn_fee DOUBLE,
    credit_card_txn_fee DOUBLE,
    campaign_fee DOUBLE,
    seller_order_proc_fee DOUBLE,
    seller_discount DOUBLE,
    seller_coin_cash_back DOUBLE,
    payment_promotion DOUBLE,
    credit_card_promotion DOUBLE,
    buyer_paid_ship_fee DOUBLE,
    actual_ship_fee DOUBLE,
    platform_ship_rebate DOUBLE,
    seller_ship_dsc DOUBLE,
    reverse_ship_fee DOUBLE,
    estimated_ship_fee DOUBLE,
    ship_fee_dsc_from_3pl DOUBLE,
    final_return_seller_ship_fee DOUBLE,
    overseas_return_service_fee DOUBLE,
    buyer_paid_packaging_fee DOUBLE,
    cross_border_tax DOUBLE,
    escrow_tax DOUBLE,
    final_escrow_prod_gst DOUBLE,
    final_escrow_ship_gst DOUBLE,
    final_prod_vat_tax DOUBLE,
    final_ship_vat_tax DOUBLE,
    sales_tax_on_lvg DOUBLE,
    ship_fee_sst DOUBLE,
    reverse_ship_fee_sst DOUBLE,
    vat_on_imported_goods DOUBLE,
    withholding_tax DOUBLE,
    withholding_vat_tax DOUBLE,
    withholding_pit_tax DOUBLE,
    delivery_seller_prot_fee_prem_amt DOUBLE,
    ship_seller_prot_fee_amt DOUBLE,
    total_seller_fee DOUBLE,
    total_seller_tax_amount DOUBLE,
    other_fees DOUBLE
);

-- Ecommerce Order Line model stores detailed item-level information for each order from various e-commerce platforms such as Shopee, Lazada, Tiki and TikTok Shop. Each record represents a single product line belonging to an ecommerce.order. Typical fields include product details, quantity, unit price, subtotal, and associated platform (via the related order's utm_name).|Dòng đơn hàng TMĐT
CREATE TABLE ecommerce_order_line (
    order_item_id VARCHAR,
    weight DOUBLE,
    combo_mapping_name VARCHAR,
    ecommerce_order_id BIGINT,
    -- ecommerce_order_id is a foreign key to ecommerce_order.id,
    utm_medium_id BIGINT,
    -- utm_medium_id is a foreign key to utm_medium.id,
    company_id BIGINT,
    currency_id BIGINT,
    item_id VARCHAR,
    sku VARCHAR,
    product_name VARCHAR,
    quantity BIGINT,
    price DOUBLE,
    original_price DOUBLE,
    voucher_amount DOUBLE,
    platform_discount DOUBLE,
    seller_discount DOUBLE,
    product_image VARCHAR,
    confirm_status VARCHAR,
    confirm_at TIMESTAMP,
    model_id VARCHAR,
    model_name VARCHAR,
    wh_partner_code VARCHAR,
    ref_virtual_id VARCHAR,
    display_name VARCHAR,
    ecommerce_product_product_id BIGINT,
    -- ecommerce_product_product_id is a foreign key to ecommerce_product_product.id,
    has_ec_product BOOLEAN
);

-- Work Center|Khu vực sản xuất
CREATE TABLE mrp_workcenter (
    message_attachment_count BIGINT,
    company_id BIGINT,
    -- company_id is a foreign key to res_company.id,
    resource_id BIGINT,
    -- resource_id is a foreign key to resource_resource.id,
    sequence BIGINT,
    resource_calendar_id BIGINT,
    -- resource_calendar_id is a foreign key to resource_calendar.id,
    expense_account_id BIGINT,
    -- expense_account_id is a foreign key to account_account.id,
    color BIGINT,
    blocked_time DOUBLE,
    analytic_precision BIGINT,
    time_start DOUBLE,
    currency_id BIGINT,
    message_is_follower BOOLEAN,
    has_message BOOLEAN,
    message_needaction BOOLEAN,
    message_needaction_counter BIGINT,
    message_has_error BOOLEAN,
    message_has_error_counter BIGINT,
    message_has_sms_error BOOLEAN,
    name VARCHAR,
    time_efficiency DOUBLE,
    active BOOLEAN,
    code VARCHAR,
    default_capacity DOUBLE,
    costs_hour DOUBLE,
    time_stop DOUBLE,
    has_routing_lines BOOLEAN,
    workorder_count BIGINT,
    workorder_ready_count BIGINT,
    workorder_progress_count BIGINT,
    workorder_pending_count BIGINT,
    workorder_late_count BIGINT,
    working_state VARCHAR,
    -- working_state has values: blocked is Blocked|Đã bị chặn, normal is Normal|Bình thường, done is In Progress|Đang thực hiện,
    productive_time DOUBLE,
    oee DOUBLE,
    oee_target DOUBLE,
    performance BIGINT,
    workcenter_load DOUBLE,
    kanban_dashboard_graph VARCHAR,
    display_name VARCHAR
);

-- Picking Type|Kiểu lấy hàng
CREATE TABLE stock_picking_type (
    count_mo_late BIGINT,
    count_mo_in_progress BIGINT,
    count_mo_to_close BIGINT,
    product_label_format VARCHAR,
    -- product_label_format has values: dymo is Dymo|Dymo, 2x7xprice is 2 x 7 with price|2 x 7 kèm giá, 4x7xprice is 4 x 7 with price|4 x 7 kèm giá, 4x12 is 4 x 12|4 x 12, 4x12xprice is 4 x 12 with price|4 x 12 kèm giá, zplxprice is ZPL Labels with price|Nhãn ZPL có giá, zpl is ZPL Labels|Nhãn ZPL,
    name VARCHAR,
    sequence BIGINT,
    sequence_id BIGINT,
    -- sequence_id is a foreign key to ir_sequence.id,
    sequence_code VARCHAR,
    return_picking_type_id BIGINT,
    -- return_picking_type_id is a foreign key to stock_picking_type.id,
    show_picking_type BOOLEAN,
    auto_print_package_label BOOLEAN,
    print_label BOOLEAN,
    mrp_product_label_to_print VARCHAR,
    -- mrp_product_label_to_print has values: zpl is ZPL|ZPL, pdf is PDF|PDF,
    done_mrp_lot_label_to_print VARCHAR,
    -- done_mrp_lot_label_to_print has values: zpl is ZPL|ZPL, pdf is PDF|PDF,
    package_label_to_print VARCHAR,
    -- package_label_to_print has values: pdf is PDF|PDF, zpl is ZPL|ZPL,
    use_create_lots BOOLEAN,
    auto_print_reception_report_labels BOOLEAN,
    color BIGINT,
    active BOOLEAN,
    count_mo_waiting BIGINT,
    default_location_src_id BIGINT,
    -- default_location_src_id is a foreign key to stock_location.id,
    default_location_dest_id BIGINT,
    -- default_location_dest_id is a foreign key to stock_location.id,
    show_entire_packs BOOLEAN,
    code VARCHAR,
    -- code has values: incoming is Receipt|Phiếu nhập kho, outgoing is Delivery|Giao hàng, internal is Internal Transfer|Lệnh chuyển hàng nội bộ, mrp_operation is Manufacturing|Sản xuất,
    use_existing_lots BOOLEAN,
    show_operations BOOLEAN,
    reservation_method VARCHAR,
    -- reservation_method has values: at_confirm is At Confirmation|Khi xác nhận, manual is Manually|Thủ công, by_date is Before scheduled date|Trước ngày theo kế hoạch,
    reservation_days_before BIGINT,
    reservation_days_before_priority BIGINT,
    auto_show_reception_report BOOLEAN,
    auto_print_delivery_slip BOOLEAN,
    auto_print_return_slip BOOLEAN,
    auto_print_product_labels BOOLEAN,
    auto_print_lot_labels BOOLEAN,
    lot_label_format VARCHAR,
    -- lot_label_format has values: 4x12_lots is 4 x 12 - One per lot/SN|4 x 12 - Một trên mỗi số lô/sê-ri, 4x12_units is 4 x 12 - One per unit|4 x 12 - Một trên mỗi đơn vị, zpl_lots is ZPL Labels - One per lot/SN|Nhãn ZPL - Một trên mỗi số lô/sê-ri, zpl_units is ZPL Labels - One per unit|Nhãn ZPL - Một trên mỗi đơn vị,
    auto_print_reception_report BOOLEAN,
    auto_print_packages BOOLEAN,
    count_picking_draft BIGINT,
    count_picking_ready BIGINT,
    count_picking BIGINT,
    count_picking_waiting BIGINT,
    count_picking_late BIGINT,
    count_picking_backorders BIGINT,
    count_move_ready BIGINT,
    hide_reservation_method BOOLEAN,
    barcode VARCHAR,
    company_id BIGINT,
    -- company_id is a foreign key to res_company.id,
    create_backorder VARCHAR,
    -- create_backorder has values: ask is Ask|Hỏi, always is Always|Luôn luôn, never is Never|Không bao giờ,
    is_favorite BOOLEAN,
    kanban_dashboard_graph VARCHAR,
    move_type VARCHAR,
    -- move_type has values: direct is As soon as possible|Càng sớm càng tốt, one is When all products are ready|Khi tất cả sản phẩm đã sẵn sàng,
    display_name VARCHAR,
    warehouse_id BIGINT,
    -- warehouse_id is a foreign key to stock_warehouse.id,
    count_mo_todo BIGINT,
    auto_print_done_production_order BOOLEAN,
    auto_print_done_mrp_product_labels BOOLEAN,
    auto_print_done_mrp_lot BOOLEAN,
    auto_print_mrp_reception_report BOOLEAN,
    auto_print_mrp_reception_report_labels BOOLEAN,
    auto_print_generated_mrp_lot BOOLEAN,
    generated_mrp_lot_label_to_print VARCHAR,
    -- generated_mrp_lot_label_to_print has values: zpl is ZPL|ZPL, pdf is PDF|PDF,
    use_create_components_lots BOOLEAN
);

-- E-commerce Statement Line|
CREATE TABLE ecommerce_statement_line (
    model_id VARCHAR,
    model_name VARCHAR,
    item_sku VARCHAR,
    model_sku VARCHAR,
    display_name VARCHAR,
    statement_id BIGINT,
    -- statement_id is a foreign key to ecommerce_statement.id,
    currency_id BIGINT,
    -- currency_id is a foreign key to res_currency.id,
    item_id VARCHAR,
    item_name VARCHAR,
    qty_purchased BIGINT,
    original_price DOUBLE,
    discounted_price DOUBLE,
    seller_discount DOUBLE,
    platform_discount DOUBLE,
    dsc_from_coin DOUBLE,
    dsc_from_vch_platform DOUBLE,
    dsc_from_vch_seller DOUBLE,
    seller_order_proc_fee DOUBLE,
    payout_amount DOUBLE
);

            ### User question:
            How many Shopee orders have been delivered to customers?, What is the total number of completed Shopee sales transactions?, Show the count of fulfilled Shopee customer orders

            ### Business concepts:
            Thuộc bảng utm_medium cột name với giá trị là SHOPEE

            ### Output requirements:
            - Return **only one valid SQL query** that correctly answers the question.
            - Use proper table joins based on foreign key relationships shown in the DDL.
            - Avoid assumptions beyond what’s available in the DDL.
            - If the question is ambiguous, interpret it in the most common Odoo business context.

            ### Expected output format:
            ```sql ```
        ","SELECT COUNT(*) AS shopee_delivered_orders
FROM ecommerce_order eo
JOIN utm_medium um ON um.id = eo.utm_medium_id
WHERE unaccent(LOWER(um.name)) ILIKE unaccent(LOWER('SHOPEE'))
  AND eo.state = 'done';","[(232,)]"
