# utils/embedding.py
from typing import List
import ollama

class OllamaEmbedder:
    """
    Trình tạo embedding dùng Ollama API.
    Yêu cầu: Ollama server đang chạy (mặc định: http://localhost:11434)
    """
    def __init__(self, model: str = "nomic-embed-text:latest"):
        self.model = model

    def embed(self, text: str) -> List[float]:
        """
        Tạo embedding cho văn bản.
        Trả về: list[float] có độ dài 768 (với nomic-embed-text)
        """
        try:
            # Sử dụng thư viện ollama để tạo embedding
            response = ollama.embeddings(model=self.model, prompt=text)
            embedding = response["embedding"]
            return embedding
        except Exception as e:
            print(f"❌ Lỗi khi tạo embedding: {e}")
            # Tr<PERSON> về embedding mặc định (tránh crash)
            return [0.0] * 768