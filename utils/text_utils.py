import csv
from typing import Dict

class TextUtils:
    @staticmethod
    def load_glossary(file_path: str) -> Dict[str, str]:
        glossary = {}
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                reader = csv.reader(f)
                for row in reader:
                    if len(row) >= 2:
                        glossary[row[0].strip().upper()] = row[1].strip()
        except FileNotFoundError:
            print(f"⚠️ Không tìm thấy file: {file_path}")
        return glossary

    @staticmethod
    def normalize_business_terms(text: str, glossary: Dict[str, str]) -> str:
        words = text.split()
        normalized = []
        for w in words:
            key = w.strip(".,?!").upper()
            if key in glossary:
                normalized.append(glossary[key])
            else:
                normalized.append(w)
        return " ".join(normalized)