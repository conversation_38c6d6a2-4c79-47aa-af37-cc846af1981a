# core/schema_to_kg.py
import json
from neo4j import GraphDatabase
from config.settings import Settings

class SchemaToKG:
    def __init__(self, schema_path: str = "data/table_schema.json"):
        with open(schema_path, "r", encoding="utf-8") as f:
            self.schema = json.load(f)
        self.driver = GraphDatabase.driver(
            Settings.NEO4J_URI,
            auth=(Settings.NEO4J_USER, Settings.NEO4J_PASSWORD)
        )

    def build(self):
        self._clear_graph()
        self._create_nodes()
        self._create_glossary()
        print("✅ KG từ schema đã được tạo!")

    def _clear_graph(self):
        with self.driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")

    def _create_nodes(self):
        """Tạo Model và Field từ table_schema.json"""
        with self.driver.session() as session:
            for table_name, meta in self.schema.items():
                # Chuyển account_move → account.move
                odoo_model = table_name.replace("_", ".")
                
                # Tạo Model node
                session.run("""
                    CREATE (:Model {
                        odoo_model: $odoo_model,
                        rdfs__label: $table_name
                    })
                """, odoo_model=odoo_model, table_name=table_name)

                # Tạo Field nodes
                for field_name, field_info in meta["columns"].items():
                    session.run("""
                        MATCH (m:Model {odoo_model: $odoo_model})
                        CREATE (m)<-[:BELONGS_TO]-(f:Field {
                            odoo_model: $odoo_model,
                            odoo_field: $field_name,
                            rdfs__label: $field_name,
                            odoo_type: $field_type
                        })
                    """, 
                    odoo_model=odoo_model,
                    field_name=field_name,
                    field_type=field_info["type"])

    def _create_glossary(self):
        """Thêm từ điển doanh nghiệp"""
        from utils.text_utils import TextUtils
        glossary = TextUtils.load_glossary("data/glossary.csv")
        with self.driver.session() as session:
            for term, concept in glossary.items():
                session.run("""
                    CREATE (:Term {
                        name: $term,
                        concept: $concept
                    })
                """, term=term, concept=concept)

    def close(self):
        self.driver.close()

# Chạy script
if __name__ == "__main__":
    builder = SchemaToKG()
    builder.build()
    builder.close()