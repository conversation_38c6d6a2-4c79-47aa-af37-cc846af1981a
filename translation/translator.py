from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
import re

class Translator:
    def __init__(self, model_name: str = "VietAI/envit5-translation"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModelForSeq2SeqLM.from_pretrained(model_name)

    def vi_to_en(self, text: str) -> str:
        # <PERSON><PERSON><PERSON> hóa dấu cách
        text = re.sub(r"\s+", " ", text.strip())
        input_ids = self.tokenizer(
            f"vi2en: {text}", return_tensors="pt", padding=True
        ).input_ids
        outputs = self.model.generate(
            input_ids,
            max_length=256,
            num_beams=3,
            early_stopping=True
        )
        return self.tokenizer.decode(outputs[0], skip_special_tokens=True)

    def en_to_vi(self, text: str) -> str:
        text = re.sub(r"\s+", " ", text.strip())
        input_ids = self.tokenizer(
            f"en2vi: {text}", return_tensors="pt", padding=True
        ).input_ids
        outputs = self.model.generate(
            input_ids,
            max_length=256,
            num_beams=3,
            early_stopping=True
        )
        return self.tokenizer.decode(outputs[0], skip_special_tokens=True)