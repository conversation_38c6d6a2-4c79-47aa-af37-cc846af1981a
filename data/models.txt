phone.blacklist.remove is <PERSON><PERSON><PERSON> số điện thoại khỏi danh sách hạn chế (Remove phone from blacklist)|(phone.blacklist.remove)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.merge.wizard is <PERSON><PERSON><PERSON><PERSON> dẫn hợp nhất tà<PERSON> (Account merge wizard)|(account.merge.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sms.account.sender is <PERSON><PERSON><PERSON> cụ tên người gửi (SMS Account Sender Name Wizard)|(sms.account.sender)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
website.robots is <PERSON><PERSON><PERSON><PERSON> chỉnh sửa <PERSON>.txt (Robots.txt Editor)|(website.robots)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.activity.schedule is Tính năng kế hoạch lên lịch hoạt động (Activity schedule plan Wizard)|(mail.activity.schedule)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
spreadsheet.dashboard is Trang chủ bảng tính (Spreadsheet Dashboard)|(spreadsheet.dashboard)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
google.gmail.mixin is Google Gmail Mixin (Google Gmail Mixin)|(google.gmail.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
sale.order.template.option is Tùy chọn mẫu báo giá (Quotation Template Option)|(sale.order.template.option)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.custom_blocked_third_party_domains is Danh sách miền của bên thứ 3 bị chặn của người dùng (User list of blocked 3rd-party domains)|(website.custom_blocked_third_party_domains)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
product.document is Tài liệu sản phẩm (Product Document)|(product.document)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
link.tracker is Trình theo dõi Liên kết (Link Tracker)|(link.tracker)| Link trackers allow users to wrap any URL into a short URL that can be
    tracked by Odoo. Clicks are counter on each link. A tracker is linked to
    UTMs allowing to analyze marketing actions.

    This model is also used in mass_mailing where each link in html body is
    automatically converted into a short link that is tracked and integrates
    UTMs. 
product.catalog.mixin is Mixin danh mục sản phẩm (Product Catalog Mixin)|(product.catalog.mixin)| This mixin should be inherited when the model should be able to work
    with the product catalog.
    It assumes the model using this mixin has a O2M field where the products are added/removed and
    this field's co-related model should has a method named `_get_product_catalog_lines_data`.
    
purchase.order.line is Dòng đơn mua hàng (Purchase Order Line)|(purchase.order.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order.option is Tùy chọn bán hàng (Sale Options)|(sale.order.option)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sms.account.phone is Công cụ đăng ký số điện thoại (SMS Account Registration Phone Number Wizard)|(sms.account.phone)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
link.tracker.click is Liên kết theo dõi số lần nhấn (Link Tracker Click)|(link.tracker.click)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.account.report_hash_integrity is Nhận kết quả toàn vẹn dữ liệu hash dưới dạng PDF. (Get hash integrity result as PDF.)|(report.account.report_hash_integrity)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.lot is Lô/sê-ri (Lot/Serial)|(stock.lot)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
choose.delivery.carrier is Công cụ chọn đơn vị giao hàng (Delivery Carrier Selection Wizard)|(choose.delivery.carrier)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.incoterms is Incoterm (Incoterms)|(account.incoterms)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.return.picking.line is Dòng phiếu trả hàng (Return Picking Line)|(stock.return.picking.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
delivery.zip.prefix is Tiền tố zip giao hàng (Delivery Zip Prefix)|(delivery.zip.prefix)| Zip prefix that a delivery.carrier will deliver to. 
product.attribute.custom.value is Giá trị tùy chỉnh của thuộc tính sản phẩm (Product Attribute Custom Value)|(product.attribute.custom.value)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.invoice.report is Thống kê hóa đơn (Invoices Statistics)|(account.invoice.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.tracking.value is Giá trị Theo vết Thư (Mail Tracking Value)|(mail.tracking.value)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
link.tracker.code is Link Tracker Code (Link Tracker Code)|(link.tracker.code)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
wizard.ir.model.menu.create is Tạo Trình Menu (Create Menu Wizard)|(wizard.ir.model.menu.create)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
format.vat.label.mixin is Nhãn thuế GTGT cụ thể theo quốc gia (Country Specific VAT Label)|(format.vat.label.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
report.product.report_producttemplatelabel2x7 is Báo cáo nhãn sản phẩm 2x7 (Product Label Report 2x7)|(report.product.report_producttemplatelabel2x7)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
report.product.report_producttemplatelabel4x7 is Báo cáo nhãn sản phẩm 4x7 (Product Label Report 4x7)|(report.product.report_producttemplatelabel4x7)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mail.resend.message is Tính năng gửi lại email (Email resend wizard)|(mail.resend.message)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
choose.delivery.package is Công cụ chọn kiện giao hàng (Delivery Package Selection Wizard)|(choose.delivery.package)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
delivery.price.rule is Quy tắc phí vận chuyển (Delivery Price Rules)|(delivery.price.rule)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.move.line is Dòng bút toán (Journal Item)|(account.move.line)| Override AccountInvoice_line to add the link to the purchase order line it is related to
sms.tracker is Liên kết SMS với các mô hình theo dõi thư/sms (Link SMS to mailing/sms tracking models)|(sms.tracker)|Relationship between a sent SMS and tracking records such as notifications and traces.

    This model acts as an extension of a `mail.notification` or a `mailing.trace` and allows to
    update those based on the SMS provider responses both at sending and when later receiving
    sent/delivery reports (see `SmsController`).
    SMS trackers are supposed to be created manually when necessary, and tied to their related
    SMS through the SMS UUID field. (They are not tied to the SMS records directly as those can
    be deleted when sent).

    Note: Only admins/system user should need to access (a fortiori modify) these technical
      records so no "sudo" is used nor should be required here.
    
sale.advance.payment.inv is Hoá đơn tạm ứng bán hàng (Sales Advance Payment Invoice)|(sale.advance.payment.inv)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
tiki.config is TikiConfig|(tiki.config)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
tiki.config.line is TikiConfigLine|(tiki.config.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.move.line is Điều chuyển sản phẩm (Dòng điều chuyển tồn kho) (Product Moves (Stock Move Line))|(stock.move.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.code.mapping is Mapping mã tài khoản theo từng công ty (Mapping of account codes per company)|(account.code.mapping)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.daybook.report is Day Book Report|(account.daybook.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.cashbook.report is Cash Book Report|(account.cashbook.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.bankbook.report is Bank Book Report|(account.bankbook.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.om_account_daily_reports.report_daybook is Day Book|(report.om_account_daily_reports.report_daybook)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
onboarding.onboarding is Onboarding (Onboarding)|(onboarding.onboarding)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.account.type is Loại tài khoản (Account Account Type)|(account.account.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
loyalty.generate.wizard is Tạo phiếu giảm giá (Generate Coupons)|(loyalty.generate.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
utm.stage is Giai đoạn của chiến dịch (Campaign Stage)|(utm.stage)|Stage for utm campaigns.
rating.mixin is Rating Mixin (Rating Mixin)|(rating.mixin)|This mixin adds rating statistics to mail.thread that already support ratings.
crm.team.member is Thành viên bộ phận sales (Sales Team Member)|(crm.team.member)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
rating.parent.mixin is Rating Parent Mixin (Rating Parent Mixin)|(rating.parent.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
onboarding.onboarding.step is Trình tự onboarding (Onboarding Step)|(onboarding.onboarding.step)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.bom.line is Dòng định mức nguyên liệu (Bill of Material Line)|(mrp.bom.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
loyalty.history is Lịch sử của thẻ khách hàng thân thiết và ví điện tử (History for Loyalty cards and Ewallets)|(loyalty.history)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order.line is Dòng đơn bán hàng (Sales Order Line)|(sale.order.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
utm.mixin is Mixin UTM (UTM Mixin)|(utm.mixin)| Mixin class for objects which can be tracked by marketing. 
delivery.carrier is Phương thức vận chuyển (Shipping Methods)|(delivery.carrier)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.payment.term.line is Chi tiết điều khoản thanh toán (Payment Terms Line)|(account.payment.term.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.route is Mọi tuyến website (All Website Route)|(website.route)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.reconcile.model is Thiết lập trước để tạo bút toán trong quá trình khớp hóa đơn và thanh toán (Preset to create journal entries during a invoices and payments matching)|(account.reconcile.model)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order.cancel is Đơn bán hàng Huỷ (Sales Order Cancel)|(sale.order.cancel)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
loyalty.mail is Trao đổi về khách hàng thân thiết (Loyalty Communication)|(loyalty.mail)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.financial.report is Báo cáo kế toán (Account Report)|(account.financial.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.accounting_pdf_reports.report_partnerledger is Báo cáo sổ chi tiết công nợ đối tác (Partner Ledger Report)|(report.accounting_pdf_reports.report_partnerledger)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.brand is ResBrand|(res.brand)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.multi.mixin is Đa trang web Mixin (Multi Website Mixin)|(website.multi.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
followup.followup is Theo dõi công nợ (Account Follow-up)|(followup.followup)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
utm.tag is Thẻ UTM (UTM Tag)|(utm.tag)|Model of categories of utm campaigns, i.e. marketing, newsletter, ...
change.production.qty is Thay đổi SL sản xuất (Change Production Qty)|(change.production.qty)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.mrp.report_mo_overview is Báo cáo tổng quan ĐMNL (MO Overview Report)|(report.mrp.report_mo_overview)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
website.published.multi.mixin is Nhiều trang web được xuất bản Mixin (Multi Website Published Mixin)|(website.published.multi.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.asset.category is Loại tài sản (Asset category)|(account.asset.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.journal.group is Nhóm sổ nhật kí tài khoản (Account Journal Group)|(account.journal.group)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
payment.provider is Nhà cung cấp dịch vụ thanh toán (Payment Provider)|(payment.provider)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.account.tag is Thẻ tài khoản (Account Tag)|(account.account.tag)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sms.template is Mẫu SMS (SMS Templates)|(sms.template)|Templates for sending SMS
snailmail.letter.missing.required.fields is Cập nhật địa chỉ của đối tác (Update address of partner)|(snailmail.letter.missing.required.fields)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.scrap is Phế phẩm (Scrap)|(stock.scrap)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.fiscal.position.account is Mapping tài khoản của vị trí tài chính (Accounts Mapping of Fiscal Position)|(account.fiscal.position.account)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.departure.reason is Lý do nghỉ việc (Departure Reason)|(hr.departure.reason)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.template is Mẫu sản phẩm (Product Template)|(product.template)|This mixin adds rating statistics to mail.thread that already support ratings.
sms.composer is Công cụ gửi SMS (Send SMS Wizard)|(sms.composer)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.production.split.line is Chi tiết tách sản lượng (Split Production Detail)|(mrp.production.split.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
loyalty.card.update.balance is Cập nhật điểm thẻ khách hàng thân thiết (Update Loyalty Card Points)|(loyalty.card.update.balance)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
loyalty.rule is Quy tắc khách hàng thân thiết (Loyalty Rule)|(loyalty.rule)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
followup.line is Tiêu chí theo dõi (Follow-up Criteria)|(followup.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.partner is Liên hệ (Contact)|(res.partner)| Purpose of this mixin is to offer two services

      * compute a sanitized phone number based on _phone_get_number_fields.
        It takes first sanitized value, trying each field returned by the
        method (see ``BaseModel._phone_get_number_fields()´´ for more details
        about the usage of this method);
      * compute blacklist state of records. It is based on phone.blacklist
        model and give an easy-to-use field and API to manipulate blacklisted
        records;

    Main API methods

      * ``_phone_set_blacklisted``: set recordset as blacklisted;
      * ``_phone_reset_blacklisted``: reactivate recordset (even if not blacklisted
        this method can be called safely);
    
product.product is Biến thể sản phẩm (Product Variant)|(product.product)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.accrued.orders.wizard is Công cụ đơn hàng dồn tích (Accrued Orders Wizard)|(account.accrued.orders.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sequence.mixin is Số thứ tự tự động (Automatic sequence)|(sequence.mixin)|Mechanism used to have an editable sequence number.

    Be careful of how you use this regarding the prefixes. More info in the
    docstring of _get_last_sequence.
    
account.bank.statement is Sao kê ngân hàng (Bank Statement)|(account.bank.statement)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.production.backorder is Công cụ để đánh dấu là đã hoàn tất hoặc tạo đơn hàng chậm trễ (Wizard to mark as done or create back order)|(mrp.production.backorder)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
_unknown is Không xác định (Unknown)|(_unknown)|
    Abstract model used as a substitute for relational fields with an unknown
    comodel.
    
loyalty.reward is Phần thưởng khách hàng thân thiết (Loyalty Reward)|(loyalty.reward)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.alias.mixin is Email Mixin bí danh (Email Aliases Mixin)|(mail.alias.mixin)| A mixin for models that inherits mail.alias to have a one-to-one relation
    between the model and its alias. 
loyalty.card is Phiếu giảm giá khách hàng thân thiết (Loyalty Coupon)|(loyalty.card)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.inventory.warning is Cảnh báo điều chỉnh tồn kho (Inventory Adjustment Warning)|(stock.inventory.warning)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
ecommerce.order is Đơn hàng TMĐT (Ecommerce Order)|(ecommerce.order)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
loyalty.program is Chương trình khách hàng thân thiết (Loyalty Program)|(loyalty.program)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.order.line is Dòng đơn hàng TMĐT (Ecommerce Order Line)|(ecommerce.order.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.tax is Thuế (Tax)|(account.tax)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.product.product is Sản phẩm TMĐT (EcommerceProductProduct)|(ecommerce.product.product)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
update.product.attribute.value is Cập nhật giá trị thuộc tính sản phẩm (Update product attribute value)|(update.product.attribute.value)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
lazada.config is Cấu hình Lazada (LazadaConfig)|(lazada.config)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lazada.config.line is Dòng cấu hình Lazada (LazadaConfigLine)|(lazada.config.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
shopee.config is Cấu hình Shopee (Shopee Config)|(shopee.config)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
shopee.config.line is Dòng cấu hình Shopee (ShopeeConfigLine)|(shopee.config.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
tiktok.config is Cấu hình TikTok (TiktokConfig)|(tiktok.config)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.shop is Cửa hàng TMĐT (EcommerceShop)|(ecommerce.shop)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.warehouse.mapping is Ánh xạ kho TMĐT (EcommerceWarehouseMapping)|(ecommerce.warehouse.mapping)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.asset.depreciation.line is Dòng khấu hao tài sản (Asset depreciation line)|(account.asset.depreciation.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.asset.asset is Tài sản/Ghi nhận doanh thu (Asset/Revenue Recognition)|(account.asset.asset)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
asset.asset.report is Phân tích tài sản (Assets Analysis)|(asset.asset.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.forecasted_product_product is Báo cáo bổ sung tồn kho (Stock Replenishment Report)|(stock.forecasted_product_product)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
crossovered.budget is Ngân sách (Budget)|(crossovered.budget)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
crossovered.budget.lines is Dòng ngân sách (Budget Line)|(crossovered.budget.lines)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.page is Trang (Page)|(website.page)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.budget.post is Hạng mục ngân sách (Budgetary Position)|(account.budget.post)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.loyalty.coupon.wizard is Khách hàng thân thiết bán hàng - Công cụ áp dụng phiếu giảm giá (Sale Loyalty - Apply Coupon Wizard)|(sale.loyalty.coupon.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.product.report_producttemplatelabel4x12 is Báo cáo nhãn sản phẩm 4x12 (Product Label Report 4x12)|(report.product.report_producttemplatelabel4x12)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
followup.stat is Thống kê theo dõi (Follow-up Statistics)|(followup.stat)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
followup.stat.by.partner is Thống kê theo dõi theo đối tác (Follow-up Statistics by Partner)|(followup.stat.by.partner)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.fiscal.position.tax is Mapping thuế của vị trí tài chính (Tax Mapping of Fiscal Position)|(account.fiscal.position.tax)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
asset.modify is Sửa đổi tài sản (Modify Asset)|(asset.modify)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
web_editor.assets is Assets Utils (Assets Utils)|(web_editor.assets)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
sale.loyalty.reward.wizard is Khách hàng thân thiết bán hàng - Công cụ chọn phần thưởng (Sale Loyalty - Reward Selection Wizard)|(sale.loyalty.reward.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sale.order.coupon.points is Điểm phiếu giảm giá đơn bán hàng - Theo dõi cách đơn bán hàng tác động đến phiếu giảm giá (Sale Order Coupon Points - Keeps track of how a sale order impacts a coupon)|(sale.order.coupon.points)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
followup.print is In theo dõi & Gửi mail cho khách hàng (Print Follow-up & Send Mail to Customers)|(followup.print)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
website.page.properties is Thuộc tính trang (Page Properties)|(website.page.properties)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
analytic.mixin is Mixin phân tích (Analytic Mixin)|(analytic.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
recurring.payment.line is Dòng thanh toán định kỳ (Recurring Payment Line)|(recurring.payment.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
recurring.payment is Thanh toán định kỳ( (Recurring Payment()|(recurring.payment)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
change.lock.date is Thay đổi ngày khóa sổ (Change Lock Date)|(change.lock.date)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.recurring.template is Mẫu định kỳ (Recurring Template)|(account.recurring.template)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.tax.repartition.line is Dòng phân bổ thuế (Tax Repartition Line)|(account.tax.repartition.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
followup.sending.results is Kết quả từ việc gửi các thư và email khác nhau (Results from the sending of the different letters and emails)|(followup.sending.results)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
asset.depreciation.confirmation.wizard is asset.depreciation.confirmation.wizard (asset.depreciation.confirmation.wizard)|(asset.depreciation.confirmation.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.aged.trial.balance is Báo cáo tuổi nợ (Account Aged Trial balance Report)|(account.aged.trial.balance)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.common.account.report is Báo cáo chung tài khoản (Account Common Account Report)|(account.common.account.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
website.seo.metadata is Siêu dữ liệu SEO (SEO metadata)|(website.seo.metadata)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
hr.department is Phòng ban (Department)|(hr.department)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
theme.ir.asset is Asset chủ đề (Theme Asset)|(theme.ir.asset)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.partner.autocomplete.sync is Đồng bộ tự động hoàn thành đối tác (Partner Autocomplete Sync)|(res.partner.autocomplete.sync)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.common.partner.report is Báo cáo chung đối tác (Account Common Partner Report)|(account.common.partner.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
fetchmail.server is Máy chủ nhận thư (Incoming Mail Server)|(fetchmail.server)|Incoming POP/IMAP mail server account
mail.bot is Mail Bot (Mail Bot)|(mail.bot)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.analytic.account is Tài khoản phân tích (Analytic Account)|(account.analytic.account)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.common.report is Báo cáo chung (Account Common Report)|(account.common.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.report.partner.ledger is Sổ chi tiết công nợ (Account Partner Ledger)|(account.report.partner.ledger)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
coupon.share is Tạo đường link áp dụng phiếu giảm giá và chuyển hướng tới trang cụ thể (Create links that apply a coupon and redirect to a specific page)|(coupon.share)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
lot.label.layout is Chọn bố cục trang tính để in nhãn lô (Choose the sheet layout to print lot labels)|(lot.label.layout)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.product.report_pricelist is Báo cáo bảng giá (Pricelist Report)|(report.product.report_pricelist)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
iap.account is Tài khoản IAP (IAP Account)|(iap.account)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.print.journal is In sổ nhật ký (Account Print Journal)|(account.print.journal)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sms.sms is SMS đi (Outgoing SMS)|(sms.sms)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sms.resend.recipient is Gửi lại thông báo (Resend Notification)|(sms.resend.recipient)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
accounting.report is Báo cáo kế toán (Accounting Report)|(accounting.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.common.journal.report is Báo cáo chung sổ nhật ký (Common Journal Report)|(account.common.journal.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.accounting_pdf_reports.report_financial is Báo cáo tài chính (Financial Reports)|(report.accounting_pdf_reports.report_financial)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.report.general.ledger is Báo cáo sổ cái (General Ledger Report)|(account.report.general.ledger)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.accounting_pdf_reports.report_general_ledger is Báo cáo sổ cái (General Ledger Report)|(report.accounting_pdf_reports.report_general_ledger)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
picking.label.type is Chọn in nhãn sản phẩm hay số lô/sê-ri (Choose whether to print product or lot/sn labels)|(picking.label.type)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
digest.digest is Tóm tắt (Digest)|(digest.digest)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.fiscal.year is Năm tài chính (Fiscal Year)|(account.fiscal.year)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.accounting_pdf_reports.report_journal is Báo cáo kiểm toán sổ nhật ký (Journal Audit Report)|(report.accounting_pdf_reports.report_journal)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.tax.report.wizard is Báo cáo thuế (Tax Report)|(account.tax.report.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.accounting_pdf_reports.report_tax is Báo cáo thuế (Tax Report)|(report.accounting_pdf_reports.report_tax)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.balance.report is Báo cáo bảng cân đối thử (Trial Balance Report)|(account.balance.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sms.template.preview is Xem trước mẫu SMS (SMS Template Preview)|(sms.template.preview)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.inventory.conflict is Không nhất quán trong Tồn kho (Conflict in Inventory)|(stock.inventory.conflict)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.accounting_pdf_reports.report_trialbalance is Báo cáo bảng cân đối thử (Trial Balance Report)|(report.accounting_pdf_reports.report_trialbalance)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
queue.job is Queue Job|(queue.job)|Model storing the jobs to be executed.
queue.job.channel is Job Channels|(queue.job.channel)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
queue.job.function is Job Functions|(queue.job.function)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
queue.requeue.job is Wizard to requeue a selection of jobs|(queue.requeue.job)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
queue.jobs.to.done is Set all selected jobs to done|(queue.jobs.to.done)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
queue.jobs.to.cancelled is Cancel all selected jobs|(queue.jobs.to.cancelled)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
bus.listener.mixin is Có thể gửi tin nhắn qua bus.bus (Can send messages via bus.bus)|(bus.listener.mixin)|Allow sending messages related to the current model via as a bus.bus channel.

    The model needs to be allowed as a valid channel for the bus in `_build_bus_channel_list`.
    
report.om_account_followup.report_followup is Báo cáo theo dõi (Report Followup)|(report.om_account_followup.report_followup)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
report.accounting_pdf_reports.report_agedpartnerbalance is Báo cáo tuổi nợ đối tác (Aged Partner Balance Report)|(report.accounting_pdf_reports.report_agedpartnerbalance)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
rating.rating is Đánh giá (Rating)|(rating.rating)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
portal.mixin is Mixin cổng thông tin (Portal Mixin)|(portal.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
portal.wizard is Cấp quyền truy cập cổng thông tin (Grant Portal Access)|(portal.wizard)|
        A wizard to manage the creation/removal of portal users.
    
lunch.product is Sản phẩm ăn trưa (Lunch Product)|(lunch.product)| Products available to order. A product is linked to a specific vendor. 
payment.provider.onboarding.wizard is Công cụ onboarding về nhà cung cấp dịch vụ thanh toán (Payment provider onboarding wizard)|(payment.provider.onboarding.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.om_account_daily_reports.report_cashbook is Cash Book|(report.om_account_daily_reports.report_cashbook)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
report.om_account_daily_reports.report_bankbook is Bank Book|(report.om_account_daily_reports.report_bankbook)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
utm.medium is Phương tiện UTM (UTM Medium)|(utm.medium)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.order.line.fee is Phí dòng đơn hàng TMĐT (Ecommerce Order Line Fee)|(ecommerce.order.line.fee)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.package.item is Mặt hàng gói hàng TMĐT (Ecommerce Package Item)|(ecommerce.package.item)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.package.list is Danh sách gói hàng TMĐT (Ecommerce Package List)|(ecommerce.package.list)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.payment.method is Phương thức thanh toán TMĐT (EcommercePaymentMethod)|(ecommerce.payment.method)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.schedule.log is Nhật ký lịch trình TMĐT (EcommerceScheduleLog)|(ecommerce.schedule.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
order.line.update is Cập nhật dòng đơn hàng (OrderLineUpdate)|(order.line.update)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sms.resend is Gửi lại SMS (SMS Resend)|(sms.resend)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
spreadsheet.mixin is Mixin bảng tính (Spreadsheet mixin)|(spreadsheet.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
snailmail.letter is Thư bưu điện Thư (Snailmail Letter)|(snailmail.letter)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.packaging is Gói hàng (Product Packaging)|(product.packaging)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lunch.topping is Bữa trưa bổ sung (Lunch Extras)|(lunch.topping)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.category is Danh mục sản phẩm (Product Category)|(product.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
tiktok.config.line is Dòng cấu hình TikTok (TiktokConfigLine)|(tiktok.config.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.root is 2 số đầu của mã tài khoản (Account codes first 2 digits)|(account.root)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.putaway.rule is Quy tắc lưu kho (Putaway Rule)|(stock.putaway.rule)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.country.state is Tỉnh/TP/Bang (Country state)|(res.country.state)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.replenish.mixin is Mixin bổ sung sản phẩm (Product Replenish Mixin)|(stock.replenish.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
resource.mixin is Nhân lực Mixin (Resource Mixin)|(resource.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
payment.token is Mã thanh toán (Payment Token)|(payment.token)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lunch.cashmove.report is Báo cáo chuyển tiền (Cashmoves report)|(lunch.cashmove.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.report is Báo cáo kế toán (Accounting Report)|(account.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.removal is Chiến lược xuất kho (Removal Strategy)|(product.removal)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.package.destination is Điểm đến của kiện hàng (Stock Package Destination)|(stock.package.destination)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
lunch.alert is Cảnh báo bữa ăn (Lunch Alert)|(lunch.alert)| Alerts to display during a lunch order. An alert can be specific to a
    given day, weekly or daily. The alert is displayed from start to end hour. 
res.country.group is Nhóm Quốc gia (Country Group)|(res.country.group)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
iap.enrich.api is IAP API Tăng cường lead (IAP Lead Enrichment API)|(iap.enrich.api)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
lunch.cashmove is Chuyển tiền bữa trưa (Lunch Cashmove)|(lunch.cashmove)| Two types of cashmoves: payment (credit) or order (debit) 
stock.storage.category.capacity is Sức chứa danh mục lưu kho (Storage Category Capacity)|(stock.storage.category.capacity)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.partner.category is Từ khoá đối tác (Partner Tags)|(res.partner.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.partner.title is Danh xưng đối tác (Partner Title)|(res.partner.title)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.quantity.history is Lịch sử số lượng tồn kho (Stock Quantity History)|(stock.quantity.history)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.request.count is Tồn kho cần kiểm kho (Stock Request an Inventory Count)|(stock.request.count)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
lunch.location is Địa điểm ăn trưa (Lunch Locations)|(lunch.location)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lunch.order is Đặt bữa trưa (Lunch Order)|(lunch.order)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lunch.product.category is Danh mục Sản phẩm Bữa trưa (Lunch Product Category)|(lunch.product.category)| Category of the product such as pizza, sandwich, pasta, chinese, burger... 
account.report.column is Cột báo cáo kế toán (Accounting Report Column)|(account.report.column)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lunch.supplier is Nhà cung cấp bữa ăn (Lunch Supplier)|(lunch.supplier)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.rules.report is Báo cáo quy tắc tồn kho (Stock Rules report)|(stock.rules.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
iap.autocomplete.api is IAP API tự động hoàn thành đối tác (IAP Partner Autocomplete API)|(iap.autocomplete.api)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
website.track is Trang đã xem (Visited Pages)|(website.track)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.edi.common is Các chức năng chung cho tài liệu EDI: tạo dữ liệu, ràng buộc,... (Common functions for EDI documents: generate the data, the constraints, etc)|(account.edi.common)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.partner.industry is Ngành nghề (Industry)|(res.partner.industry)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.move.send is Account Move Send (Account Move Send)|(account.move.send)| Shared class between the two sending wizards.
    See 'account.move.send.batch.wizard' for multiple invoices sending wizard (async)
    and 'account.move.send.wizard' for single invoice sending wizard (sync).
    
theme.ir.attachment is Chủ đề đính kèm (Theme Attachments)|(theme.ir.attachment)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
theme.ir.ui.view is Giao diện chủ đề (Theme UI View)|(theme.ir.ui.view)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
theme.utils is Chủ đề Utils (Theme Utils)|(theme.utils)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.warn.insufficient.qty is Cảnh báo số lượng không đủ (Warn Insufficient Quantity)|(stock.warn.insufficient.qty)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.move.reversal is Đảo bút toán kế toán (Account Move Reversal)|(account.move.reversal)|
    Account move reversal wizard, it cancel an account move by reversing it.
    
account.secure.entries.wizard is Bảo mật bút toán (Secure Journal Entries)|(account.secure.entries.wizard)|
    This wizard is used to secure journal entries (with a hash)
    
stock.track.line is Dòng theo dõi tồn kho (Stock Track Line)|(stock.track.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
resource.calendar.leaves is Chi tiết nghỉ phép (Resource Time Off Detail)|(resource.calendar.leaves)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
utm.source is Nguồn UTM (UTM Source)|(utm.source)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.replenishment.info is Thông tin bổ sung nhà cung cấp tồn kho (Stock supplier replenishment information)|(stock.replenishment.info)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.edi.xml.cii is Factur-x/XRechnung CII 2.2.0 (Factur-x/XRechnung CII 2.2.0)|(account.edi.xml.cii)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.replenishment.option is Tuỳ chọn bổ sung kho hàng (Stock warehouse replenishment option)|(stock.replenishment.option)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.storage.category is Danh mục lưu kho (Storage Category)|(stock.storage.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
web_tour.tour.step is Bước của tour (Tour's step)|(web_tour.tour.step)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.activity.plan is Kế hoạch hoạt động (Activity Plan)|(mail.activity.plan)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
iap.service is Dịch vụ IAP (IAP Service)|(iap.service)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.edi.xml.ubl_sg is SG BIS Billing 3.0 (SG BIS Billing 3.0)|(account.edi.xml.ubl_sg)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
payment.link.wizard is Tạo liên kết thanh toán bán hàng (Generate Sales Payment Link)|(payment.link.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.move.send.wizard is Account Move Send Wizard (Account Move Send Wizard)|(account.move.send.wizard)|Wizard that handles the sending a single invoice.
ecommerce.statement is E-commerce Statement|(ecommerce.statement)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.setup.bank.manual.config is Thiết lập ngân hàng thủ công (Bank setup manual config)|(account.setup.bank.manual.config)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sms.account.code is Công cụ mã xác minh (SMS Account Verification Code Wizard)|(sms.account.code)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.edi.xml.ubl_nl is SI-UBL 2.0 (NLCIUS) (SI-UBL 2.0 (NLCIUS))|(account.edi.xml.ubl_nl)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
web_tour.tour is Du lịch (Tours)|(web_tour.tour)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.departure.wizard is Công cụ hỗ trợ quá trình nghỉ việc (Departure Wizard)|(hr.departure.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.warn.insufficient.qty.scrap is Cảnh báo số lượng phế phẩm không đủ (Warn Insufficient Scrap Quantity)|(stock.warn.insufficient.qty.scrap)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.edi.xml.ubl_20 is UBL 2.0 (UBL 2.0)|(account.edi.xml.ubl_20)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
utm.source.mixin is Mixin nguồn UTM (UTM Source Mixin)|(utm.source.mixin)|Mixin responsible of generating the name of the source based on the content
    (field defined by _rec_name) of the record (mailing, social post,...).
    
payment.capture.wizard is Công cụ thu hồi thanh toán (Payment Capture Wizard)|(payment.capture.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.merge.wizard.line is Dòng trình hướng dẫn hợp nhất tài khoản (Account merge wizard line)|(account.merge.wizard.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.account.report_invoice_with_payments is Báo cáo tài khoản kèm chi tiết thanh toán (Account report with payment lines)|(report.account.report_invoice_with_payments)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
hr.job is Chức vụ (Job Position)|(hr.job)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
uom.category is Danh mục đơn vị tính sản phẩm (Product UoM Categories)|(uom.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
payment.method is Phương thức thanh toán (Payment Method)|(payment.method)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.statement.line is E-commerce Statement Line|(ecommerce.statement.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.account.report_invoice is Báo cáo tài khoản không kèm chi tiết thanh toán (Account report without payment lines)|(report.account.report_invoice)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.edi.xml.ubl_21 is UBL 2.1 (UBL 2.1)|(account.edi.xml.ubl_21)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.quant.package is Kiện hàng (Packages)|(stock.quant.package)| Packages containing quants and/or other packages 
mail.notification is Thông báo tin nhắn (Message Notifications)|(mail.notification)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.edi.xml.ubl_bis3 is UBL BIS Billing 3.0.12 (UBL BIS Billing 3.0.12)|(account.edi.xml.ubl_bis3)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
snailmail.letter.format.error is Lỗi định dạng khi gửi thư bưu điện (Format Error Sending a Snailmail Letter)|(snailmail.letter.format.error)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
digest.tip is Mẹo tóm tắt (Digest Tips)|(digest.tip)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.skill.level is Cấp độ kỹ năng (Skill Level)|(hr.skill.level)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.employee.skill.log is Lịch sử kỹ năng (Skills History)|(hr.employee.skill.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.stock.label_lot_template_view is Báo cáo nhãn lô (Lot Label Report)|(report.stock.label_lot_template_view)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.users is Người dùng (User)|(res.users)| Update of res.users class
        - add a preference about sending emails about notifications
        - make a new user follow itself
        - add a welcome message
        - add suggestion preference
    
report.stock.report_stock_rule is Báo cáo quy tắc tồn kho (Stock rule report)|(report.stock.report_stock_rule)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.traceability.report is Báo cáo truy xuất nguồn gốc (Traceability Report)|(stock.traceability.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
res.bank is Ngân hàng (Bank)|(res.bank)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website is Trang web (Website)|(website)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.menu is Menu trang web (Website Menu)|(website.menu)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.work.location is Địa điểm làm việc (Work Location)|(hr.work.location)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.partner.bank is Tài khoản Ngân hàng (Bank Accounts)|(res.partner.bank)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.configurator.feature is Tính năng bộ định cấu hình website (Website Configurator Feature)|(website.configurator.feature)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.resume.line.type is Loại của một dòng CV (Type of a resume line)|(hr.resume.line.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.layout is Bố cục bản in (Report Layout)|(report.layout)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.alias.mixin.optional is Mixin bí danh email (sáng) (Email Aliases Mixin (light))|(mail.alias.mixin.optional)| A mixin for models that handles underlying 'mail.alias' records to use
    the mail gateway. Field is not mandatory and its creation is done dynamically
    based on given 'alias_name', allowing to gradually populate the alias table
    without having void aliases as when used with an inherits-like implementation.
    
account.payment.register is Thanh toán (Pay)|(account.payment.register)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.production.split.multi is Công cụ để tách nhiều sản lượng (Wizard to Split Multiple Productions)|(mrp.production.split.multi)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.stock.report_reception is Báo cáo nhập kho (Stock Reception Report)|(report.stock.report_reception)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mrp.unbuild is Lệnh tháo gỡ (Unbuild Order)|(mrp.unbuild)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.move is Điều chuyển tồn kho (Stock Move)|(stock.move)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.warehouse is Kho hàng (Warehouse)|(stock.warehouse)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.config is Cấu hình (Config)|(res.config)| Base classes for new-style configuration items

    Configuration items should inherit from this class, implement
    the execute method (and optionally the cancel one) and have
    their view inherit from the related res_config_view_base view.
    
report.paperformat is Cấu hình định dạng trang giấy (Paper Format Config)|(report.paperformat)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
change.password.own is Người dùng, tính năng hướng dẫn thay đổi trình mật khẩu của mình (User, change own password wizard)|(change.password.own)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.canned.response is Câu trả lời soạn sẵn (Canned Response)|(mail.canned.response)|
    Canned Response: content that will automatically replace the shortcut of your choosing. This content can still be adapted before sending your message.
    
web_editor.converter.test.sub is Web Editor Converter Subtest (Web Editor Converter Subtest)|(web_editor.converter.test.sub)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.employee.public is Nhân viên chung (Public Employee)|(hr.employee.public)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.warn.insufficient.qty.unbuild is Cảnh báo không đủ số lượng tháo gỡ (Warn Insufficient Unbuild Quantity)|(stock.warn.insufficient.qty.unbuild)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.payment is Thanh toán (Payments)|(account.payment)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.consumption.warning is Công cụ dùng trong trường hợp lượng tiêu thụ trong trạng thái cảnh báo/tuyệt đối và nhiều nguyên liệu đã được sử dụng cho LSX hơn (liên quan đến ĐMNL) (Wizard in case of consumption in warning/strict and more component has been used for a MO (related to the bom))|(mrp.consumption.warning)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.package_level is Mức độ đóng gói tồn kho (Stock Package Level)|(stock.package_level)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.scrap.reason.tag is Thẻ lý do loại bỏ hàng (Scrap Reason Tag)|(stock.scrap.reason.tag)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
payment.refund.wizard is Hướng dẫn thanh toán hoàn tiền (Payment Refund Wizard)|(payment.refund.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
hr.employee.base is Người dùng cơ bản (Basic Employee)|(hr.employee.base)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.package.type is Kiểu đóng gói tồn kho (Stock package type)|(stock.package.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.payment.method is Phương thức thanh toán (Payment Methods)|(account.payment.method)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.payment.method.line is Phương thức thanh toán (Payment Methods)|(account.payment.method.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
resource.resource is Nhân lực (Resources)|(resource.resource)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.change.product.qty is Thay đổi số lượng sản phẩm (Change Product Quantity)|(stock.change.product.qty)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.stock.label_product_product_view is Báo cáo nhãn sản phẩm (Product Label Report)|(report.stock.label_product_product_view)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
report.stock.quantity is Báo cáo số lượng tồn kho (Stock Quantity Report)|(report.stock.quantity)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
web_editor.converter.test is Web Editor Converter Test (Web Editor Converter Test)|(web_editor.converter.test)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.lot.report is Báo cáo lô của khách hàng (Customer Lot Report)|(stock.lot.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.forecasted_product_template is Báo cáo bổ sung tồn kho (Stock Replenishment Report)|(stock.forecasted_product_template)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
website.published.mixin is Trang web được xuất bản Mixin (Website Published Mixin)|(website.published.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
website.searchable.mixin is Mixin có thể tìm kiếm website (Website Searchable Mixin)|(website.searchable.mixin)|Mixin to be inherited by all models that need to searchable through website
analytic.plan.fields.mixin is Trường kế hoạch phân tích (Analytic Plan Fields)|(analytic.plan.fields.mixin)| Add one field per analytic plan to the model 
image.mixin is Mixin Hình ảnh (Image Mixin)|(image.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
avatar.mixin is Mixin Hình đại diện (Avatar Mixin)|(avatar.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
format.address.mixin is Định dạng Địa chỉ (Address Format)|(format.address.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.users.apikeys.description is Mô tả Khóa API (API Key Description)|(res.users.apikeys.description)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
res.country is Quốc gia (Country)|(res.country)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.pricelist is Bảng giá (Pricelist)|(product.pricelist)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.cash.rounding is Tài khoản làm tròn tiền mặt (Account Cash Rounding)|(account.cash.rounding)|
    In some countries, we need to be able to make appear on an invoice a rounding line, appearing there only because the
    smallest coinage has been removed from the circulation. For example, in Switzerland invoices have to be rounded to
    0.05 CHF because coins of 0.01 CHF and 0.02 CHF aren't used anymore.
    see https://en.wikipedia.org/wiki/Cash_rounding for more details.
    
stock.quant.relocate is Chuyển địa điểm Số lượng tồn kho (Stock Quantity Relocation)|(stock.quant.relocate)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
product.combo is Combo sản phẩm (Product Combo)|(product.combo)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.combo.item is Mặt hàng combo (Product Combo Item)|(product.combo.item)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
procurement.group is Nhóm mua sắm (Procurement Group)|(procurement.group)|
    The procurement group class is used to group products together
    when computing procurements. (tasks, physical products, ...)

    The goal is that when you have one sales order of several products
    and the products are pulled from the same or several location(s), to keep
    having the moves grouped into pickings that represent the sales order.

    Used in: sales order (to group delivery order lines like the so), pull/push
    rules (to pack like the delivery order), on orderpoints (e.g. for wave picking
    all the similar products together).

    Grouping is made only if the source and the destination is the same.
    Suppose you have 4 lines on a picking from Output where 2 lines will need
    to come from Input (crossdock) and 2 lines coming from Stock -> Output As
    the four will have the same group ids from the SO, the move from input will
    have a stock.picking with 2 grouped lines and the move from stock will have
    2 grouped lines also.

    The name is usually the name of the original document (sales order) or a
    sequence computed if created manually.
    
product.attribute.value is Giá trị thuộc tính (Attribute Value)|(product.attribute.value)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.pricelist.item is Quy tắc bảng giá (Pricelist Rule)|(product.pricelist.item)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.edi.xml.ubl_a_nz is A-NZ BIS Billing 3.0 (A-NZ BIS Billing 3.0)|(account.edi.xml.ubl_a_nz)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
barcode.nomenclature is Phép đặt tên mã vạch (Barcode Nomenclature)|(barcode.nomenclature)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auth_totp.device is Thiết bị Xác thực (Authentication Device)|(auth_totp.device)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.routing.workcenter is Sử dụng khu vực sản xuất (Work Center Usage)|(mrp.routing.workcenter)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.employee.category is Nhóm nhân viên (Employee Category)|(hr.employee.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.manager.department.report is Báo cáo phòng nhân sự (Hr Manager Department Report)|(hr.manager.department.report)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.edi.xml.ubl_de is BIS3 DE (XRechnung) (BIS3 DE (XRechnung))|(account.edi.xml.ubl_de)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.journal is Sổ nhật ký (Journal)|(account.journal)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.resume.line is Dòng CV của một nhân viên (Resume line of an employee)|(hr.resume.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.skill is Kỹ năng (Skill)|(hr.skill)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.edi.xml.ubl_efff is E-FFF (BE) (E-FFF (BE))|(account.edi.xml.ubl_efff)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mrp.workorder is Công đoạn (Work Order)|(mrp.workorder)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.workcenter.productivity is Nhật ký sản xuất của khu vực sản xuất (Workcenter Productivity Log)|(mrp.workcenter.productivity)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.users.identitycheck is Trình kiểm tra mật khẩu (Password Check Wizard)|(res.users.identitycheck)| Wizard used to re-check the user's credentials (password) and eventually
    revoke access to his account to every device he has an active session on.

    Might be useful before the more security-sensitive operations, users might be
    leaving their computer unlocked & unattended. Re-checking credentials mitigates
    some of the risk of a third party using such an unattended device to manipulate
    the account.
    
crm.tag is Thẻ CRM (CRM Tag)|(crm.tag)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
barcodes.barcode_events_mixin is Mixin sự kiện mã vạch (Barcode Event Mixin)|(barcodes.barcode_events_mixin)| Mixin class for objects reacting when a barcode is scanned in their form views
        which contains `<field name="_barcode_scanned" widget="barcode_handler"/>`.
        Models using this mixin must implement the method on_barcode_scanned. It works
        like an onchange and receives the scanned barcode in parameter.
    
change.password.user is Thay đổi mật khẩu người dùng (User, Change Password Wizard)|(change.password.user)| Inherited model to configure users in the change password wizard. 
portal.wizard.user is Cấu hình Người dùng Portal (Portal User Config)|(portal.wizard.user)|
        A model to configure users in the portal wizard.
    
mrp.workcenter.tag is Thêm thẻ cho khu vực sản xuất (Add tag for the workcenter)|(mrp.workcenter.tag)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.skill.type is Loại kỹ năng (Skill Type)|(hr.skill.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
bus.bus is Bus thông tin trao đổi (Communication Bus)|(bus.bus)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.production.backorder.line is Dòng xác nhận đơn hàng chậm trễ (Backorder Confirmation Line)|(mrp.production.backorder.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.bom.byproduct is Phụ phẩm (Byproduct)|(mrp.bom.byproduct)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.lang is Ngôn ngữ (Languages)|(res.lang)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
theme.website.menu is Menu chủ đề Website (Website Theme Menu)|(theme.website.menu)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
onboarding.progress.step is Trình theo dõi bước thao tác Quá trình onboarding (Onboarding Progress Step Tracker)|(onboarding.progress.step)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.users.apikeys is Khóa API của Người dùng (Users API Keys)|(res.users.apikeys)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.employee.skill is Cấp độ kỹ năng cho một nhân viên (Skill level for an employee)|(hr.employee.skill)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.users.deletion is Yêu cầu xóa người dùng (Users Deletion Request)|(res.users.deletion)|User deletion requests.

    Those requests are logged in a different model to keep a trace of this action and the
    deletion is done in a CRON. Indeed, removing a user can be a heavy operation on
    large database (because of create_uid, write_uid on each model, which are not always
    indexed). This model just remove the users added in the deletion queue, remaining code
    must deal with other consideration (archiving, blacklist email...).
    
onboarding.progress is Trình theo dõi Quá trình onboarding (Onboarding Progress Tracker)|(onboarding.progress)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
resource.calendar is Nhân lực thời gian làm việc (Resource Working Time)|(resource.calendar)| Calendar model for a resource. It has

     - attendance_ids: list of resource.calendar.attendance that are a working
                       interval in a given weekday.
     - leave_ids: list of leaves linked to this calendar. A leave can be general
                  or linked to a specific resource, depending on its resource_id.

    All methods in this class use intervals. An interval is a tuple holding
    (begin_datetime, end_datetime). A list of intervals is therefore a list of
    tuples, holding several intervals of work or leaves. 
res.users.apikeys.show is Hiển thị Khóa API (Show API Key)|(res.users.apikeys.show)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.group is Nhóm tài khoản (Account Group)|(account.group)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.workcenter.capacity is Công suất khu vực sản xuất (Work Center Capacity)|(mrp.workcenter.capacity)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
resource.calendar.attendance is Chi tiết Công việc (Work Detail)|(resource.calendar.attendance)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.activity.plan.template is Mẫu kế hoạch hoạt động (Activity plan template)|(mail.activity.plan.template)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.activity.type is Loại hoạt động (Activity Type)|(mail.activity.type)| Activity Types are used to categorize activities. Each type is a different
    kind of activity e.g. call, mail, meeting. An activity can be generic i.e.
    available for all models using activities; or specific to a model in which
    case res_model field should be used. 
stock.orderpoint.snooze is Tạm dừng điểm đặt hàng (Snooze Orderpoint)|(stock.orderpoint.snooze)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.inventory.adjustment.name is Tham chiếu/Lý do điều chỉnh tồn kho (Inventory Adjustment Reference / Reason)|(stock.inventory.adjustment.name)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.base.report_irmodulereference is Báo cáo tham chiếu phân hệ (cơ sở) (Module Reference Report (base))|(report.base.report_irmodulereference)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
decimal.precision is Độ chính xác Thập phân (Decimal Precision)|(decimal.precision)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.consumption.warning.line is Dòng vấn đề lượng sử dụng  (Line of issue consumption)|(mrp.consumption.warning.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.lock_exception is Kế toán Ngoại lệ khoá sổ (Account Lock Exception)|(account.lock_exception)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
bill.to.po.wizard is Hoá đơn thành đơn mua hàng (Bill to Purchase Order)|(bill.to.po.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
discuss.channel is Kênh thảo luận (Discussion Channel)|(discuss.channel)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.hr_skills.report_employee_cv is CV nhân viên (Employee Resume)|(report.hr_skills.report_employee_cv)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
hr.employee.skill.report is Báo cáo kỹ năng nhân viên (Employee Skills Report)|(hr.employee.skill.report)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mrp.workcenter.productivity.loss.type is Hao hụt năng suất công đoạn Sản xuất (MRP Workorder productivity losses)|(mrp.workcenter.productivity.loss.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.batch.produce is Tiến hành sản xuất một loạt LSX (Produce a batch of production order)|(mrp.batch.produce)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
hr.employee.cv.wizard is In CV (Print Resume)|(hr.employee.cv.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
discuss.channel.member is Thành viên kênh (Channel Member)|(discuss.channel.member)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.contract.type is Kiểu Hợp đồng (Contract Type)|(hr.contract.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.thread.cc is Quản lý Email CC (Email CC management)|(mail.thread.cc)| mail_thread model is meant to be inherited by any model that needs to
        act as a discussion topic on which messages can be attached. Public
        methods are prefixed with ``message_`` in order to avoid name
        collisions with methods of the models that will inherit from this class.

        ``mail.thread`` defines fields used to handle and display the
        communication history. ``mail.thread`` also manages followers of
        inheriting classes. All features and expected behavior are managed
        by mail.thread. Widgets has been designed for the 7.0 and following
        versions of Odoo.

        Inheriting classes are not required to implement any method, as the
        default implementation will work for any model. However it is common
        to override at least the ``message_new`` and ``message_update``
        methods (calling ``super``) to add model-specific behavior at
        creation and update of a thread when processing incoming emails.

        Options:
            - _mail_flat_thread: if set to True, all messages without parent_id
                are automatically attached to the first message posted on the
                resource. If set to False, the display of Chatter is done using
                threads, and no parent_id is automatically set.

    MailThread features can be somewhat controlled through context keys :

     - ``mail_create_nosubscribe``: at create or message_post, do not subscribe
       uid to the record thread
     - ``mail_create_nolog``: at create, do not log the automatic '<Document>
       created' message
     - ``mail_notrack``: at create and write, do not perform the value tracking
       creating messages
     - ``tracking_disable``: at create and write, perform no MailThread features
       (auto subscription, tracking, post, ...)
     - ``mail_notify_force_send``: if less than 50 email notifications to send,
       send them directly instead of using the queue; True by default
    
theme.website.page is Trang chủ đề Website (Website Theme Page)|(theme.website.page)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
purchase.bill.line.match is Xem khớp dòng mua hàng và dòng hóa đơn mua hàng (Purchase Line and Vendor Bill line matching view)|(purchase.bill.line.match)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.rewrite is Viết lại trang web (Website rewrite)|(website.rewrite)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
spreadsheet.dashboard.share is Bản sao của trang chủ được chia sẻ (Copy of a shared dashboard)|(spreadsheet.dashboard.share)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.margin is Biên lợi nhuận sản phẩm (Product Margin)|(product.margin)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.production.split is Công cụ để tách một sản lượng (Wizard to Split a Production)|(mrp.production.split)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.workcenter.productivity.loss is Hao hụt năng suất của khu vực sản xuất (Workcenter Productivity Losses)|(mrp.workcenter.productivity.loss)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.location is Địa điểm kiểm kho (Inventory Locations)|(stock.location)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
spreadsheet.dashboard.group is Nhóm trang chủ (Group of dashboards)|(spreadsheet.dashboard.group)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.valuation.layer.revaluation is Mô hình công cụ để tái định giá tồn kho cho một sản phẩm (Wizard model to reavaluate a stock inventory for a product)|(stock.valuation.layer.revaluation)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
purchase.bill.union is Mua hàng & hóa đơn (Purchases & Bills Union)|(purchase.bill.union)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.move.send.batch.wizard is Account Move Send Batch Wizard (Account Move Send Batch Wizard)|(account.move.send.batch.wizard)|Wizard that handles the sending of multiple invoices.
confirm.stock.sms is SMS xác nhận tồn kho (Confirm Stock SMS)|(confirm.stock.sms)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.backorder.confirmation is Xác nhận đơn hàng chậm trễ (Backorder Confirmation)|(stock.backorder.confirmation)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.account is Tài khoản (Account)|(account.account)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.quant is Quant (Quants)|(stock.quant)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.backorder.confirmation.line is Dòng xác nhận đơn hàng chậm trễ (Backorder Confirmation Line)|(stock.backorder.confirmation.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
product.image is Hình ảnh sản phẩm (Product Image)|(product.image)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.employee is Nhân viên (Employee)|(hr.employee)|
    NB: Any field only available on the model hr.employee (i.e. not on the
    hr.employee.public model) should have `groups="hr.group_hr_user"` on its
    definition to avoid being prefetched when the user hasn't access to the
    hr.employee model. Indeed, the prefetch loads the data for all the fields
    that are available according to the group defined on them.
    
product.ribbon is Ruy-băng sản phẩm (Product ribbon)|(product.ribbon)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
expiry.picking.confirmation is Xác nhận ngày hết hạn (Confirm Expiry)|(expiry.picking.confirmation)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.account.wip.accounting.line is Dòng bút toán kế toán được tạo khi ghi sổ bút toán kế toán WIP (Account move line to be created when posting WIP account move)|(mrp.account.wip.accounting.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.track.confirmation is Xác nhận theo dõi tồn kho (Stock Track Confirmation)|(stock.track.confirmation)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.account.wip.accounting is Công cụ ghi sổ bút toán kế toán WIP sản xuất (Wizard to post Manufacturing WIP account move)|(mrp.account.wip.accounting)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sale.payment.provider.onboarding.wizard is Công cụ onboarding về nhà cung cấp dịch vụ thanh toán bán hàng (Sale Payment provider onboarding wizard)|(sale.payment.provider.onboarding.wizard)| Override for the sale quotation onboarding panel. 
mrp.workcenter is Khu vực sản xuất (Work Center)|(mrp.workcenter)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.public.category is Danh mục sản phẩm trên trang web (Website Product Category)|(product.public.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.composer.mixin is Mixin trình soạn thảo email (Mail Composer Mixin)|(mail.composer.mixin)| Mixin used to edit and render some fields used when sending emails or
    notifications based on a mail template.

    Main current purpose is to hide details related to subject and body computation
    and rendering based on a mail.template. It also give the base tools to control
    who is allowed to edit body, notably when dealing with templating language
    like inline_template or qweb.

    It is meant to evolve in a near future with upcoming support of qweb and fine
    grain control of rendering access.
    
account.chart.template is Mẫu hệ thống tài khoản (Account Chart Template)|(account.chart.template)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
sale.pdf.form.field is Các trường biểu mẫu bên trong tài liệu báo giá. (Form fields of inside quotation documents.)|(sale.pdf.form.field)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
quotation.document is Header & Footer của báo giá (Quotation's Headers & Footers)|(quotation.document)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.replenish is Bổ sung sản phẩm (Product Replenish)|(product.replenish)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.valuation.layer is Lớp định giá tồn kho (Stock Valuation Layer)|(stock.valuation.layer)|Stock Valuation Layer
stock.picking.type is Kiểu lấy hàng (Picking Type)|(stock.picking.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.mass.cancel.orders is Huỷ nhiều báo giá (Cancel multiple quotations)|(sale.mass.cancel.orders)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.message.reaction is Phản ứng tin nhắn (Message Reaction)|(mail.message.reaction)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
purchase.edi.xml.ubl_bis3 is UBL BIS 3 Peppol Order transaction 3.4 (UBL BIS 3 Peppol Order transaction 3.4)|(purchase.edi.xml.ubl_bis3)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
purchase.report is Báo cáo mua hàng (Purchase Report)|(purchase.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.message.translation is Dịch tin nhắn (Message Translation)|(mail.message.translation)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.warehouse.orderpoint is Quy tắc tồn kho tối thiểu (Minimum Inventory Rule)|(stock.warehouse.orderpoint)| Defines Minimum stock rules. 
report.mrp.report_bom_structure is Báo cáo tổng quan ĐMNL (BOM Overview Report)|(report.mrp.report_bom_structure)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
vendor.delay.report is Báo cáo mức độ chậm trễ của nhà cung cấp (Vendor Delay Report)|(vendor.delay.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order.discount is Tính năng chiết khấu (Discount Wizard)|(sale.order.discount)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
utm.campaign is Chiến dịch UTM (UTM Campaign)|(utm.campaign)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.message.subtype is Kiểu phụ của tin nhắn (Message subtypes)|(mail.message.subtype)| Class holding subtype definition for messages. Subtypes allow to tune
        the follower subscription, allowing only some subtypes to be pushed
        on the Wall. 
stock.return.picking is Phiếu lấy hàng trả lại (Return Picking)|(stock.return.picking)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.mail is Thư gửi đi (Outgoing Mails)|(mail.mail)| Model holding RFC2822 email messages to send. This model also provides
        facilities to queue and send new email messages.  
sale.edi.common is Các chức năng chung cho các đơn hàng EDI (Common functions for EDI orders)|(sale.edi.common)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
sale.edi.xml.ubl_bis3 is UBL BIS Ordering 3.0 (UBL BIS Ordering 3.0)|(sale.edi.xml.ubl_bis3)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
discuss.voice.metadata is Siêu dữ liệu cho tệp đính kèm dạng giọng nói (Metadata for voice attachments)|(discuss.voice.metadata)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.sale.extra.field is Thông tin bổ sung về thương mại điện tử được hiển thị trên trang sản phẩm (E-Commerce Extra Info Shown on product page)|(website.sale.extra.field)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.attribute is Thuộc tính sản phẩm (Product Attribute)|(product.attribute)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.supplierinfo is Bảng giá nhà cung cấp (Supplier Pricelist)|(product.supplierinfo)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.bom is Định mức nguyên liệu (Bill of Material)|(mrp.bom)| Defines bills of material for a product or a product template 
mrp.production is Lệnh sản xuất (Manufacturing Order)|(mrp.production)| Manufacturing Orders 
sale.order.template.line is Dòng mẫu báo giá (Quotation Template Line)|(sale.order.template.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.tracking.duration.mixin is Mixin để tính thời gian một bản ghi sử dụng cho mỗi giá trị mà trường many2one có thể lấy (Mixin to compute the time a record has spent in each value a many2one field can take)|(mail.tracking.duration.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
sale.report is Báo cáo phân tích bán hàng (Sales Analysis Report)|(sale.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.route is Tuyến cung ứng tồn kho (Inventory Routes)|(stock.route)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order.template is Mẫu báo giá (Quotation Template)|(sale.order.template)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.rule is Quy tắc tồn kho (Stock Rule)|(stock.rule)| A rule describe what a procurement should do; produce, buy, move, ... 
purchase.order is Đơn mua hàng (Purchase Order)|(purchase.order)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.resend.partner is Đối tác có thông tin bổ sung để gửi lại thư (Partner with additional information for mail resend)|(mail.resend.partner)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.move is Bút toán (Journal Entry)|(account.move)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.picking is Lệnh chuyển hàng (Transfer)|(stock.picking)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
base is Cơ sở (Base)|(base)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
crm.team is Bộ phận sales (Sales Team)|(crm.team)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.base.unit is Đơn vị tính cho đơn giá của sản phẩm thương mại điện tử.  (Unit of Measure for price per unit on eCommerce products.)|(website.base.unit)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.snippet.filter is Bộ lọc đoạn mã trang web (Website Snippet Filter)|(website.snippet.filter)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order is Đơn bán hàng (Sales Order)|(sale.order)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
discuss.gif.favorite is Lưu GIF yêu thích từ API Tenor (Save favorite GIF from Tenor API)|(discuss.gif.favorite)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.scheduled.message is Tin nhắn đã lên lịch (Scheduled Message)|(mail.scheduled.message)| Scheduled message model (holds post values generated by the composer to delay the
    posting of the message). Different from mail.message.schedule that posts the message but
    delays the notification process.

    Todo: when adding support for scheduling messages in mass_mail mode, could add a reference to
    the "parent" composer (by making 'mail.compose.message' not transient anymore). This reference
    could then be used to cancel every message scheduled "at the same time" (from one composer),
    and to get the static 'notification parameters' (mail_server_id, auto_delete,...) instead of
    duplicating them for each scheduled message.
    Currently as scheduling is allowed in monocomment only, we don't have duplicates and we only
    have static notification parameters, but some will become dynamic when adding mass_mail support
    such as 'email_from' and 'force_email_lang'.
    
bus.presence is Sự hiện diện của người dùng (User Presence)|(bus.presence)| User Presence
        Its status is 'online', 'away' or 'offline'. This model should be a one2one, but is not
        attached to res_users to avoid database concurrence errors. Since the 'update_presence' method is executed
        at each poll, if the user have multiple opened tabs, concurrence errors can happend, but are 'muted-logged'.
    
res.users.settings.volumes is Cài đặt người dùng Dung lượng (User Settings Volumes)|(res.users.settings.volumes)| Represents the volume of the sound that the user of user_setting_id will receive from partner_id. 
account.analytic.line is Dòng phân tích (Analytic Line)|(account.analytic.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
reset.view.arch.wizard is Trình hỗ trợ Đặt lại Kiến trúc Chế độ xem (Reset View Architecture Wizard)|(reset.view.arch.wizard)| A wizard to compare and reset views architecture. 
phone.blacklist is Danh sách hạn chế số điện thoại (Phone Blacklist)|(phone.blacklist)| Blacklist of phone numbers. Used to avoid sending unwanted messages to people. 
mail.thread.phone is Mixin danh sách hạn chế số điện thoại (Phone Blacklist Mixin)|(mail.thread.phone)| Purpose of this mixin is to offer two services

      * compute a sanitized phone number based on _phone_get_number_fields.
        It takes first sanitized value, trying each field returned by the
        method (see ``BaseModel._phone_get_number_fields()´´ for more details
        about the usage of this method);
      * compute blacklist state of records. It is based on phone.blacklist
        model and give an easy-to-use field and API to manipulate blacklisted
        records;

    Main API methods

      * ``_phone_set_blacklisted``: set recordset as blacklisted;
      * ``_phone_reset_blacklisted``: reactivate recordset (even if not blacklisted
        this method can be called safely);
    
privacy.log is Nhật ký quyền riêng tư (Privacy Log)|(privacy.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
privacy.lookup.wizard is Tính năng tra cứu quyền riêng tư (Privacy Lookup Wizard)|(privacy.lookup.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
privacy.lookup.wizard.line is Dòng tính năng tra cứu quyền riêng tư (Privacy Lookup Wizard Line)|(privacy.lookup.wizard.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
res.config.settings is Cài đặt cấu hình (Config Settings)|(res.config.settings)|inheriting configuration settings.
product.label.layout is Chọn bố cục trang tính để in nhãn (Choose the sheet layout to print the labels)|(product.label.layout)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.activity.mixin is Hoạt động Mixin (Activity Mixin)|(mail.activity.mixin)| Mail Activity Mixin is a mixin class to use if you want to add activities
    management on a model. It works like the mail.thread mixin. It defines
    an activity_ids one2many field toward activities using res_id and res_model_id.
    Various related / computed fields are also added to have a global status of
    activities on documents.

    Activities come with a new JS widget for the form view. It is integrated in the
    Chatter widget although it is a separate widget. It displays activities linked
    to the current record and allow to schedule, edit and mark done activities.

    There is also a kanban widget defined. It defines a small widget to integrate
    in kanban vignettes. It allow to manage activities directly from the kanban
    view. Use widget="kanban_activity" on activitiy_ids field in kanban view to
    use it.

    Some context keys allow to control the mixin behavior. Use those in some
    specific cases like import

     * ``mail_activity_automation_skip``: skip activities automation; it means
       no automated activities will be generated, updated or unlinked, allowing
       to save computation and avoid generating unwanted activities;
    
product.template.attribute.exclusion is Loại trừ thuộc tính mẫu sản phẩm (Product Template Attribute Exclusion)|(product.template.attribute.exclusion)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.users.settings is Cài đặt người dùng (User Settings)|(res.users.settings)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.tag is Thẻ sản phẩm (Product Tag)|(product.tag)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.template.attribute.line is Dòng thuộc tính mẫu sản phẩm (Product Template Attribute Line)|(product.template.attribute.line)|Attributes available on product.template with their selected values in a m2m.
    Used as a configuration model to generate the appropriate product.template.attribute.value
mail.followers is Người theo dõi Tài liệu (Document Followers)|(mail.followers)| mail_followers holds the data related to the follow mechanism inside
    Odoo. Partners can choose to follow documents (records) of any kind
    that inherits from mail.thread. Following documents allow to receive
    notifications for new messages. A subscription is characterized by:

    :param: res_model: model of the followed objects
    :param: res_id: ID of resource (may be 0 for every objects)
    
mail.alias.domain is Miền email (Email Domain)|(mail.alias.domain)| Model alias domains, now company-specific. Alias domains are email
    domains used to receive emails through catchall and bounce aliases, as
    well as using mail.alias records to redirect email replies.

    This replaces ``mail.alias.domain`` configuration parameter use until v16.
    
mail.template.preview is Xem trước Mẫu Email (Email Template Preview)|(mail.template.preview)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
html.field.history.mixin is Lịch sử trường html (Field html History)|(html.field.history.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mail.alias is Bí danh Email (Email Aliases)|(mail.alias)|A Mail Alias is a mapping of an email address with a given Odoo Document
       model. It is used by Odoo's mail gateway when processing incoming emails
       sent to the system. If the recipient address (To) of the message matches
       a Mail Alias, the message will be either processed following the rules
       of that alias. If the message is a reply it will be attached to the
       existing discussion on the corresponding record, otherwise a new
       record of the corresponding model will be created.

       This is meant to be used in combination with a catch-all email configuration
       on the company's mail server, so that as soon as a new mail.alias is
       created, it becomes immediately usable and Odoo will accept email for it.
     
mail.thread is Chủ đề email (Email Thread)|(mail.thread)| mail_thread model is meant to be inherited by any model that needs to
        act as a discussion topic on which messages can be attached. Public
        methods are prefixed with ``message_`` in order to avoid name
        collisions with methods of the models that will inherit from this class.

        ``mail.thread`` defines fields used to handle and display the
        communication history. ``mail.thread`` also manages followers of
        inheriting classes. All features and expected behavior are managed
        by mail.thread. Widgets has been designed for the 7.0 and following
        versions of Odoo.

        Inheriting classes are not required to implement any method, as the
        default implementation will work for any model. However it is common
        to override at least the ``message_new`` and ``message_update``
        methods (calling ``super``) to add model-specific behavior at
        creation and update of a thread when processing incoming emails.

        Options:
            - _mail_flat_thread: if set to True, all messages without parent_id
                are automatically attached to the first message posted on the
                resource. If set to False, the display of Chatter is done using
                threads, and no parent_id is automatically set.

    MailThread features can be somewhat controlled through context keys :

     - ``mail_create_nosubscribe``: at create or message_post, do not subscribe
       uid to the record thread
     - ``mail_create_nolog``: at create, do not log the automatic '<Document>
       created' message
     - ``mail_notrack``: at create and write, do not perform the value tracking
       creating messages
     - ``tracking_disable``: at create and write, perform no MailThread features
       (auto subscription, tracking, post, ...)
     - ``mail_notify_force_send``: if less than 50 email notifications to send,
       send them directly instead of using the queue; True by default
    
mail.compose.message is Trình soạn thảo email (Email composition wizard)|(mail.compose.message)| Generic message composition wizard. You may inherit from this wizard
        at model and view levels to provide specific features.

        The behavior of the wizard depends on the composition_mode field:
        - 'comment': post on a record.
        - 'mass_mail': wizard in mass mailing mode where the mail details can
            contain template placeholders that will be merged with actual data
            before being sent to each recipient.
    
mail.blacklist is Danh sách hạn chế thư (Mail Blacklist)|(mail.blacklist)| Model of blacklisted email addresses to stop sending emails.
mail.thread.blacklist is Mixin danh sách hạn chế thư (Mail Blacklist mixin)|(mail.thread.blacklist)| Mixin that is inherited by all model with opt out. This mixin stores a normalized
    email based on primary_email field.

    A normalized email is considered as :
        - having a left part + @ + a right part (the domain can be without '.something')
        - being lower case
        - having no name before the address. Typically, having no 'Name <>'
    Ex:
        - Formatted Email : 'Name <<EMAIL>>'
        - Normalized Email : '<EMAIL>'

    The primary email field can be specified on the parent model, if it differs from the default one ('email')
    The email_normalized field can than be used on that model to search quickly on emails (by simple comparison
    and not using time consuming regex anymore).

    Using this email_normalized field, blacklist status is computed.

    Mail Thread capabilities are required for this mixin. 
mail.thread.main.attachment is Quản lý tệp đính kèm chính của email (Mail Main Attachment management)|(mail.thread.main.attachment)| Mixin that adds main attachment support to the MailThread class. 
discuss.channel.rtc.session is Phiên RTC email (Mail RTC session)|(discuss.channel.rtc.session)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.template.reset is Đặt lại mẫu email (Mail Template Reset)|(mail.template.reset)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.render.mixin is Mixin kết xuất email (Mail Render Mixin)|(mail.render.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
change.password.wizard is Trình Thay đổi Mật khẩu (Change Password Wizard)|(change.password.wizard)| A wizard to manage the change of users' passwords. 
res.groups is Nhóm truy cập (Access Groups)|(res.groups)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
template.reset.mixin is Mixin đặt lại mẫu (Template Reset Mixin)|(template.reset.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.currency is Tiền tệ (Currency)|(res.currency)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.users.log is Lịch sử tài khoản (Users Log)|(res.users.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.template.attribute.value is Giá trị thuộc tính mẫu sản phẩm (Product Template Attribute Value)|(product.template.attribute.value)|Materialized relationship between attribute values
    and product template generated by the product.template.attribute.line
publisher_warranty.contract is Hợp đồng bảo hành nhà phát hành (Publisher Warranty Contract)|(publisher_warranty.contract)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
portal.share is Chia sẻ cổng thông tin (Portal Sharing)|(portal.share)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
res.currency.rate is Tỷ giá (Currency Rate)|(res.currency.rate)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sms.template.reset is Đặt lại mẫu SMS (SMS Template Reset)|(sms.template.reset)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.product.report_producttemplatelabel4x12noprice is Báo cáo nhãn sản phẩm 4x12 Không có giá (Product Label Report 4x12 No Price)|(report.product.report_producttemplatelabel4x12noprice)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.device.log is Nhật ký thiết bị (Device Log)|(res.device.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.device is Thiết bị (Devices)|(res.device)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.product.report_producttemplatelabel_dymo is Báo cáo nhãn sản phẩm (Product Label Report)|(report.product.report_producttemplatelabel_dymo)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.report.expression is Biểu thức báo cáo kế toán (Accounting Report Expression)|(account.report.expression)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sparse_fields.test is Kiểm thử trường thưa thớt (Sparse fields Test)|(sparse_fields.test)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.report.external.value is Báo cáo kế toán Giá trị bên ngoài (Accounting Report External Value)|(account.report.external.value)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.report.line is Dòng báo cáo kế toán (Accounting Report Line)|(account.report.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.autopost.bills.wizard is Trình hướng dẫn tự động vào sổ hoá đơn (Autopost Bills Wizard)|(account.autopost.bills.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.bank.statement.line is Dòng sao kê ngân hàng (Bank Statement Line)|(account.bank.statement.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.automatic.entry.wizard is Tạo bút toán tự động (Create Automatic Entries)|(account.automatic.entry.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.template is Mẫu email (Email Templates)|(mail.template)|Templates for sending email
sale.order.warning.wizard is SaleOrderWarningWizard|(sale.order.warning.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
barcode.rule is Quy tắc mã vạch (Barcode Rule)|(barcode.rule)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auth_totp.wizard is Công cụ cài đặt 2 yếu tố (2-Factor Setup Wizard)|(auth_totp.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.fiscal.position is Vị trí tài chính (Fiscal Position)|(account.fiscal.position)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.rule is Auditlog - Rule|(auditlog.rule)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.http.session is Auditlog - HTTP User session log|(auditlog.http.session)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.full.reconcile is Đối chiếu toàn bộ (Full Reconcile)|(account.full.reconcile)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.http.request is Auditlog - HTTP request log|(auditlog.http.request)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.log is Auditlog - Log|(auditlog.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.log.line is Auditlog - Log details (fields updated)|(auditlog.log.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.log.line.view is Auditlog - Log details (fields updated)|(auditlog.log.line.view)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.autovacuum is Auditlog - Delete old logs|(auditlog.autovacuum)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
payment.transaction is Giao dịch thanh toán (Payment Transaction)|(payment.transaction)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.message is Tin nhắn (Message)|(mail.message)| Override MailMessage class in order to add a new type: SMS messages.
    Those messages comes with their own notification method, using SMS
    gateway. 
res.company is Công ty (Companies)|(res.company)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.financial.year.op is Số dư đầu kỳ của năm tài chính (Opening Balance of Financial Year)|(account.financial.year.op)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.activity is Hoạt động (Activity)|(mail.activity)| An actual activity to perform. Activities are linked to
    documents using res_id and res_model_id fields. Activities have a deadline
    that can be used in kanban view to display a status. Once done activities
    are unlinked and a message is posted. This message has a new activity_type_id
    field that indicates the activity linked to the message. 
account.partial.reconcile is Đối chiếu một phần (Partial Reconcile)|(account.partial.reconcile)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.reconcile.model.partner.mapping is Mapping đối tác cho mẫu đối chiếu (Partner mapping for reconciliation models)|(account.reconcile.model.partner.mapping)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.payment.term is Điều khoản thanh toán (Payment Terms)|(account.payment.term)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.order.state.timeline is Ecommerce Order State Timeline|(ecommerce.order.state.timeline)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.resequence.wizard is Tạo lại số thứ tự bút toán. (Remake the sequence of Journal Entries.)|(account.resequence.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.reconcile.model.line is Quy tắc dành cho mẫu đối chiếu (Rules for the reconciliation model)|(account.reconcile.model.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.guest is Khách (Guest)|(mail.guest)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.tax.group is Nhóm thuế (Tax Group)|(account.tax.group)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.ice.server is Máy chủ ICE (ICE server)|(mail.ice.server)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.wizard.invite is Đồ thuật Mời (Invite wizard)|(mail.wizard.invite)| Wizard to invite partners (or channels) and make them followers. 
mail.gateway.allowed is Cổng email được cho phép (Mail Gateway Allowed)|(mail.gateway.allowed)|List of trusted email address which won't have the quota restriction.

    The incoming emails have a restriction of the number of records they can
    create with alias, defined by the 2 systems parameters;
    - mail.gateway.loop.minutes
    - mail.gateway.loop.threshold

    But we might have some legit use cases for which we want to receive a ton of emails
    from an automated-source. This model stores those trusted source and this restriction
    won't apply to them.
    
validate.account.move is Xác thực bút toán tài khoản (Validate Account Move)|(validate.account.move)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
uom.uom is Đơn vị tính sản phẩm (Product Unit of Measure)|(uom.uom)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.page.properties.base is Page Properties Base|(website.page.properties.base)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
website.cover_properties.mixin is Bìa Thuộc tính Trang web Mixin (Cover Properties Website Mixin)|(website.cover_properties.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mail.push.device is Thiết bị nhận thông báo đẩy (Push Notification Device)|(mail.push.device)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.push is Thông báo đẩy (Push Notifications)|(mail.push)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.blacklist.remove is Công cụ xoá email khỏi danh sách hạn chế (Remove email from blacklist wizard)|(mail.blacklist.remove)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.message.schedule is Tin nhắn đã lên lịch (Scheduled Messages)|(mail.message.schedule)| Mail message notification schedule queue.

    This model is used to store the mail messages scheduled. So we can
    delay the sending of the notifications. A scheduled date field already
    exists on the <mail.mail> but it does not allow us to delay the sending
    of the <bus.bus> notifications.
    
mail.link.preview is Lưu dữ liệu xem trước liên kết (Store link preview data)|(mail.link.preview)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.analytic.distribution.model is Mô hình phân bổ phân tích (Analytic Distribution Model)|(account.analytic.distribution.model)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.controller.page is Trang mô hình (Model Page)|(website.controller.page)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.visitor is Khách truy cập trang web (Website Visitor)|(website.visitor)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.analytic.plan is Kế hoạch phân tích (Analytic Plans)|(account.analytic.plan)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.analytic.applicability is Các ứng dụng của kế hoạch phân tích (Analytic Plan's Applicabilities)|(account.analytic.applicability)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
