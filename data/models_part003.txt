    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.account.report_invoice_with_payments is <PERSON><PERSON>o c<PERSON>o tài kho<PERSON>n kèm chi tiết thanh toán (Account report with payment lines)|(report.account.report_invoice_with_payments)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
hr.job is <PERSON><PERSON><PERSON> vụ (Job Position)|(hr.job)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
uom.category is <PERSON>h mục đơn vị t<PERSON>h sản phẩm (Product UoM Categories)|(uom.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
payment.method is <PERSON><PERSON><PERSON><PERSON>ứ<PERSON> to<PERSON> (Payment Method)|(payment.method)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.statement.line is E-commerce Statement Line|(ecommerce.statement.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.account.report_invoice is Báo cáo tài khoản không kèm chi tiết thanh toán (Account report without payment lines)|(report.account.report_invoice)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.edi.xml.ubl_21 is UBL 2.1 (UBL 2.1)|(account.edi.xml.ubl_21)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.quant.package is Kiện hàng (Packages)|(stock.quant.package)| Packages containing quants and/or other packages 
mail.notification is Thông báo tin nhắn (Message Notifications)|(mail.notification)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.edi.xml.ubl_bis3 is UBL BIS Billing 3.0.12 (UBL BIS Billing 3.0.12)|(account.edi.xml.ubl_bis3)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
snailmail.letter.format.error is Lỗi định dạng khi gửi thư bưu điện (Format Error Sending a Snailmail Letter)|(snailmail.letter.format.error)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
digest.tip is Mẹo tóm tắt (Digest Tips)|(digest.tip)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.skill.level is Cấp độ kỹ năng (Skill Level)|(hr.skill.level)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.employee.skill.log is Lịch sử kỹ năng (Skills History)|(hr.employee.skill.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.stock.label_lot_template_view is Báo cáo nhãn lô (Lot Label Report)|(report.stock.label_lot_template_view)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.users is Người dùng (User)|(res.users)| Update of res.users class
        - add a preference about sending emails about notifications
        - make a new user follow itself
        - add a welcome message
        - add suggestion preference
    
report.stock.report_stock_rule is Báo cáo quy tắc tồn kho (Stock rule report)|(report.stock.report_stock_rule)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.traceability.report is Báo cáo truy xuất nguồn gốc (Traceability Report)|(stock.traceability.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
res.bank is Ngân hàng (Bank)|(res.bank)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website is Trang web (Website)|(website)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.menu is Menu trang web (Website Menu)|(website.menu)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.work.location is Địa điểm làm việc (Work Location)|(hr.work.location)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.partner.bank is Tài khoản Ngân hàng (Bank Accounts)|(res.partner.bank)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.configurator.feature is Tính năng bộ định cấu hình website (Website Configurator Feature)|(website.configurator.feature)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.resume.line.type is Loại của một dòng CV (Type of a resume line)|(hr.resume.line.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.layout is Bố cục bản in (Report Layout)|(report.layout)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.alias.mixin.optional is Mixin bí danh email (sáng) (Email Aliases Mixin (light))|(mail.alias.mixin.optional)| A mixin for models that handles underlying 'mail.alias' records to use
    the mail gateway. Field is not mandatory and its creation is done dynamically
    based on given 'alias_name', allowing to gradually populate the alias table
    without having void aliases as when used with an inherits-like implementation.
    
account.payment.register is Thanh toán (Pay)|(account.payment.register)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.production.split.multi is Công cụ để tách nhiều sản lượng (Wizard to Split Multiple Productions)|(mrp.production.split.multi)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.stock.report_reception is Báo cáo nhập kho (Stock Reception Report)|(report.stock.report_reception)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mrp.unbuild is Lệnh tháo gỡ (Unbuild Order)|(mrp.unbuild)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.move is Điều chuyển tồn kho (Stock Move)|(stock.move)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.warehouse is Kho hàng (Warehouse)|(stock.warehouse)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.config is Cấu hình (Config)|(res.config)| Base classes for new-style configuration items

    Configuration items should inherit from this class, implement
    the execute method (and optionally the cancel one) and have
    their view inherit from the related res_config_view_base view.
    
report.paperformat is Cấu hình định dạng trang giấy (Paper Format Config)|(report.paperformat)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
change.password.own is Người dùng, tính năng hướng dẫn thay đổi trình mật khẩu của mình (User, change own password wizard)|(change.password.own)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.canned.response is Câu trả lời soạn sẵn (Canned Response)|(mail.canned.response)|
    Canned Response: content that will automatically replace the shortcut of your choosing. This content can still be adapted before sending your message.
    
web_editor.converter.test.sub is Web Editor Converter Subtest (Web Editor Converter Subtest)|(web_editor.converter.test.sub)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.employee.public is Nhân viên chung (Public Employee)|(hr.employee.public)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.warn.insufficient.qty.unbuild is Cảnh báo không đủ số lượng tháo gỡ (Warn Insufficient Unbuild Quantity)|(stock.warn.insufficient.qty.unbuild)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.payment is Thanh toán (Payments)|(account.payment)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.consumption.warning is Công cụ dùng trong trường hợp lượng tiêu thụ trong trạng thái cảnh báo/tuyệt đối và nhiều nguyên liệu đã được sử dụng cho LSX hơn (liên quan đến ĐMNL) (Wizard in case of consumption in warning/strict and more component has been used for a MO (related to the bom))|(mrp.consumption.warning)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.package_level is Mức độ đóng gói tồn kho (Stock Package Level)|(stock.package_level)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.scrap.reason.tag is Thẻ lý do loại bỏ hàng (Scrap Reason Tag)|(stock.scrap.reason.tag)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
payment.refund.wizard is Hướng dẫn thanh toán hoàn tiền (Payment Refund Wizard)|(payment.refund.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
hr.employee.base is Người dùng cơ bản (Basic Employee)|(hr.employee.base)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.package.type is Kiểu đóng gói tồn kho (Stock package type)|(stock.package.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.payment.method is Phương thức thanh toán (Payment Methods)|(account.payment.method)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.payment.method.line is Phương thức thanh toán (Payment Methods)|(account.payment.method.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
resource.resource is Nhân lực (Resources)|(resource.resource)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.change.product.qty is Thay đổi số lượng sản phẩm (Change Product Quantity)|(stock.change.product.qty)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.stock.label_product_product_view is Báo cáo nhãn sản phẩm (Product Label Report)|(report.stock.label_product_product_view)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
report.stock.quantity is Báo cáo số lượng tồn kho (Stock Quantity Report)|(report.stock.quantity)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
web_editor.converter.test is Web Editor Converter Test (Web Editor Converter Test)|(web_editor.converter.test)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.lot.report is Báo cáo lô của khách hàng (Customer Lot Report)|(stock.lot.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.forecasted_product_template is Báo cáo bổ sung tồn kho (Stock Replenishment Report)|(stock.forecasted_product_template)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
website.published.mixin is Trang web được xuất bản Mixin (Website Published Mixin)|(website.published.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
website.searchable.mixin is Mixin có thể tìm kiếm website (Website Searchable Mixin)|(website.searchable.mixin)|Mixin to be inherited by all models that need to searchable through website
analytic.plan.fields.mixin is Trường kế hoạch phân tích (Analytic Plan Fields)|(analytic.plan.fields.mixin)| Add one field per analytic plan to the model 
image.mixin is Mixin Hình ảnh (Image Mixin)|(image.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
avatar.mixin is Mixin Hình đại diện (Avatar Mixin)|(avatar.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
format.address.mixin is Định dạng Địa chỉ (Address Format)|(format.address.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.users.apikeys.description is Mô tả Khóa API (API Key Description)|(res.users.apikeys.description)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
res.country is Quốc gia (Country)|(res.country)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.pricelist is Bảng giá (Pricelist)|(product.pricelist)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.cash.rounding is Tài khoản làm tròn tiền mặt (Account Cash Rounding)|(account.cash.rounding)|
    In some countries, we need to be able to make appear on an invoice a rounding line, appearing there only because the
    smallest coinage has been removed from the circulation. For example, in Switzerland invoices have to be rounded to
    0.05 CHF because coins of 0.01 CHF and 0.02 CHF aren't used anymore.
    see https://en.wikipedia.org/wiki/Cash_rounding for more details.
    
stock.quant.relocate is Chuyển địa điểm Số lượng tồn kho (Stock Quantity Relocation)|(stock.quant.relocate)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
product.combo is Combo sản phẩm (Product Combo)|(product.combo)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.combo.item is Mặt hàng combo (Product Combo Item)|(product.combo.item)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
procurement.group is Nhóm mua sắm (Procurement Group)|(procurement.group)|
    The procurement group class is used to group products together
    when computing procurements. (tasks, physical products, ...)

    The goal is that when you have one sales order of several products
    and the products are pulled from the same or several location(s), to keep
    having the moves grouped into pickings that represent the sales order.

    Used in: sales order (to group delivery order lines like the so), pull/push
    rules (to pack like the delivery order), on orderpoints (e.g. for wave picking
    all the similar products together).

    Grouping is made only if the source and the destination is the same.
    Suppose you have 4 lines on a picking from Output where 2 lines will need
    to come from Input (crossdock) and 2 lines coming from Stock -> Output As
    the four will have the same group ids from the SO, the move from input will
    have a stock.picking with 2 grouped lines and the move from stock will have
    2 grouped lines also.

    The name is usually the name of the original document (sales order) or a
    sequence computed if created manually.
    
product.attribute.value is Giá trị thuộc tính (Attribute Value)|(product.attribute.value)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.pricelist.item is Quy tắc bảng giá (Pricelist Rule)|(product.pricelist.item)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.edi.xml.ubl_a_nz is A-NZ BIS Billing 3.0 (A-NZ BIS Billing 3.0)|(account.edi.xml.ubl_a_nz)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
barcode.nomenclature is Phép đặt tên mã vạch (Barcode Nomenclature)|(barcode.nomenclature)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auth_totp.device is Thiết bị Xác thực (Authentication Device)|(auth_totp.device)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.routing.workcenter is Sử dụng khu vực sản xuất (Work Center Usage)|(mrp.routing.workcenter)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.employee.category is Nhóm nhân viên (Employee Category)|(hr.employee.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.manager.department.report is Báo cáo phòng nhân sự (Hr Manager Department Report)|(hr.manager.department.report)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.edi.xml.ubl_de is BIS3 DE (XRechnung) (BIS3 DE (XRechnung))|(account.edi.xml.ubl_de)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.journal is Sổ nhật ký (Journal)|(account.journal)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.resume.line is Dòng CV của một nhân viên (Resume line of an employee)|(hr.resume.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.skill is Kỹ năng (Skill)|(hr.skill)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.edi.xml.ubl_efff is E-FFF (BE) (E-FFF (BE))|(account.edi.xml.ubl_efff)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mrp.workorder is Công đoạn (Work Order)|(mrp.workorder)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.workcenter.productivity is Nhật ký sản xuất của khu vực sản xuất (Workcenter Productivity Log)|(mrp.workcenter.productivity)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.users.identitycheck is Trình kiểm tra mật khẩu (Password Check Wizard)|(res.users.identitycheck)| Wizard used to re-check the user's credentials (password) and eventually
    revoke access to his account to every device he has an active session on.

    Might be useful before the more security-sensitive operations, users might be
    leaving their computer unlocked & unattended. Re-checking credentials mitigates
    some of the risk of a third party using such an unattended device to manipulate
    the account.
    
crm.tag is Thẻ CRM (CRM Tag)|(crm.tag)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
barcodes.barcode_events_mixin is Mixin sự kiện mã vạch (Barcode Event Mixin)|(barcodes.barcode_events_mixin)| Mixin class for objects reacting when a barcode is scanned in their form views
        which contains `<field name="_barcode_scanned" widget="barcode_handler"/>`.
        Models using this mixin must implement the method on_barcode_scanned. It works
        like an onchange and receives the scanned barcode in parameter.
    
change.password.user is Thay đổi mật khẩu người dùng (User, Change Password Wizard)|(change.password.user)| Inherited model to configure users in the change password wizard. 
portal.wizard.user is Cấu hình Người dùng Portal (Portal User Config)|(portal.wizard.user)|
        A model to configure users in the portal wizard.
    
mrp.workcenter.tag is Thêm thẻ cho khu vực sản xuất (Add tag for the workcenter)|(mrp.workcenter.tag)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.skill.type is Loại kỹ năng (Skill Type)|(hr.skill.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
bus.bus is Bus thông tin trao đổi (Communication Bus)|(bus.bus)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.production.backorder.line is Dòng xác nhận đơn hàng chậm trễ (Backorder Confirmation Line)|(mrp.production.backorder.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.bom.byproduct is Phụ phẩm (Byproduct)|(mrp.bom.byproduct)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.lang is Ngôn ngữ (Languages)|(res.lang)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
theme.website.menu is Menu chủ đề Website (Website Theme Menu)|(theme.website.menu)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
onboarding.progress.step is Trình theo dõi bước thao tác Quá trình onboarding (Onboarding Progress Step Tracker)|(onboarding.progress.step)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.users.apikeys is Khóa API của Người dùng (Users API Keys)|(res.users.apikeys)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.employee.skill is Cấp độ kỹ năng cho một nhân viên (Skill level for an employee)|(hr.employee.skill)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.users.deletion is Yêu cầu xóa người dùng (Users Deletion Request)|(res.users.deletion)|User deletion requests.

    Those requests are logged in a different model to keep a trace of this action and the
    deletion is done in a CRON. Indeed, removing a user can be a heavy operation on
    large database (because of create_uid, write_uid on each model, which are not always
    indexed). This model just remove the users added in the deletion queue, remaining code
    must deal with other consideration (archiving, blacklist email...).
    
onboarding.progress is Trình theo dõi Quá trình onboarding (Onboarding Progress Tracker)|(onboarding.progress)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
resource.calendar is Nhân lực thời gian làm việc (Resource Working Time)|(resource.calendar)| Calendar model for a resource. It has

     - attendance_ids: list of resource.calendar.attendance that are a working
                       interval in a given weekday.
     - leave_ids: list of leaves linked to this calendar. A leave can be general
                  or linked to a specific resource, depending on its resource_id.

    All methods in this class use intervals. An interval is a tuple holding
    (begin_datetime, end_datetime). A list of intervals is therefore a list of
    tuples, holding several intervals of work or leaves. 
res.users.apikeys.show is Hiển thị Khóa API (Show API Key)|(res.users.apikeys.show)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.group is Nhóm tài khoản (Account Group)|(account.group)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.workcenter.capacity is Công suất khu vực sản xuất (Work Center Capacity)|(mrp.workcenter.capacity)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
resource.calendar.attendance is Chi tiết Công việc (Work Detail)|(resource.calendar.attendance)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.activity.plan.template is Mẫu kế hoạch hoạt động (Activity plan template)|(mail.activity.plan.template)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.activity.type is Loại hoạt động (Activity Type)|(mail.activity.type)| Activity Types are used to categorize activities. Each type is a different
    kind of activity e.g. call, mail, meeting. An activity can be generic i.e.
    available for all models using activities; or specific to a model in which
    case res_model field should be used. 
stock.orderpoint.snooze is Tạm dừng điểm đặt hàng (Snooze Orderpoint)|(stock.orderpoint.snooze)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.inventory.adjustment.name is Tham chiếu/Lý do điều chỉnh tồn kho (Inventory Adjustment Reference / Reason)|(stock.inventory.adjustment.name)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.base.report_irmodulereference is Báo cáo tham chiếu phân hệ (cơ sở) (Module Reference Report (base))|(report.base.report_irmodulereference)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
decimal.precision is Độ chính xác Thập phân (Decimal Precision)|(decimal.precision)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.consumption.warning.line is Dòng vấn đề lượng sử dụng  (Line of issue consumption)|(mrp.consumption.warning.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.lock_exception is Kế toán Ngoại lệ khoá sổ (Account Lock Exception)|(account.lock_exception)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
bill.to.po.wizard is Hoá đơn thành đơn mua hàng (Bill to Purchase Order)|(bill.to.po.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
discuss.channel is Kênh thảo luận (Discussion Channel)|(discuss.channel)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.hr_skills.report_employee_cv is CV nhân viên (Employee Resume)|(report.hr_skills.report_employee_cv)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
hr.employee.skill.report is Báo cáo kỹ năng nhân viên (Employee Skills Report)|(hr.employee.skill.report)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mrp.workcenter.productivity.loss.type is Hao hụt năng suất công đoạn Sản xuất (MRP Workorder productivity losses)|(mrp.workcenter.productivity.loss.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.batch.produce is Tiến hành sản xuất một loạt LSX (Produce a batch of production order)|(mrp.batch.produce)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
hr.employee.cv.wizard is In CV (Print Resume)|(hr.employee.cv.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
discuss.channel.member is Thành viên kênh (Channel Member)|(discuss.channel.member)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
