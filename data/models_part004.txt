hr.contract.type is <PERSON><PERSON><PERSON> đồ<PERSON> (Contract Type)|(hr.contract.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.thread.cc is <PERSON><PERSON><PERSON>n lý Email CC (Email CC management)|(mail.thread.cc)| mail_thread model is meant to be inherited by any model that needs to
        act as a discussion topic on which messages can be attached. Public
        methods are prefixed with ``message_`` in order to avoid name
        collisions with methods of the models that will inherit from this class.

        ``mail.thread`` defines fields used to handle and display the
        communication history. ``mail.thread`` also manages followers of
        inheriting classes. All features and expected behavior are managed
        by mail.thread. Widgets has been designed for the 7.0 and following
        versions of Odoo.

        Inheriting classes are not required to implement any method, as the
        default implementation will work for any model. However it is common
        to override at least the ``message_new`` and ``message_update``
        methods (calling ``super``) to add model-specific behavior at
        creation and update of a thread when processing incoming emails.

        Options:
            - _mail_flat_thread: if set to True, all messages without parent_id
                are automatically attached to the first message posted on the
                resource. If set to False, the display of Chatter is done using
                threads, and no parent_id is automatically set.

    MailThread features can be somewhat controlled through context keys :

     - ``mail_create_nosubscribe``: at create or message_post, do not subscribe
       uid to the record thread
     - ``mail_create_nolog``: at create, do not log the automatic '<Document>
       created' message
     - ``mail_notrack``: at create and write, do not perform the value tracking
       creating messages
     - ``tracking_disable``: at create and write, perform no MailThread features
       (auto subscription, tracking, post, ...)
     - ``mail_notify_force_send``: if less than 50 email notifications to send,
       send them directly instead of using the queue; True by default
    
theme.website.page is Trang chủ đề Website (Website Theme Page)|(theme.website.page)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
purchase.bill.line.match is Xem khớp dòng mua hàng và dòng hóa đơn mua hàng (Purchase Line and Vendor Bill line matching view)|(purchase.bill.line.match)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.rewrite is Viết lại trang web (Website rewrite)|(website.rewrite)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
spreadsheet.dashboard.share is Bản sao của trang chủ được chia sẻ (Copy of a shared dashboard)|(spreadsheet.dashboard.share)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.margin is Biên lợi nhuận sản phẩm (Product Margin)|(product.margin)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.production.split is Công cụ để tách một sản lượng (Wizard to Split a Production)|(mrp.production.split)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.workcenter.productivity.loss is Hao hụt năng suất của khu vực sản xuất (Workcenter Productivity Losses)|(mrp.workcenter.productivity.loss)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.location is Địa điểm kiểm kho (Inventory Locations)|(stock.location)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
spreadsheet.dashboard.group is Nhóm trang chủ (Group of dashboards)|(spreadsheet.dashboard.group)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.valuation.layer.revaluation is Mô hình công cụ để tái định giá tồn kho cho một sản phẩm (Wizard model to reavaluate a stock inventory for a product)|(stock.valuation.layer.revaluation)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
purchase.bill.union is Mua hàng & hóa đơn (Purchases & Bills Union)|(purchase.bill.union)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.move.send.batch.wizard is Account Move Send Batch Wizard (Account Move Send Batch Wizard)|(account.move.send.batch.wizard)|Wizard that handles the sending of multiple invoices.
confirm.stock.sms is SMS xác nhận tồn kho (Confirm Stock SMS)|(confirm.stock.sms)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.backorder.confirmation is Xác nhận đơn hàng chậm trễ (Backorder Confirmation)|(stock.backorder.confirmation)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.account is Tài khoản (Account)|(account.account)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.quant is Quant (Quants)|(stock.quant)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.backorder.confirmation.line is Dòng xác nhận đơn hàng chậm trễ (Backorder Confirmation Line)|(stock.backorder.confirmation.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
product.image is Hình ảnh sản phẩm (Product Image)|(product.image)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.employee is Nhân viên (Employee)|(hr.employee)|
    NB: Any field only available on the model hr.employee (i.e. not on the
    hr.employee.public model) should have `groups="hr.group_hr_user"` on its
    definition to avoid being prefetched when the user hasn't access to the
    hr.employee model. Indeed, the prefetch loads the data for all the fields
    that are available according to the group defined on them.
    
product.ribbon is Ruy-băng sản phẩm (Product ribbon)|(product.ribbon)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
expiry.picking.confirmation is Xác nhận ngày hết hạn (Confirm Expiry)|(expiry.picking.confirmation)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.account.wip.accounting.line is Dòng bút toán kế toán được tạo khi ghi sổ bút toán kế toán WIP (Account move line to be created when posting WIP account move)|(mrp.account.wip.accounting.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.track.confirmation is Xác nhận theo dõi tồn kho (Stock Track Confirmation)|(stock.track.confirmation)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.account.wip.accounting is Công cụ ghi sổ bút toán kế toán WIP sản xuất (Wizard to post Manufacturing WIP account move)|(mrp.account.wip.accounting)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sale.payment.provider.onboarding.wizard is Công cụ onboarding về nhà cung cấp dịch vụ thanh toán bán hàng (Sale Payment provider onboarding wizard)|(sale.payment.provider.onboarding.wizard)| Override for the sale quotation onboarding panel. 
mrp.workcenter is Khu vực sản xuất (Work Center)|(mrp.workcenter)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.public.category is Danh mục sản phẩm trên trang web (Website Product Category)|(product.public.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.composer.mixin is Mixin trình soạn thảo email (Mail Composer Mixin)|(mail.composer.mixin)| Mixin used to edit and render some fields used when sending emails or
    notifications based on a mail template.

    Main current purpose is to hide details related to subject and body computation
    and rendering based on a mail.template. It also give the base tools to control
    who is allowed to edit body, notably when dealing with templating language
    like inline_template or qweb.

    It is meant to evolve in a near future with upcoming support of qweb and fine
    grain control of rendering access.
    
account.chart.template is Mẫu hệ thống tài khoản (Account Chart Template)|(account.chart.template)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
sale.pdf.form.field is Các trường biểu mẫu bên trong tài liệu báo giá. (Form fields of inside quotation documents.)|(sale.pdf.form.field)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
quotation.document is Header & Footer của báo giá (Quotation's Headers & Footers)|(quotation.document)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.replenish is Bổ sung sản phẩm (Product Replenish)|(product.replenish)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.valuation.layer is Lớp định giá tồn kho (Stock Valuation Layer)|(stock.valuation.layer)|Stock Valuation Layer
stock.picking.type is Kiểu lấy hàng (Picking Type)|(stock.picking.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.mass.cancel.orders is Huỷ nhiều báo giá (Cancel multiple quotations)|(sale.mass.cancel.orders)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.message.reaction is Phản ứng tin nhắn (Message Reaction)|(mail.message.reaction)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
purchase.edi.xml.ubl_bis3 is UBL BIS 3 Peppol Order transaction 3.4 (UBL BIS 3 Peppol Order transaction 3.4)|(purchase.edi.xml.ubl_bis3)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
purchase.report is Báo cáo mua hàng (Purchase Report)|(purchase.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.message.translation is Dịch tin nhắn (Message Translation)|(mail.message.translation)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.warehouse.orderpoint is Quy tắc tồn kho tối thiểu (Minimum Inventory Rule)|(stock.warehouse.orderpoint)| Defines Minimum stock rules. 
report.mrp.report_bom_structure is Báo cáo tổng quan ĐMNL (BOM Overview Report)|(report.mrp.report_bom_structure)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
vendor.delay.report is Báo cáo mức độ chậm trễ của nhà cung cấp (Vendor Delay Report)|(vendor.delay.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order.discount is Tính năng chiết khấu (Discount Wizard)|(sale.order.discount)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
utm.campaign is Chiến dịch UTM (UTM Campaign)|(utm.campaign)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.message.subtype is Kiểu phụ của tin nhắn (Message subtypes)|(mail.message.subtype)| Class holding subtype definition for messages. Subtypes allow to tune
        the follower subscription, allowing only some subtypes to be pushed
        on the Wall. 
stock.return.picking is Phiếu lấy hàng trả lại (Return Picking)|(stock.return.picking)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.mail is Thư gửi đi (Outgoing Mails)|(mail.mail)| Model holding RFC2822 email messages to send. This model also provides
        facilities to queue and send new email messages.  
sale.edi.common is Các chức năng chung cho các đơn hàng EDI (Common functions for EDI orders)|(sale.edi.common)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
sale.edi.xml.ubl_bis3 is UBL BIS Ordering 3.0 (UBL BIS Ordering 3.0)|(sale.edi.xml.ubl_bis3)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
discuss.voice.metadata is Siêu dữ liệu cho tệp đính kèm dạng giọng nói (Metadata for voice attachments)|(discuss.voice.metadata)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.sale.extra.field is Thông tin bổ sung về thương mại điện tử được hiển thị trên trang sản phẩm (E-Commerce Extra Info Shown on product page)|(website.sale.extra.field)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.attribute is Thuộc tính sản phẩm (Product Attribute)|(product.attribute)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.supplierinfo is Bảng giá nhà cung cấp (Supplier Pricelist)|(product.supplierinfo)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.bom is Định mức nguyên liệu (Bill of Material)|(mrp.bom)| Defines bills of material for a product or a product template 
mrp.production is Lệnh sản xuất (Manufacturing Order)|(mrp.production)| Manufacturing Orders 
sale.order.template.line is Dòng mẫu báo giá (Quotation Template Line)|(sale.order.template.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.tracking.duration.mixin is Mixin để tính thời gian một bản ghi sử dụng cho mỗi giá trị mà trường many2one có thể lấy (Mixin to compute the time a record has spent in each value a many2one field can take)|(mail.tracking.duration.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
sale.report is Báo cáo phân tích bán hàng (Sales Analysis Report)|(sale.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.route is Tuyến cung ứng tồn kho (Inventory Routes)|(stock.route)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order.template is Mẫu báo giá (Quotation Template)|(sale.order.template)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.rule is Quy tắc tồn kho (Stock Rule)|(stock.rule)| A rule describe what a procurement should do; produce, buy, move, ... 
purchase.order is Đơn mua hàng (Purchase Order)|(purchase.order)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.resend.partner is Đối tác có thông tin bổ sung để gửi lại thư (Partner with additional information for mail resend)|(mail.resend.partner)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.move is Bút toán (Journal Entry)|(account.move)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.picking is Lệnh chuyển hàng (Transfer)|(stock.picking)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
base is Cơ sở (Base)|(base)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
crm.team is Bộ phận sales (Sales Team)|(crm.team)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.base.unit is Đơn vị tính cho đơn giá của sản phẩm thương mại điện tử.  (Unit of Measure for price per unit on eCommerce products.)|(website.base.unit)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.snippet.filter is Bộ lọc đoạn mã trang web (Website Snippet Filter)|(website.snippet.filter)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order is Đơn bán hàng (Sales Order)|(sale.order)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
discuss.gif.favorite is Lưu GIF yêu thích từ API Tenor (Save favorite GIF from Tenor API)|(discuss.gif.favorite)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.scheduled.message is Tin nhắn đã lên lịch (Scheduled Message)|(mail.scheduled.message)| Scheduled message model (holds post values generated by the composer to delay the
    posting of the message). Different from mail.message.schedule that posts the message but
    delays the notification process.

    Todo: when adding support for scheduling messages in mass_mail mode, could add a reference to
    the "parent" composer (by making 'mail.compose.message' not transient anymore). This reference
    could then be used to cancel every message scheduled "at the same time" (from one composer),
    and to get the static 'notification parameters' (mail_server_id, auto_delete,...) instead of
    duplicating them for each scheduled message.
    Currently as scheduling is allowed in monocomment only, we don't have duplicates and we only
    have static notification parameters, but some will become dynamic when adding mass_mail support
    such as 'email_from' and 'force_email_lang'.
    
bus.presence is Sự hiện diện của người dùng (User Presence)|(bus.presence)| User Presence
        Its status is 'online', 'away' or 'offline'. This model should be a one2one, but is not
        attached to res_users to avoid database concurrence errors. Since the 'update_presence' method is executed
        at each poll, if the user have multiple opened tabs, concurrence errors can happend, but are 'muted-logged'.
    
res.users.settings.volumes is Cài đặt người dùng Dung lượng (User Settings Volumes)|(res.users.settings.volumes)| Represents the volume of the sound that the user of user_setting_id will receive from partner_id. 
account.analytic.line is Dòng phân tích (Analytic Line)|(account.analytic.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
reset.view.arch.wizard is Trình hỗ trợ Đặt lại Kiến trúc Chế độ xem (Reset View Architecture Wizard)|(reset.view.arch.wizard)| A wizard to compare and reset views architecture. 
phone.blacklist is Danh sách hạn chế số điện thoại (Phone Blacklist)|(phone.blacklist)| Blacklist of phone numbers. Used to avoid sending unwanted messages to people. 
mail.thread.phone is Mixin danh sách hạn chế số điện thoại (Phone Blacklist Mixin)|(mail.thread.phone)| Purpose of this mixin is to offer two services

      * compute a sanitized phone number based on _phone_get_number_fields.
        It takes first sanitized value, trying each field returned by the
        method (see ``BaseModel._phone_get_number_fields()´´ for more details
        about the usage of this method);
      * compute blacklist state of records. It is based on phone.blacklist
        model and give an easy-to-use field and API to manipulate blacklisted
        records;

    Main API methods

      * ``_phone_set_blacklisted``: set recordset as blacklisted;
      * ``_phone_reset_blacklisted``: reactivate recordset (even if not blacklisted
        this method can be called safely);
    
privacy.log is Nhật ký quyền riêng tư (Privacy Log)|(privacy.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
privacy.lookup.wizard is Tính năng tra cứu quyền riêng tư (Privacy Lookup Wizard)|(privacy.lookup.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
privacy.lookup.wizard.line is Dòng tính năng tra cứu quyền riêng tư (Privacy Lookup Wizard Line)|(privacy.lookup.wizard.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
res.config.settings is Cài đặt cấu hình (Config Settings)|(res.config.settings)|inheriting configuration settings.
product.label.layout is Chọn bố cục trang tính để in nhãn (Choose the sheet layout to print the labels)|(product.label.layout)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.activity.mixin is Hoạt động Mixin (Activity Mixin)|(mail.activity.mixin)| Mail Activity Mixin is a mixin class to use if you want to add activities
    management on a model. It works like the mail.thread mixin. It defines
    an activity_ids one2many field toward activities using res_id and res_model_id.
    Various related / computed fields are also added to have a global status of
    activities on documents.

    Activities come with a new JS widget for the form view. It is integrated in the
    Chatter widget although it is a separate widget. It displays activities linked
    to the current record and allow to schedule, edit and mark done activities.

    There is also a kanban widget defined. It defines a small widget to integrate
    in kanban vignettes. It allow to manage activities directly from the kanban
    view. Use widget="kanban_activity" on activitiy_ids field in kanban view to
    use it.

    Some context keys allow to control the mixin behavior. Use those in some
    specific cases like import

     * ``mail_activity_automation_skip``: skip activities automation; it means
       no automated activities will be generated, updated or unlinked, allowing
       to save computation and avoid generating unwanted activities;
    
product.template.attribute.exclusion is Loại trừ thuộc tính mẫu sản phẩm (Product Template Attribute Exclusion)|(product.template.attribute.exclusion)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.users.settings is Cài đặt người dùng (User Settings)|(res.users.settings)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.tag is Thẻ sản phẩm (Product Tag)|(product.tag)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.template.attribute.line is Dòng thuộc tính mẫu sản phẩm (Product Template Attribute Line)|(product.template.attribute.line)|Attributes available on product.template with their selected values in a m2m.
    Used as a configuration model to generate the appropriate product.template.attribute.value
mail.followers is Người theo dõi Tài liệu (Document Followers)|(mail.followers)| mail_followers holds the data related to the follow mechanism inside
    Odoo. Partners can choose to follow documents (records) of any kind
    that inherits from mail.thread. Following documents allow to receive
    notifications for new messages. A subscription is characterized by:

    :param: res_model: model of the followed objects
    :param: res_id: ID of resource (may be 0 for every objects)
    
mail.alias.domain is Miền email (Email Domain)|(mail.alias.domain)| Model alias domains, now company-specific. Alias domains are email
    domains used to receive emails through catchall and bounce aliases, as
    well as using mail.alias records to redirect email replies.

    This replaces ``mail.alias.domain`` configuration parameter use until v16.
    
mail.template.preview is Xem trước Mẫu Email (Email Template Preview)|(mail.template.preview)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
html.field.history.mixin is Lịch sử trường html (Field html History)|(html.field.history.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mail.alias is Bí danh Email (Email Aliases)|(mail.alias)|A Mail Alias is a mapping of an email address with a given Odoo Document
       model. It is used by Odoo's mail gateway when processing incoming emails
       sent to the system. If the recipient address (To) of the message matches
       a Mail Alias, the message will be either processed following the rules
       of that alias. If the message is a reply it will be attached to the
       existing discussion on the corresponding record, otherwise a new
       record of the corresponding model will be created.

       This is meant to be used in combination with a catch-all email configuration
       on the company's mail server, so that as soon as a new mail.alias is
       created, it becomes immediately usable and Odoo will accept email for it.
     
mail.thread is Chủ đề email (Email Thread)|(mail.thread)| mail_thread model is meant to be inherited by any model that needs to
        act as a discussion topic on which messages can be attached. Public
        methods are prefixed with ``message_`` in order to avoid name
        collisions with methods of the models that will inherit from this class.

        ``mail.thread`` defines fields used to handle and display the
        communication history. ``mail.thread`` also manages followers of
        inheriting classes. All features and expected behavior are managed
        by mail.thread. Widgets has been designed for the 7.0 and following
        versions of Odoo.

        Inheriting classes are not required to implement any method, as the
        default implementation will work for any model. However it is common
        to override at least the ``message_new`` and ``message_update``
        methods (calling ``super``) to add model-specific behavior at
        creation and update of a thread when processing incoming emails.

        Options:
            - _mail_flat_thread: if set to True, all messages without parent_id
                are automatically attached to the first message posted on the
                resource. If set to False, the display of Chatter is done using
                threads, and no parent_id is automatically set.

    MailThread features can be somewhat controlled through context keys :

     - ``mail_create_nosubscribe``: at create or message_post, do not subscribe
       uid to the record thread
     - ``mail_create_nolog``: at create, do not log the automatic '<Document>
       created' message
     - ``mail_notrack``: at create and write, do not perform the value tracking
       creating messages
     - ``tracking_disable``: at create and write, perform no MailThread features
       (auto subscription, tracking, post, ...)
     - ``mail_notify_force_send``: if less than 50 email notifications to send,
       send them directly instead of using the queue; True by default
    
mail.compose.message is Trình soạn thảo email (Email composition wizard)|(mail.compose.message)| Generic message composition wizard. You may inherit from this wizard
        at model and view levels to provide specific features.

        The behavior of the wizard depends on the composition_mode field:
        - 'comment': post on a record.
        - 'mass_mail': wizard in mass mailing mode where the mail details can
            contain template placeholders that will be merged with actual data
            before being sent to each recipient.
    
mail.blacklist is Danh sách hạn chế thư (Mail Blacklist)|(mail.blacklist)| Model of blacklisted email addresses to stop sending emails.
mail.thread.blacklist is Mixin danh sách hạn chế thư (Mail Blacklist mixin)|(mail.thread.blacklist)| Mixin that is inherited by all model with opt out. This mixin stores a normalized
    email based on primary_email field.

    A normalized email is considered as :
        - having a left part + @ + a right part (the domain can be without '.something')
        - being lower case
        - having no name before the address. Typically, having no 'Name <>'
    Ex:
        - Formatted Email : 'Name <<EMAIL>>'
        - Normalized Email : '<EMAIL>'

    The primary email field can be specified on the parent model, if it differs from the default one ('email')
    The email_normalized field can than be used on that model to search quickly on emails (by simple comparison
    and not using time consuming regex anymore).

    Using this email_normalized field, blacklist status is computed.

    Mail Thread capabilities are required for this mixin. 
mail.thread.main.attachment is Quản lý tệp đính kèm chính của email (Mail Main Attachment management)|(mail.thread.main.attachment)| Mixin that adds main attachment support to the MailThread class. 
discuss.channel.rtc.session is Phiên RTC email (Mail RTC session)|(discuss.channel.rtc.session)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.template.reset is Đặt lại mẫu email (Mail Template Reset)|(mail.template.reset)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.render.mixin is Mixin kết xuất email (Mail Render Mixin)|(mail.render.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
change.password.wizard is Trình Thay đổi Mật khẩu (Change Password Wizard)|(change.password.wizard)| A wizard to manage the change of users' passwords. 
res.groups is Nhóm truy cập (Access Groups)|(res.groups)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
template.reset.mixin is Mixin đặt lại mẫu (Template Reset Mixin)|(template.reset.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.currency is Tiền tệ (Currency)|(res.currency)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.users.log is Lịch sử tài khoản (Users Log)|(res.users.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.template.attribute.value is Giá trị thuộc tính mẫu sản phẩm (Product Template Attribute Value)|(product.template.attribute.value)|Materialized relationship between attribute values
    and product template generated by the product.template.attribute.line
publisher_warranty.contract is Hợp đồng bảo hành nhà phát hành (Publisher Warranty Contract)|(publisher_warranty.contract)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
portal.share is Chia sẻ cổng thông tin (Portal Sharing)|(portal.share)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
res.currency.rate is Tỷ giá (Currency Rate)|(res.currency.rate)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sms.template.reset is Đặt lại mẫu SMS (SMS Template Reset)|(sms.template.reset)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.product.report_producttemplatelabel4x12noprice is Báo cáo nhãn sản phẩm 4x12 Không có giá (Product Label Report 4x12 No Price)|(report.product.report_producttemplatelabel4x12noprice)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.device.log is Nhật ký thiết bị (Device Log)|(res.device.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.device is Thiết bị (Devices)|(res.device)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.product.report_producttemplatelabel_dymo is Báo cáo nhãn sản phẩm (Product Label Report)|(report.product.report_producttemplatelabel_dymo)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.report.expression is Biểu thức báo cáo kế toán (Accounting Report Expression)|(account.report.expression)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sparse_fields.test is Kiểm thử trường thưa thớt (Sparse fields Test)|(sparse_fields.test)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.report.external.value is Báo cáo kế toán Giá trị bên ngoài (Accounting Report External Value)|(account.report.external.value)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.report.line is Dòng báo cáo kế toán (Accounting Report Line)|(account.report.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.autopost.bills.wizard is Trình hướng dẫn tự động vào sổ hoá đơn (Autopost Bills Wizard)|(account.autopost.bills.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.bank.statement.line is Dòng sao kê ngân hàng (Bank Statement Line)|(account.bank.statement.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

