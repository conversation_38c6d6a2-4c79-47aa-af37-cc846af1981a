account.account.account_type = asset_fixed is <PERSON><PERSON><PERSON> sản cố định (Fixed Assets)
account.journal.invoice_reference_type = partner is D<PERSON><PERSON> trên kh<PERSON>ch hàng (Based on Customer)
account.journal.invoice_reference_type = invoice is Dự<PERSON> trên ho<PERSON> đơ<PERSON> (Based on Invoice)
hr.employee.activity_state = overdue is Overdue
hr.employee.activity_state = today is Today
hr.employee.activity_state = planned is Planned
hr.employee.activity_exception_decoration = warning is <PERSON>ert
hr.employee.activity_exception_decoration = danger is Error
snailmail.letter.error_code = MISSING_REQUIRED_FIELDS is MISSING_REQUIRED_FIELDS (MISSING_REQUIRED_FIELDS)
snailmail.letter.error_code = NO_PRICE_AVAILABLE is NO_PRICE_AVAILABLE (NO_PRICE_AVAILABLE)
account.move.move_type = in_refund is Gi<PERSON>y báo có nhà cung cấp (Vendor Credit Note)
snailmail.letter.state = sent is <PERSON><PERSON>ử<PERSON> (Sent)
account.move.move_type = out_receipt is <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> hà<PERSON> (Sales Receipt)
account.move.move_type = in_receipt is <PERSON><PERSON><PERSON><PERSON> la<PERSON> mua hàng (Purchase Receipt)
account.move.auto_post = no is Không (No)
hr.employee.public.hr_icon_display = presence_absent is Vắng mặt (Absent)
hr.employee.public.hr_presence_state = absent is Vắng mặt (Absent)
hr.employee.hr_icon_display = presence_archive is Đã lưu trữ (Archived)
sale.report.state = cancel is Đã hủy (Cancelled)
delivery.price.rule.variable = quantity is Số lượng (Quantity)
delivery.price.rule.variable_factor = quantity is Số lượng (Quantity)
hr.department.activity_state = overdue is Overdue
hr.department.activity_state = today is Today
hr.department.activity_state = planned is Planned
hr.department.activity_exception_decoration = warning is Alert
hr.department.activity_exception_decoration = danger is Error
mail.notification.failure_type = sms_registration_needed is Yêu cầu đăng ký theo quốc gia cụ thể (Country-specific Registration Required)
hr.employee.hr_presence_state = archive is Đã lưu trữ (Archived)
hr.employee.base.hr_icon_display = presence_archive is Đã lưu trữ (Archived)
hr.employee.base.hr_presence_state = archive is Đã lưu trữ (Archived)
hr.employee.public.hr_presence_state = archive is Đã lưu trữ (Archived)
mrp.workorder.state = ready is Sẵn sàng (Ready)
mrp.routing.workcenter.time_mode = manual is Đặt thời lượng theo cách thủ công (Set duration manually)
mrp.routing.workcenter.worksheet_type = text is Văn bản (Text)
mrp.production.search_date_category = day_2 is Ngày kia (The day after tomorrow)
mail.activity.state = today is Hôm nay (Today)
product.pricelist.item.display_applied_on = 2_product_category is Danh mục (Category)
product.attribute.create_variant = dynamic is Linh động (Dynamically)
product.pricelist.item.compute_price = fixed is Giá cố định (Fixed Price)
product.pricelist.item.compute_price = formula is Công thức (Formula)
product.template.type = consu is Hàng hóa (Goods)
product.attribute.create_variant = no_variant is Không bao giờ (Never)
sms.sms.failure_type = sms_registration_needed is Yêu cầu đăng ký theo quốc gia cụ thể (Country-specific Registration Required)
ir.actions.server.sms_method = sms is SMS (không kèm ghi chú) (SMS (without note))
mail.message.message_type = snailmail is Thư bưu điện (Snailmail)
mail.notification.failure_type = sn_error is Thư bưu điện Lỗi không xác định (Snailmail Unknown Error)
mrp.production.state = to_close is Cần đóng (To Close)
mrp.production.search_date_category = today is Hôm nay (Today)
mail.notification.failure_type = sn_format is Thư bưu điện Lỗi định dạng (Snailmail Format Error)
snailmail.letter.error_code = UNKNOWN_ERROR is UNKNOWN_ERROR (UNKNOWN_ERROR)
payment.provider.state = disabled is Đã vô hiệu (Disabled)
payment.transaction.state = draft is Nháp (Draft)
account.move.auto_post = at_date is Vào ngày (At Date)
delivery.price.rule.variable = volume is Thể tích (Volume)
delivery.price.rule.variable_factor = volume is Thể tích (Volume)
delivery.price.rule.variable = weight is Khối lượng (Weight)
delivery.price.rule.variable_factor = weight is Khối lượng (Weight)
product.attribute.display_type = multi is Nhiều hộp kiểm (Multi-checkbox)
account.move.auto_post = monthly is Hàng tháng (Monthly)
account.move.auto_post = quarterly is Hàng quý (Quarterly)
account.move.auto_post = yearly is Hàng năm (Yearly)
account.move.payment_state = invoicing_legacy is Kế thừa ứng dụng hóa đơn (Invoicing App Legacy)
product.combo.discount_type = amount is Số tiền (Amount)
ecommerce.order.order_type = cancel is HỦY (CANCEL)
ecommerce.product.product.type = combo is Combo (Combo)
ecommerce.schedule.log.state = done is Hoàn tất (Done)
ecommerce.schedule.log.state = draft is Bản nháp (Draft)
ecommerce.product.product.type = consu is Hàng hóa (Goods)
sms.sms.failure_type = sms_country_not_supported is Quốc gia không được hỗ trợ (Country Not Supported)
sms.sms.state = outgoing is Đang chờ (In Queue)
mail.notification.failure_type = sms_credit is Không đủ tín dụng (Insufficient Credit)
account.move.status_in_payment = blocked is Đã bị chặn (Blocked)
res.company.fiscalyear_last_month = 3 is Tháng 3 (March)
hr.employee.work_location_type = office is Văn phòng (Office)
hr.employee.public.hr_icon_display = presence_out_of_working_hour is Ngoài giờ làm việc (Out of Working hours)
hr.employee.public.hr_icon_display = presence_undetermined is Không xác định (Undetermined)
hr.employee.employee_type = worker is Người làm (Worker)
hr.employee.distance_home_work_unit = kilometers is km (km)
stock.picking.state = waiting is Đang chờ hoạt động khác (Waiting Another Operation)
stock.picking.state = assigned is Sẵn sàng (Ready)
stock.picking.state = done is Hoàn tất (Done)
stock.picking.state = cancel is Đã hủy (Cancelled)
stock.warehouse.manufacture_steps = mrp_one_step is Sản xuất (1 bước) (Manufacture (1 step))
ecommerce.order.state = draft is Đơn bán hàng (Draft)
ir.actions.server.activity_date_deadline_range_type = months is Tháng (Months)
ir.actions.server.activity_user_type = specific is Người dùng cụ thể (Specific User)
ir.actions.server.activity_user_type = generic is Người dùng không cố định (dựa trên dữ liệu) (Dynamic User (based on record))
product.label.layout.print_format = 4x7xprice is 4 x 7 kèm giá (4 x 7 with price)
product.attribute.display_type = color is Màu sắc (Color)
product.pricelist.item.compute_price = percentage is Chiết khấu (Discount)
product.attribute.create_variant = always is Tức thời (Instantly)
res.config.settings.product_weight_in_lbs = 0 is Kg (Kilograms)
product.template.service_tracking = no is Không có gì (Nothing)
sms.sms.failure_type = sms_credit is Không đủ tín dụng (Insufficient Credit)
sms.sms.failure_type = sms_number_missing is Thiếu số điện thoại (Missing Number)
hr.employee.distance_home_work_unit = miles is mi (mi)
stock.picking.priority = 0 is Bình thường (Normal)
product.combo.discount_type = percentage is Phần trăm (Percentage)
ecommerce.order.order_type = refund is HOÀN TIỀN (REFUND)
sale.order.state = returned is Đã trả hàng (Returned)
ecommerce.product.product.type = service is Dịch vụ (Service)
ecommerce.order.state = waiting is Đang chờ (Waiting)
account.asset.asset.date_first_depreciation = last_day_period is Dựa vào ngày cuối cùng của kỳ mua hàng (Based on Last Day of Purchase Period)
account.asset.category.date_first_depreciation = last_day_period is Dựa vào ngày cuối cùng của kỳ mua hàng (Based on Last Day of Purchase Period)
crossovered.budget.state = cancel is Đã hủy (Cancelled)
account.asset.asset.state = close is Đóng (Close)
asset.asset.report.state = close is Đóng (Close)
crossovered.budget.state = confirm is Đã xác nhận (Confirmed)
account.recurring.template.recurring_period = days is Ngày (Days)
account.asset.asset.method = degressive is Theo số dư giảm dần (Degressive)
account.asset.category.method = degressive is Theo số dư giảm dần (Degressive)
sale.payment.provider.onboarding.wizard.payment_method = stripe is Thẻ tín dụng và ghi nợ (qua Stripe) (Credit & Debit card (via Stripe))
res.company.fiscalyear_last_month = 4 is Tháng 4 (April)
mrp.routing.workcenter.activity_state = overdue is Overdue
mrp.routing.workcenter.activity_state = today is Today
mrp.routing.workcenter.activity_state = planned is Planned
mrp.routing.workcenter.activity_exception_decoration = warning is Alert
mrp.routing.workcenter.activity_exception_decoration = danger is Error
crossovered.budget.state = done is Hoàn thành (Done)
account.recurring.template.state = done is Hoàn thành (Done)
recurring.payment.state = done is Hoàn thành (Done)
recurring.payment.line.state = done is Hoàn thành (Done)
account.asset.asset.state = draft is Nháp (Draft)
asset.asset.report.state = draft is Nháp (Draft)
crossovered.budget.state = draft is Nháp (Draft)
account.recurring.template.state = draft is Nháp (Draft)
recurring.payment.state = draft is Nháp (Draft)
recurring.payment.line.state = draft is Nháp (Draft)
res.company.fiscalyear_last_month = 5 is Tháng 5 (May)
res.company.fiscalyear_last_month = 6 is Tháng 6 (June)
res.partner.sale_warn = warning is Cảnh báo (Warning)
account.asset.asset.method_time = end is Ngày kết thúc (Ending Date)
account.asset.category.method_time = end is Ngày kết thúc (Ending Date)
account.asset.asset.method = linear is Đường thẳng (Linear)
res.company.fiscalyear_last_month = 7 is Tháng 7 (July)
mrp.production.activity_state = overdue is Overdue
mrp.production.activity_state = today is Today
mrp.production.activity_state = planned is Planned
mrp.production.activity_exception_decoration = warning is Alert
mrp.production.activity_exception_decoration = danger is Error
res.company.fiscalyear_last_month = 8 is Tháng 8 (August)
account.asset.category.method = linear is Đường thẳng (Linear)
account.asset.asset.date_first_depreciation = manual is Thủ công (Manual)
account.asset.category.date_first_depreciation = manual is Thủ công (Mặc định theo ngày mua) (Manual (Defaulted on Purchase Date))
account.recurring.template.recurring_period = months is Tháng (Months)
account.asset.asset.method_time = number is Số bút toán (Number of Entries)
account.asset.category.method_time = number is Số bút toán (Number of Entries)
account.recurring.template.journal_state = posted is Đã vào sổ (Posted)
account.asset.category.type = purchase is Mua hàng: Tài sản (Purchase: Asset)
recurring.payment.payment_type = inbound is Nhận tiền (Receive Money)
account.asset.asset.state = open is Đang hoạt động (Running)
asset.asset.report.state = open is Đang hoạt động (Running)
account.asset.category.type = sale is Bán hàng: Ghi nhận doanh thu (Sale: Revenue Recognition)
stock.move.priority = 1 is Khẩn cấp (Urgent)
stock.move.state = draft is Mới (New)
stock.move.state = waiting is Chờ điều chuyển khác (Waiting Another Move)
rating.mixin.rating_avg_text = ok is Bình thường (Okay)
mail.notification.failure_type = sms_number_missing is Thiếu số điện thoại (Missing Number)
payment.provider.state = enabled is Đã bật (Enabled)
ecommerce.order.order_type = return is Đơn trả (RETURN)
ecommerce.order.order_type = normal is Đơn bán (NORMAL)
recurring.payment.payment_type = outbound is Gửi tiền (Send Money)
mrp.unbuild.activity_state = overdue is Overdue
mrp.unbuild.activity_state = today is Today
mrp.unbuild.activity_state = planned is Planned
mrp.unbuild.activity_exception_decoration = warning is Alert
mrp.unbuild.activity_exception_decoration = danger is Error
account.recurring.template.journal_state = draft is Chưa vào sổ (Un Posted)
crossovered.budget.state = validate is Đã xác thực (Validated)
account.recurring.template.recurring_period = weeks is Tuần (Weeks)
account.recurring.template.recurring_period = years is Năm (Years)
account.common.report.target_move = posted is Tất cả bút toán đã vào sổ (All Posted Entries)
account.common.report.target_move = all is Tất cả bút toán (All Entries)
account.account.account_type = off_balance is Ngoài bảng cân đối (Off-Balance Sheet)
res.company.fiscalyear_last_month = 9 is Tháng 9 (September)
account.common.journal.report.target_move = posted is Tất cả bút toán đã vào sổ (All Posted Entries)
account.common.journal.report.target_move = all is Tất cả bút toán (All Entries)
account.print.journal.target_move = posted is Tất cả bút toán đã vào sổ (All Posted Entries)
account.print.journal.target_move = all is Tất cả bút toán (All Entries)
account.print.journal.sort_selection = date is Ngày (Date)
account.print.journal.sort_selection = move_name is Số bút toán (Journal Entry Number)
accounting.report.target_move = posted is Tất cả bút toán đã vào sổ (All Posted Entries)
accounting.report.target_move = all is Tất cả bút toán (All Entries)
accounting.report.filter_cmp = filter_no is Không có bộ lọc (No Filters)
accounting.report.filter_cmp = filter_date is Ngày (Date)
account.common.partner.report.target_move = posted is Tất cả bút toán đã vào sổ (All Posted Entries)
account.common.partner.report.target_move = all is Tất cả bút toán (All Entries)
account.common.partner.report.result_selection = customer is Tài khoản phải thu (Receivable Accounts)
account.common.partner.report.result_selection = supplier is Tài khoản phải trả (Payable Accounts)
account.common.partner.report.result_selection = customer_supplier is Tài khoản phải thu và phải trả (Receivable and Payable Accounts)
account.common.account.report.target_move = posted is Tất cả bút toán đã vào sổ (All Posted Entries)
account.common.account.report.target_move = all is Tất cả bút toán (All Entries)
account.common.account.report.display_account = all is Tất cả (All)
account.common.account.report.display_account = movement is Có phát sinh (With movements)
account.common.account.report.display_account = not_zero is Có số dư khác 0 (With balance is not equal to 0)
account.report.partner.ledger.target_move = posted is Tất cả bút toán đã vào sổ (All Posted Entries)
account.report.partner.ledger.target_move = all is Tất cả bút toán (All Entries)
account.report.partner.ledger.result_selection = customer is Tài khoản phải thu (Receivable Accounts)
account.report.partner.ledger.result_selection = supplier is Tài khoản phải trả (Payable Accounts)
account.report.partner.ledger.result_selection = customer_supplier is Tài khoản phải thu và phải trả (Receivable and Payable Accounts)
mail.mail.failure_type = mail_email_invalid is Địa chỉ email không hợp lệ (Invalid email address)
product.pricelist.item.base = standard_price is Chi phí (Cost)
res.company.fiscalyear_last_month = 11 is Tháng 11 (November)
sale.report.line_invoice_status = upselling is Cơ hội bán thêm (Upselling Opportunity)
product.template.sale_line_warn = warning is Cảnh báo (Warning)
stock.move.state = confirmed is Đang chờ hàng (Waiting Availability)
res.config.settings.product_volume_volume_in_cubic_feet = 1 is Feet khối (Cubic Feet)
mail.notification.failure_type = sms_not_delivered is Chưa giao (Not Delivered)
res.company.terms_type = plain is Thêm ghi chú (Add a Note)
res.company.terms_type = html is Thêm liên kết đến một trang web (Add a link to a Web Page)
ir.actions.server.sms_method = note is Chỉ ghi chú (Note only)
mail.notification.failure_type = sn_fields is Thư bưu điện Thiếu trường yêu cầu (Snailmail Missing Required Fields)
stock.picking.type.mrp_product_label_to_print = zpl is ZPL (ZPL)
mail.notification.failure_type = sn_price is Thư bưu điện Không có giá (Snailmail No Price Available)
res.partner.invoice_warn = no-message is Không có tin nhắn (No Message)
stock.picking.type.code = incoming is Phiếu nhập kho (Receipt)
mail.alias.alias_status = invalid is Không hợp lệ (Invalid)
product.template.purchase_line_warn = no-message is Không có tin nhắn (No Message)
res.partner.invoice_warn = block is Chặn tin nhắn (Blocking Message)
res.partner.invoice_sending_method = manual is Tải xuống (Download)
res.partner.invoice_sending_method = email is bằng email (by Email)
res.partner.autopost_bills = always is Luôn luôn (Always)
res.partner.autopost_bills = ask is Hỏi sau 3 lần xác thực mà không có chỉnh sửa (Ask after 3 validations without edits)
res.partner.autopost_bills = never is Không bao giờ (Never)
account.account.tag.applicability = accounts is Tài khoản (Accounts)
res.company.quick_edit_mode = out_invoices is Hóa đơn bán hàng (Customer Invoices)
mail.activity.mixin.activity_state = overdue is Quá hạn (Overdue)
mail.activity.mixin.activity_state = today is Hôm nay (Today)
res.config.settings.product_weight_in_lbs = 1 is Pound (Pounds)
mail.notification.failure_type = sn_trial is Thư bưu điện Lỗi dùng thử (Snailmail Trial Error)
payment.provider.support_manual_capture = full_only is Full Only (Full Only)
res.company.payment_onboarding_payment_method = manual is Thủ công (Manual)
payment.provider.code = none is Không có nhà cung cấp được đặt (No Provider Set)
res.company.quick_edit_mode = in_invoices is Hoá đơn mua hàng (Vendor Bills)
barcode.rule.type = pack_date is Ngày đóng hàng (Pack Date)
stock.location.usage = production is Sản xuất (Production)
stock.location.usage = transit is Địa điểm trung chuyển (Transit Location)
stock.picking.type.code = outgoing is Giao hàng (Delivery)
mail.activity.mixin.activity_state = planned is Đã lên kế hoạch (Planned)
sms.sms.state = process is Đang xử lý (Processing)
mail.notification.failure_type = sms_rejected is Bị từ chối (Rejected)
mail.notification.failure_type = sms_acc is Tài khoản chưa đăng ký (Unregistered Account)
mail.message.message_type = sms is SMS (SMS)
mail.notification.notification_type = sms is SMS (SMS)
ir.actions.server.sms_method = comment is SMS (kèm ghi chú) (SMS (with note))
snailmail.letter.error_code = TRIAL_ERROR is TRIAL_ERROR (TRIAL_ERROR)
sms.sms.failure_type = sms_acc is Tài khoản chưa đăng ký (Unregistered Account)
payment.transaction.operation = online_redirect is Thanh toán online với chuyển hướng (Online payment with redirection)
res.company.payment_onboarding_payment_method = other is Khác (Other)
payment.provider.support_manual_capture = partial is Một phần (Partial)
payment.provider.onboarding.wizard.payment_method = paypal is PayPal (PayPal)
res.company.payment_onboarding_payment_method = paypal is PayPal (PayPal)
payment.transaction.operation = refund is Hoàn tiền (Refund)
payment.provider.support_refund = none is Không hỗ trợ (Unsupported)
stock.picking.type.code = internal is Lệnh chuyển hàng nội bộ (Internal Transfer)
stock.picking.type.reservation_method = at_confirm is Khi xác nhận (At Confirmation)
stock.picking.type.reservation_method = manual is Thủ công (Manually)
stock.picking.type.reservation_method = by_date is Trước ngày theo kế hoạch (Before scheduled date)
res.partner.peppol_eas = 0130 is 0130 - Đoàn uỷ viên của Ủy ban Châu Âu (0130 - Directorates of the European Commission)
res.partner.peppol_eas = 0142 is 0142 - Mã định danh đối tượng SECETI (0142 - SECETI Object Identifiers)
res.partner.peppol_eas = 0151 is 0151 - Cơ chế mã số doanh nghiệp Úc (ABN) (0151 - Australian Business Number (ABN) Scheme)
res.partner.peppol_eas = 0183 is 0183 - Mã số doanh nghiệp duy nhất của Thụy Sĩ (UIDB) (0183 - Swiss Unique Business Identification Number (UIDB))
mrp.production.components_availability_state = available is Có hàng (Available)
stock.picking.type.lot_label_format = 4x12_lots is 4 x 12 - Một trên mỗi số lô/sê-ri (4 x 12 - One per lot/SN)
stock.picking.type.lot_label_format = 4x12_units is 4 x 12 - Một trên mỗi đơn vị (4 x 12 - One per unit)
stock.picking.type.lot_label_format = zpl_lots is Nhãn ZPL - Một trên mỗi số lô/sê-ri (ZPL Labels - One per lot/SN)
stock.picking.type.package_label_to_print = pdf is PDF (PDF)
mail.alias.alias_status = valid is Hợp lệ (Valid)
fetchmail.server.server_type = pop is Địa chỉ máy chủ POP (POP Server)
mail.activity.type.delay_from = current_date is sau ngày xác nhận (after completion date)
mail.activity.type.delay_from = previous_activity is sau thời hạn hoạt động trước (after previous activity deadline)
mail.activity.type.decoration_type = warning is Cảnh báo (Alert)
mail.activity.type.decoration_type = danger is Lỗi (Error)
mail.activity.type.chaining_type = suggest is Gợi ý hoạt động kế tiếp (Suggest Next Activity)
mail.activity.type.chaining_type = trigger is Điều kiện cho tác vụ kế tiếp (Trigger Next Activity)
mail.activity.type.category = default is Không (None)
product.attribute.display_type = pills is Viên  (Pills)
product.pricelist.item.applied_on = 1_product is Sản phẩm (Product)
product.pricelist.item.display_applied_on = 1_product is Sản phẩm (Product)
ir.actions.server.state = sms is Gửi SMS (Send SMS)
payment.provider.state = test is Chế độ kiểm thử (Test Mode)
payment.method.support_refund = none is Không hỗ trợ (Unsupported)
account.account.account_type = asset_receivable is Phải thu (Receivable)
account.account.account_type = asset_cash is Ngân hàng và tiền mặt (Bank and Cash)
account.account.account_type = asset_current is Tài sản lưu động (Current Assets)
account.account.account_type = asset_non_current is Tài sản dài hạn (Non-current Assets)
account.account.account_type = asset_prepayments is Khoản trả trước (Prepayments)
account.account.account_type = liability_payable is Phải trả (Payable)
account.account.account_type = liability_credit_card is Thẻ (Credit Card)
account.account.account_type = liability_current is Nợ ngắn hạn (Current Liabilities)
account.account.account_type = liability_non_current is Nợ dài hạn (Non-current Liabilities)
stock.picking.type.package_label_to_print = zpl is ZPL (ZPL)
stock.picking.type.create_backorder = ask is Hỏi (Ask)
stock.picking.type.create_backorder = always is Luôn luôn (Always)
stock.picking.type.create_backorder = never is Không bao giờ (Never)
stock.picking.type.move_type = direct is Càng sớm càng tốt (As soon as possible)
stock.scrap.state = draft is Nháp (Draft)
stock.scrap.state = done is Hoàn tất (Done)
auditlog.rule.log_type = full is Full log
auditlog.rule.log_type = fast is Fast log
auditlog.rule.state = subscribed is Subscribed
auditlog.log.log_type = full is Full log
auditlog.log.log_type = fast is Fast log
auditlog.rule.state = draft is Dự thảo (Draft)
purchase.order.order_type = purchase is Purchase
purchase.order.order_type = return is Return
ecommerce.order.order_status = UNPAID is UNPAID
ecommerce.order.order_status = READY_TO_SHIP is READY_TO_SHIP
ecommerce.order.order_status = PROCESSED is PROCESSED
ecommerce.order.order_status = RETRY_SHIP is RETRY_SHIP
ecommerce.order.order_status = SHIPPED is SHIPPED
ecommerce.order.order_status = TO_CONFIRM_RECEIVE is TO_CONFIRM_RECEIVE
ecommerce.order.order_status = IN_CANCEL is IN_CANCEL
ecommerce.order.order_status = CANCELLED is CANCELLED
ecommerce.order.order_status = TO_RETURN is TO_RETURN
ecommerce.order.order_status = COMPLETED is COMPLETED
ecommerce.order.checkout_shipping_carrier = standard is Standard Delivery
ecommerce.order.checkout_shipping_carrier = fast is Fast Delivery
ecommerce.order.state = done is Hoàn tất (Done)
ecommerce.order.state = cancelled is Đã hủy (Cancelled)
ecommerce.order.order_status = UNKNOWN is UNKNOWN
ecommerce.order.activity_state = overdue is Overdue
ecommerce.order.activity_state = today is Today
ecommerce.order.activity_state = planned is Planned
ecommerce.order.activity_exception_decoration = warning is Alert
ecommerce.order.activity_exception_decoration = danger is Error
ecommerce.order.order_status = ACCEPTED is ACCEPTED
res.partner.peppol_eas = 0201 is 0201 - Codice Univoco Unità Organizzativa iPA (0201 - Codice Univoco Unità Organizzativa iPA)
product.document.attached_on_mrp = hidden is Ẩn (Hidden)
res.partner.purchase_warn = no-message is Không có tin nhắn (No Message)
stock.picking.type.move_type = one is Khi tất cả sản phẩm đã sẵn sàng (When all products are ready)
stock.picking.move_type = direct is Càng sớm càng tốt (As soon as possible)
stock.picking.search_date_category = today is Hôm nay (Today)
stock.picking.search_date_category = day_1 is Ngày mai (Tomorrow)
res.partner.peppol_eas = 0188 is 0188 - Mã số doanh nghiệp của Hệ thống mã số thuế và an sinh xã hội (0188 - Corporate Number of The Social Security and Tax Number System)
res.partner.peppol_eas = 0190 is 0190 - Mã số tổ chức của Hà Lan (0190 - Dutch Originator's Identification Number)
res.partner.peppol_eas = 0191 is 0191 - Trung tâm Đăng ký và Hệ thống Thông tin Bộ Tư pháp (0191 - Centre of Registers and Information Systems of the Ministry of Justice)
res.partner.peppol_eas = 0192 is 0192 - Enhetsregisteret ved Bronnoysundregisterne (0192 - Enhetsregisteret ved Bronnoysundregisterne)
res.partner.peppol_eas = 0193 is 0193 - Mã số tổ chức UBL.BE (0193 - UBL.BE party identifier)
account.account.account_type = equity is Vốn chủ sở hữu (Equity)
account.account.account_type = equity_unaffected is Thu nhập năm hiện tại (Current Year Earnings)
purchase.order.priority = 0 is Bình thường (Normal)
account.account.account_type = income is Thu nhập (Income)
account.account.account_type = income_other is Thu nhập khác (Other Income)
account.account.account_type = expense is Chi phí (Expenses)
account.account.account_type = expense_depreciation is Khấu hao (Depreciation)
account.account.account_type = expense_direct_cost is Chi phí doanh thu (Cost of Revenue)
account.account.internal_group = equity is Vốn chủ sở hữu (Equity)
ecommerce.order.order_status = REQUESTED is REQUESTED
ecommerce.order.order_status = PROCESSING is PROCESSING
ecommerce.order.order_status = JUDGING is JUDGING
ecommerce.order.order_status = SELLER_DISPUTE is SELLER_DISPUTE
ecommerce.order.order_status = CLOSED is CLOSED
account.account.internal_group = asset is Tài sản (Asset)
account.account.internal_group = liability is Nợ (Liability)
account.account.internal_group = income is Thu nhập (Income)
res.company.quick_edit_mode = out_and_in_invoices is Hóa đơn bán hàng và hóa đơn mua hàng (Customer Invoices and Vendor Bills)
res.company.account_price_include = tax_included is Bao gồm thuế (Tax Included)
res.partner.peppol_eas = 0195 is 0195 - Mã số UEN của Singapore (0195 - Singapore UEN identifier)
res.partner.peppol_eas = 0196 is 0196 - Kennitala - ID pháp lý cho cá nhân và pháp nhân của Iceland (0196 - Kennitala - Iceland legal id for individuals and legal entities)
res.partner.peppol_eas = 0198 is 0198 - ERSTORG (0198 - ERSTORG)
res.partner.peppol_eas = 0199 is 0199 - Mã phân định pháp nhân (LEI) (0199 - Legal Entity Identifier (LEI))
product.template.uom_type = reference is Reference
product.template.uom_type = bigger is Bigger
product.template.uom_type = smaller is Smaller
ecommerce.order.state = packed is Đóng gói (Packed)
ecommerce.order.state = shipped is Giao hàng (Shipped)
fetchmail.server.server_type = local is Máy chủ cục bộ (Local Server)
mail.notification.notification_type = inbox is Hộp thư đến (Inbox)
mail.notification.failure_type = mail_email_missing is Thiếu địa chỉ email (Missing email address)
mail.activity.state = overdue is Quá hạn (Overdue)
sms.composer.composition_mode = mass is Gửi SMS hàng loạt (Send SMS in batch)
sms.composer.composition_mode = numbers is Gửi đến số (Send to numbers)
sms.sms.state = pending is Đã gửi (Sent)
mail.notification.failure_type = sms_server is Lỗi máy chủ (Server Error)
sms.sms.failure_type = sms_server is Lỗi máy chủ (Server Error)
sms.sms.failure_type = unknown is Lỗi không xác định (Unknown error)
snailmail.letter.state = error is Lỗi (Error)
snailmail.letter.error_code = FORMAT_ERROR is FORMAT_ERROR (FORMAT_ERROR)
snailmail.letter.state = pending is Đang chờ (In Queue)
website.page.properties.redirect_type = 301 is 301 Đã chuyển vĩnh viễn (301 Moved permanently)
stock.picking.search_date_category = day_2 is Ngày kia (The day after tomorrow)
stock.picking.search_date_category = after is Sau (After)
stock.quant.package.package_use = disposable is Hộp dùng một lần (Disposable Box)
stock.warehouse.reception_steps = one_step is Nhận hàng và lưu kho (1 bước) (Receive and Store (1 step))
web_editor.converter.test.selection_str = D is La réponse D
stock.quant.package.package_use = reusable is Hộp có thể tái sử dụng (Reusable Box)
product.category.packaging_reserve_method = full is Chỉ dự trữ toàn bộ gói hàng (Reserve Only Full Packagings)
stock.package_level.state = assigned is Đã dự trữ (Reserved)
stock.package_level.state = new is Mới (New)
account.account.internal_group = expense is Chi phí (Expense)
account.move.status_in_payment = in_payment is Đang thanh toán (In Payment)
account.move.status_in_payment = paid is Đã thanh toán (Paid)
account.move.status_in_payment = partial is Đã thanh toán một phần (Partially Paid)
res.partner.peppol_eas = 0202 is 0202 - Indirizzo di Posta Elettronica Certificata (0202 - Indirizzo di Posta Elettronica Certificata)
res.partner.peppol_eas = 0204 is 0204 - Leitweg-ID (0204 - Leitweg-ID)
account.move.status_in_payment = reversed is Đã đảo (Reversed)
res.company.fiscalyear_last_month = 10 is Tháng 10 (October)
res.company.fiscalyear_last_month = 12 is Tháng 12 (December)
res.company.tax_calculation_rounding_method = round_per_line is Làm tròn từng dòng (Round per Line)
res.partner.peppol_eas = 0208 is 0208 - Numero d'entreprise / ondernemingsnummer / Unternehmensnummer (0208 - Numero d'entreprise / ondernemingsnummer / Unternehmensnummer)
res.partner.peppol_eas = 9919 is 9919 - Kennziffer des Unternehmensregisters (9919 - Kennziffer des Unternehmensregisters)
res.partner.peppol_eas = 9920 is 9920 - Agencia Española de Administración Tributaria (9920 - Agencia Española de Administración Tributaria)
res.partner.peppol_eas = 9922 is 9922 - Mã số thuế GTGT của Andorra (9922 - Andorra VAT number)
res.partner.peppol_eas = 9926 is 9926 - Mã số thuế GTGT của Bulgaria (9926 - Bulgaria VAT number)
hr.employee.public.hr_icon_display = presence_archive is Đã lưu trữ (Archived)
mail.alias.alias_contact = employees is Nhân viên đã xác thực (Authenticated Employees)
hr.employee.certificate = bachelor is Cử nhân (Bachelor)
mail.activity.plan.template.responsible_type = coach is Người huấn luyện (Coach)
hr.employee.employee_type = contractor is Người mua (Contractor)
hr.employee.certificate = doctor is Tiến sĩ (Doctor)
hr.employee.employee_type = employee is Nhân viên (Employee)
stock.package_level.state = cancel is Đã hủy (Cancelled)
website.rewrite.redirect_type = 301 is 301 Đã chuyển vĩnh viễn (301 Moved permanently)
website.page.properties.redirect_type = 302 is 302 Đã chuyển tạm thời (302 Moved temporarily)
website.rewrite.redirect_type = 302 is 302 Đã chuyển tạm thời (302 Moved temporarily)
website.rewrite.redirect_type = 308 is 308 Chuyển hướng / Viết lại (308 Redirect / Rewrite)
website.rewrite.redirect_type = 404 is 404: Không tìm thấy (404 Not Found)
theme.ir.asset.directive = after is Sau (After)
theme.ir.asset.directive = append is Append (Append)
theme.ir.ui.view.mode = primary is Chế độ xem cơ sở (Base view)
theme.ir.asset.directive = before is Trước (Before)
theme.ir.ui.view.mode = extension is Chế độ xem Mở rộng (Extension View)
hr.resume.line.display_type = classic is Cổ điển (Classic)
mail.mail.failure_type = mail_from_invalid is Địa chỉ gửi từ không hợp lệ (Invalid from address)
mail.mail.failure_type = mail_from_missing is Thiếu địa chỉ gửi từ (Missing from address)
mail.mail.failure_type = mail_smtp is Kết nối thất bại (vấn đề máy chủ gửi email) (Connection failed (outgoing mail server problem))
ir.actions.server.mail_post_method = comment is Tin nhắn (Message)
ir.actions.server.mail_post_method = note is Ghi chú (Note)
ir.actions.server.activity_date_deadline_range_type = days is Ngày (Days)
res.company.tax_calculation_rounding_method = round_globally is Làm tròn toàn bộ (Round Globally)
website.auth_signup_uninvited = b2c is Đăng ký miễn phí (Free sign up)
mrp.production.search_date_category = after is Sau (After)
website.controller.page.default_layout = grid is Lưới (Grid)
theme.ir.asset.directive = include is Bao gồm (Include)
mail.activity.plan.template.responsible_type = employee is Nhân viên (Employee)
hr.employee.gender = female is Nữ (Female)
hr.employee.employee_type = freelance is Tự do (Freelancer)
hr.employee.certificate = graduate is Tốt nghiệp (Graduate)
website.controller.page.default_layout = list is Danh sách (List)
website.auth_signup_uninvited = b2b is Khi mời (On invitation)
theme.ir.asset.directive = prepend is Prepend (Prepend)
ir.ui.view.visibility =  is Công khai (Public)
theme.ir.asset.directive = remove is Xóa (Remove)
theme.ir.asset.directive = replace is Thay thế (Replace)
ir.ui.view.visibility = restricted_group is Nhóm bị hạn chế (Restricted Group)
ir.ui.view.visibility = connected is Đã đăng nhập (Signed In)
ir.ui.view.visibility = password is Kèm mật khẩu (With Password)
theme.ir.ui.view.inherit_id = ir.ui.view is ir.ui.view (ir.ui.view)
theme.ir.ui.view.inherit_id = theme.ir.ui.view is theme.ir.ui.view (theme.ir.ui.view)
stock.picking.move_type = one is Khi tất cả sản phẩm đã sẵn sàng (When all products are ready)
stock.warehouse.reception_steps = two_steps is Nhận hàng rồi lưu kho (2 bước) (Receive then Store (2 steps))
res.company.account_price_include = tax_excluded is Chưa kèm thuế (Tax Excluded)
account.lock_exception.state = active is Đang hoạt động (Active)
account.lock_exception.state = revoked is Đã thu hồi (Revoked)
account.lock_exception.state = expired is Đã hết hạn (Expired)
stock.picking.state = draft is Nháp (Draft)
stock.warehouse.reception_steps = three_steps is Nhận hàng, kiểm soát chất lượng, rồi lưu kho (3 bước) (Receive, Quality Control, then Store (3 steps))
stock.warehouse.delivery_steps = ship_only is Giao hàng (1 bước) (Deliver (1 step))
account.lock_exception.lock_date_field = fiscalyear_lock_date is Ngày khoá sổ chung (Global Lock Date)
account.lock_exception.lock_date_field = tax_lock_date is Ngày khoá tờ khai thuế (Tax Return Lock Date)
account.lock_exception.lock_date_field = sale_lock_date is Ngày khoá sổ bán hàng (Sales Lock Date)
stock.picking.products_availability_state = available is Có hàng (Available)
res.partner.peppol_eas = 9923 is 9923 - Mã số thuế GTGT của Albania (9923 - Albania VAT number)
stock.warehouse.delivery_steps = pick_ship is Lấy hàng rồi giao (2 bước) (Pick then Deliver (2 steps))
stock.warehouse.delivery_steps = pick_pack_ship is Lấy hàng, đóng gói, rồi giao (3 bước) (Pick, Pack, then Deliver (3 steps))
res.config.settings.tenor_content_filter = off is Tắt (Off)
res.users.notification_type = email is Xử lý bằng email (Handle by Emails)
payment.transaction.operation = offline is Thanh toán offline bằng token (Offline payment by token)
payment.transaction.operation = online_direct is Thanh toán trực tiếp online (Online direct payment)
account.lock_exception.lock_date_field = purchase_lock_date is Ngày khoá sổ mua hàng (Purchase Lock Date)
hr.employee.work_location_type = home is Trang chủ (Home)
mrp.bom.consumption = flexible is Được phép (Allowed)
mrp.consumption.warning.consumption = flexible is Được phép (Allowed)
mrp.production.consumption = flexible is Được phép (Allowed)
mrp.bom.consumption = warning is Được phép kèm cảnh báo (Allowed with warning)
mrp.consumption.warning.consumption = warning is Được phép kèm cảnh báo (Allowed with warning)
mrp.production.consumption = warning is Được phép kèm cảnh báo (Allowed with warning)
mrp.workcenter.productivity.loss.type.loss_type = availability is Tình trạng còn hàng (Availability)
product.template.purchase_line_warn = warning is Cảnh báo (Warning)
res.partner.purchase_warn = warning is Cảnh báo (Warning)
barcode.rule.type = alias is Bí danh (Alias)
barcode.nomenclature.upc_ean_conv = always is Luôn luôn (Always)
ir.module.module.module_type = industries is Ngành nghề (Industries)
ir.module.module.module_type = official is Ứng dụng chính thức (Official Apps)
mail.notification.notification_type = snail is Thư bưu điện (Snailmail)
mail.notification.failure_type = sn_credit is Thư bưu điện Lỗi tín dụng (Snailmail Credit Error)
hr.employee.base.work_location_type = home is Trang chủ (Home)
hr.employee.public.work_location_type = home is Trang chủ (Home)
res.partner.peppol_eas = 0200 is 0200 - Mã định danh pháp nhân (Lithuania) (0200 - Legal entity code (Lithuania))
res.partner.peppol_eas = 9924 is 9924 - Mã số thuế GTGT của Bosnia và Herzegovina (9924 - Bosnia and Herzegovina VAT number)
product.template.activity_state = overdue is Overdue
product.template.activity_state = today is Today
product.template.activity_state = planned is Planned
product.template.activity_exception_decoration = warning is Alert
product.template.activity_exception_decoration = danger is Error
res.partner.peppol_eas = 9933 is 9933 - Mã số thuế GTGT của Hy Lạp (9933 - Greece VAT number)
res.partner.peppol_eas = 9934 is 9934 - Mã số thuế GTGT của Croatia (9934 - Croatia VAT number)
res.partner.peppol_eas = 9935 is 9935 - Mã số thuế GTGT của Ireland (9935 - Ireland VAT number)
product.product.activity_state = overdue is Overdue
product.product.activity_state = today is Today
product.product.activity_state = planned is Planned
product.product.activity_exception_decoration = warning is Alert
product.product.activity_exception_decoration = danger is Error
product.pricelist.activity_state = overdue is Overdue
product.pricelist.activity_state = today is Today
product.pricelist.activity_state = planned is Planned
product.pricelist.activity_exception_decoration = warning is Alert
product.pricelist.activity_exception_decoration = danger is Error
res.config.settings.providers_state = none is Không (None)
res.config.settings.providers_state = other_than_paypal is Khác Paypal (Other than Paypal)
res.config.settings.providers_state = paypal_only is Chỉ Paypal (Paypal Only)
product.template.rating_avg_text = top is Satisfied
product.template.rating_avg_text = ok is Okay
product.template.rating_avg_text = ko is Dissatisfied
product.document.attached_on_mrp = bom is Định mức nguyên liệu (Bill of Materials)
mrp.bom.consumption = strict is Đã bị chặn (Blocked)
mrp.consumption.warning.consumption = strict is Đã bị chặn (Blocked)
mrp.production.consumption = strict is Đã bị chặn (Blocked)
product.template.rating_avg_text = none is No Rating yet
account.tax.type_tax_use = sale is Bán hàng (Sales)
account.tax.type_tax_use = purchase is Mua hàng (Purchases)
account.tax.type_tax_use = none is Không (None)
account.tax.tax_scope = service is Dịch vụ (Services)
account.tax.tax_scope = consu is Hàng hóa (Goods)
account.tax.amount_type = group is Nhóm thuế (Group of Taxes)
account.tax.amount_type = fixed is Cố định (Fixed)
stock.warehouse.manufacture_steps = pbm is Lấy nguyên liệu và sản xuất (2 bước) (Pick components then manufacture (2 steps))
stock.warehouse.manufacture_steps = pbm_sam is Lấy nguyên liệu, sản xuất và lưu trữ sản phẩm (3 bước) (Pick components, manufacture, then store products (3 steps))
account.tax.amount_type = percent is Phần trăm (Percentage)
mrp.workcenter.working_state = blocked is Đã bị chặn (Blocked)
mrp.production.state = cancel is Đã hủy (Cancelled)
mrp.workorder.state = cancel is Đã hủy (Cancelled)
mrp.routing.workcenter.time_mode = auto is Tính theo thời gian đã theo dõi (Compute based on tracked time)
mrp.production.state = confirmed is Đã xác nhận (Confirmed)
website.product_page_image_layout = carousel is Băng chuyền (Carousel)
res.config.settings.account_on_checkout = disabled is Tắt (mua với tư cách khách) (Disabled (buy as guest))
payment.transaction.operation = online_token is Thanh toán online bằng token (Online payment by token)
account.tax.amount_type = division is Phần trăm đã bao gồm thuế (Percentage Tax Included)
account.tax.price_include_override = tax_included is Bao gồm thuế (Tax Included)
account.tax.price_include_override = tax_excluded is Chưa kèm thuế (Tax Excluded)
stock.storage.category.allow_new_product = empty is Nếu địa điểm trống (If the location is empty)
stock.storage.category.allow_new_product = same is Nếu tất cả sản phẩm tương đồng (If all products are same)
stock.storage.category.allow_new_product = mixed is Cho phép phối trộn sản phẩm (Allow mixed products)
report.stock.quantity.state = forecast is Tồn kho dự báo (Forecasted Stock)
product.template.tracking = serial is Theo số sê-ri duy nhất (By Unique Serial Number)
mrp.unbuild.state = draft is Nháp (Draft)
mrp.production.components_availability_state = expected is Dự kiến (Expected)
mrp.workorder.state = done is Đã hoàn thành (Finished)
mrp.routing.workcenter.worksheet_type = google_slide is Google Slide (Google Slide)
mrp.workcenter.working_state = normal is Bình thường (Normal)
mrp.production.components_availability_state = unavailable is Không có hàng (Not Available)
res.partner.bank.activity_state = overdue is Overdue
res.partner.bank.activity_state = today is Today
res.partner.bank.activity_state = planned is Planned
res.partner.bank.activity_exception_decoration = warning is Alert
res.partner.bank.activity_exception_decoration = danger is Error
website.product_page_image_width = 100_pc is 100% (100 %)
website.product_page_image_width = 50_pc is 50% (50 %)
website.product_page_image_width = 66_pc is 66% (66 %)
website.ecommerce_access = everyone is Tất cả người dùng (All users)
website.product_page_image_spacing = big is Lớn (Big)
website.account_on_checkout = disabled is Tắt (mua với tư cách khách) (Disabled (buy as guest))
website.add_to_cart_action = go_to_cart is Đi đến giỏ hàng (Go to cart)
account.journal.activity_state = overdue is Overdue
account.journal.activity_state = today is Today
account.journal.activity_state = planned is Planned
account.journal.activity_exception_decoration = warning is Alert
account.journal.activity_exception_decoration = danger is Error
website.product_page_image_layout = grid is Lưới (Grid)
product.attribute.visibility = hidden is Ẩn (Hidden)
account.tax.tax_exigibility = on_invoice is Dựa trên hoá đơn (Based on Invoice)
account.reconcile.model.line.amount_type = percentage_st_line is Phần trăm của dòng sao kê (Percentage of statement line)
account.move.activity_state = overdue is Overdue
account.move.activity_state = today is Today
account.move.activity_state = planned is Planned
account.move.activity_exception_decoration = warning is Alert
account.move.activity_exception_decoration = danger is Error
account.reconcile.model.line.amount_type = regex is Từ nhãn (From label)
mail.activity.plan.template.responsible_type = manager is Quản lý (Manager)
website.product_page_image_width = none is Ẩn (Hidden)
product.ribbon.position = left is Trái (Left)
hr.employee.certificate = master is Thạc sĩ (Master)
website.add_to_cart_action = force_dialog is Để người dùng quyết định (hộp thoại) (Let the user decide (dialog))
website.ecommerce_access = logged_in is Người dùng đã đăng nhập (Logged in users)
res.config.settings.account_on_checkout = mandatory is Bắt buộc (không cho phép thanh toán với tư cách khách) (Mandatory (no guest checkout))
res.config.settings.tenor_content_filter = high is Cao (High)
account.analytic.applicability.applicability = optional is Tuỳ chọn (Optional)
res.users.odoobot_state = onboarding_command is Onboarding Lệnh (Onboarding command)
res.users.odoobot_state = onboarding_emoji is Onboarding emoji (Onboarding emoji)
res.users.odoobot_state = onboarding_ping is Onboarding ping (Onboarding ping)
website.account_on_checkout = mandatory is Bắt buộc (không cho phép thanh toán với tư cách khách) (Mandatory (no guest checkout))
website.product_page_image_spacing = medium is Phương tiện (Medium)
website.product_page_image_spacing = none is Không (None)
product.label.layout.print_format = 2x7xprice is 2 x 7 kèm giá (2 x 7 with price)
product.label.layout.print_format = 4x12 is 4 x 12 (4 x 12)
rating.mixin.rating_avg_text = top is Hài lòng (Satisfied)
rating.rating.rating_text = top is Hài lòng (Satisfied)
portal.wizard.user.email_state = exist is Đã đăng ký (Already Registered)
portal.wizard.user.email_state = ko is Không hợp lệ (Invalid)
portal.wizard.user.email_state = ok is Hợp lệ (Valid)
sms.sms.failure_type = sms_blacklist is Đã hạn chế (Blacklisted)
sms.sms.state = canceled is Đã hủy (Cancelled)
mail.notification.failure_type = sms_country_not_supported is Quốc gia không được hỗ trợ (Country Not Supported)
account.reconcile.model.rule_type = writeoff_button is Nút để tạo bút toán đối ứng (Button to generate counterpart entry)
account.reconcile.model.rule_type = writeoff_suggestion is Quy tắc đề xuất bút toán đối ứng (Rule to suggest counterpart entry)
purchase.order.state = done is Đã khoá (Locked)
purchase.order.line.qty_received_method = manual is Thủ công (Manual)
purchase.order.invoice_status = to invoice is Chờ hoá đơn (Waiting Bills)
snailmail.letter.error_code = ATTACHMENT_ERROR is ATTACHMENT_ERROR (ATTACHMENT_ERROR)
snailmail.letter.error_code = CREDIT_ERROR is CREDIT_ERROR (CREDIT_ERROR)
account.reconcile.model.rule_type = invoice_matching is Quy tắc khớp hóa đơn (Rule to match invoices/bills)
account.reconcile.model.matching_order = old_first is Cũ nhất trước tiên (Oldest first)
account.reconcile.model.matching_order = new_first is Mới nhất trước tiên (Newest first)
delivery.price.rule.operator = == is =
delivery.price.rule.operator = <= is <=
delivery.price.rule.operator = < is <
delivery.price.rule.operator = >= is >=
delivery.price.rule.operator = > is >
res.partner.peppol_eas = 9910 is 9910 - Mã số thuế GTGT của Hungary (9910 - Hungary VAT number)
update.product.attribute.value.mode = add is Thêm vào các sản phẩm hiện có (Add to existing products)
hr.employee.base.work_location_type = office is Văn phòng (Office)
hr.employee.public.work_location_type = office is Văn phòng (Office)
hr.work.location.location_type = office is Văn phòng (Office)
hr.employee.certificate = other is Khác (Other)
hr.employee.gender = other is Khác (Other)
res.config.settings.account_on_checkout = optional is Tuỳ chọn (Optional)
website.account_on_checkout = optional is Tuỳ chọn (Optional)
product.ribbon.position = right is Phải (Right)
website.product_page_image_spacing = small is Nhỏ (Small)
hr.employee.work_location_type = other is Khác (Other)
res.partner.peppol_eas = 9913 is 9913 - Mạng lưới đăng ký kinh doanh (9913 - Business Registers Network)
delivery.carrier.delivery_type = base_on_rule is Dựa trên quy tắc (Based on Rules)
delivery.carrier.invoice_policy = estimated is Phí dự tính (Estimated cost)
delivery.carrier.delivery_type = fixed is Giá cố định (Fixed Price)
delivery.carrier.integration_level = rate is Lấy đánh giá (Get Rate)
hr.employee.base.work_location_type = other is Khác (Other)
res.partner.peppol_eas = 9914 is 9914 - Österreichische Umsatzsteuer-Identifikationsnummer (9914 - Österreichische Umsatzsteuer-Identifikationsnummer)
res.partner.peppol_eas = 9915 is 9915 - Österreichisches Verwaltungs bzw. Organisationskennzeichen (9915 - Österreichisches Verwaltungs bzw. Organisationskennzeichen)
res.partner.peppol_eas = 9918 is 9918 - HIỆP HỘI VIỄN THÔNG TÀI CHÍNH LIÊN NGÂN HÀNG TOÀN CẦU S.W.I.F.T (9918 - SOCIETY FOR WORLDWIDE INTERBANK FINANCIAL, TELECOMMUNICATION S.W.I.F.T)
product.category.property_valuation = manual_periodic is Thủ công (Manual)
delivery.carrier.integration_level = rate_and_ship is Nhận tỷ lệ và tạo lô hàng (Get Rate and Create Shipment)
delivery.price.rule.variable = price is Giá (Price)
delivery.price.rule.variable_factor = price is Giá (Price)
website.add_to_cart_action = stay is Ở lại trang sản phẩm (Stay on Product Page)
website.show_line_subtotals_tax_selection = tax_excluded is Chưa gồm thuế (Tax Excluded)
website.show_line_subtotals_tax_selection = tax_included is Bao gồm thuế (Tax Included)
product.attribute.visibility = visible is Hiển thị (Visible)
account.payment.activity_state = overdue is Overdue
hr.employee.public.work_location_type = other is Khác (Other)
hr.work.location.location_type = other is Khác (Other)
ir.actions.act_window.binding_type = report is Báo cáo (Report)
ir.actions.act_window.target = current is Cửa sổ Hiện tại (Current Window)
ir.actions.act_window.target = new is Cửa sổ mới (New Window)
ir.actions.act_window.target = inline is Chỉnh sửa Nội tuyến (Inline Edit)
ir.actions.act_window.target = fullscreen is Toàn Màn hình (Full Screen)
ir.actions.act_window.target = main is Hoạt động chính của cửa sổ hiện tại (Main action of Current Window)
ir.actions.act_window.view.view_mode = form is Biểu mẫu (Form)
ir.actions.act_window.view.view_mode = graph is Biểu đồ (Graph)
ir.actions.act_window.view.view_mode = pivot is Pivot (Pivot)
ir.actions.act_window.view.view_mode = calendar is Lịch (Calendar)
ir.actions.act_window.view.view_mode = kanban is Kanban (Kanban)
ir.actions.act_url.target = self is Cửa sổ này (This Window)
ir.actions.act_url.target = download is Tải xuống (Download)
ir.actions.server.binding_type = action is Tác vụ (Action)
ir.actions.client.binding_type = action is Tác vụ (Action)
ir.cron.interval_type = minutes is Phút (Minutes)
product.label.layout.print_format = 4x12xprice is 4 x 12 kèm giá (4 x 12 with price)
product.pricelist.item.applied_on = 3_global is Tất cả sản phẩm (All Products)
sms.sms.state = sent is Đã giao (Delivered)
sms.sms.failure_type = sms_duplicate is Nhân bản (Duplicate)
ir.cron.interval_type = hours is Giờ (Hours)
ir.cron.interval_type = days is Ngày (Days)
ir.cron.interval_type = weeks is Tuần (Weeks)
account.payment.activity_state = today is Today
account.payment.activity_state = planned is Planned
account.payment.activity_exception_decoration = warning is Alert
account.payment.activity_exception_decoration = danger is Error
account.reconcile.model.counterpart_type = general is Bút toán (Journal Entry)
hr.employee.public.hr_presence_state = out_of_working_hour is Ngoài giờ làm việc (Out of Working hours)
account.reconcile.model.counterpart_type = sale is Hóa đơn bán hàng (Customer Invoices)
account.reconcile.model.counterpart_type = purchase is Hoá đơn mua hàng (Vendor Bills)
payment.token.demo_simulated_state = cancel is Đã huỷ (Canceled)
payment.token.demo_simulated_state = done is Đã xác nhận (Confirmed)
payment.provider.code = demo is Demo (Demo)
payment.token.demo_simulated_state = error is Lỗi (Error)
payment.token.demo_simulated_state = pending is Đang chờ (Pending)
delivery.price.rule.variable = wv is Khối lượng * Thể tích (Weight * Volume)
delivery.price.rule.variable_factor = wv is Khối lượng * Thể tích (Weight * Volume)
stock.package.type.package_carrier_type = none is Không tích hợp đơn vị vận chuyển (No carrier integration)
delivery.carrier.invoice_policy = real is Phí thực tế (Real cost)
res.partner.picking_warn = no-message is Không có tin nhắn (No Message)
res.partner.picking_warn = warning is Cảnh báo (Warning)
hr.employee.hr_icon_display = presence_present is Có mặt (Present)
res.partner.picking_warn = block is Chặn tin nhắn (Blocking Message)
stock.location.usage = supplier is Địa điểm nhà cung cấp (Vendor Location)
stock.location.usage = view is Chế độ xem (View)
stock.location.usage = internal is Địa điểm nội bộ (Internal Location)
stock.location.usage = customer is Địa điểm khách hàng (Customer Location)
stock.picking.state = confirmed is Đang chờ (Waiting)
mrp.bom.type = normal is Sản xuất sản phẩm này (Manufacture this product)
stock.picking.type.code = mrp_operation is Sản xuất (Manufacturing)
loyalty.mail.trigger = create is Lúc sáng tạo (At Creation)
loyalty.rule.mode = auto is Tự động (Automatic)
loyalty.reward.discount_applicability = cheapest is Sản phẩm rẻ nhất (Cheapest Product)
loyalty.program.program_type = coupons is Phiếu giảm giá (Coupons)
ir.cron.interval_type = months is Tháng (Months)
ir.mail_server.smtp_authentication = login is Tên đăng nhập (Username)
ir.mail_server.smtp_authentication = certificate is Chứng chỉ SSL (SSL Certificate)
ir.mail_server.smtp_authentication = cli is Giao diện dòng lệnh (Command Line Interface)
ir.mail_server.smtp_encryption = none is Không (None)
ir.mail_server.smtp_encryption = starttls is TLS (STARTTLS) (TLS (STARTTLS))
ir.mail_server.smtp_encryption = ssl is SSL/TLS (SSL/TLS)
ir.logging.type = client is Khách hàng (Client)
ir.logging.type = server is Máy chủ (Server)
ir.module.module.state = uninstallable is Không thể cài đặt được (Uninstallable)
ir.module.module.state = uninstalled is Chưa cài đặt (Not Installed)
ir.module.module.state = installed is Đã cài đặt (Installed)
snailmail.letter.state = canceled is Đã hủy (Cancelled)
digest.digest.periodicity = quarterly is Hàng quý (Quarterly)
digest.digest.periodicity = weekly is Hàng tuần (Weekly)
payment.transaction.state = authorized is Cơ chế ủy quyền (Authorized)
payment.transaction.state = cancel is Đã huỷ (Canceled)
payment.transaction.state = done is Đã xác nhận (Confirmed)
account.reconcile.model.match_nature = amount_received is Đã nhận (Received)
account.reconcile.model.match_nature = amount_paid is Đã thanh toán (Paid)
account.payment.term.early_pay_discount_computation = excluded is Không bao giờ (Never)
hr.employee.hr_presence_state = present is Có mặt (Present)
hr.employee.base.hr_icon_display = presence_present is Có mặt (Present)
hr.employee.base.hr_presence_state = present is Có mặt (Present)
hr.employee.public.hr_icon_display = presence_present is Có mặt (Present)
account.reconcile.model.match_nature = both is Đã thanh toán/Đã nhận (Paid/Received)
account.reconcile.model.match_amount = lower is Nhỏ hơn (Is Lower Than)
loyalty.generate.wizard.mode = anonymous is Khách hàng ẩn danh (Anonymous Customers)
loyalty.program.trigger = auto is Tự động (Automatic)
loyalty.program.program_type = buy_x_get_y is Mua X Tặng Y (Buy X Get Y)
loyalty.program.applies_on = both is Đơn hàng hiện tại & tương lai (Current & Future orders)
loyalty.program.applies_on = current is Đơn hiện tại (Current order)
loyalty.reward.reward_type = discount is Chiết khấu (Discount)
loyalty.program.program_type = promo_code is Mã giảm giá (Discount Code)
loyalty.reward.reward_type = product is Sản phẩm miễn phí (Free Product)
product.template.tracking = none is Theo số lượng (By Quantity)
product.category.packaging_reserve_method = partial is Dự trữ một phần gói hàng (Reserve Partial Packagings)
loyalty.program.applies_on = future is Đơn hàng tương lai (Future orders)
loyalty.program.program_type = gift_card is Thẻ quà tặng (Gift Card)
loyalty.program.program_type = loyalty is Thẻ khách hàng thân thiết (Loyalty Cards)
loyalty.program.program_type = next_order_coupons is Phiếu giảm giá đơn hàng tiếp theo (Next Order Coupons)
stock.package_level.state = confirmed is Đã xác nhận (Confirmed)
stock.package_level.state = done is Hoàn tất (Done)
mrp.production.state = progress is Đang thực hiện (In Progress)
mrp.workcenter.working_state = done is Đang thực hiện (In Progress)
mrp.workorder.state = progress is Đang thực hiện (In Progress)
mrp.bom.type = phantom is Bộ kit (Kit)
account.report.general.ledger.target_move = posted is Tất cả bút toán đã vào sổ (All Posted Entries)
account.report.general.ledger.target_move = all is Tất cả bút toán (All Entries)
account.report.general.ledger.display_account = all is Tất cả (All)
account.report.general.ledger.display_account = movement is Có phát sinh (With movements)
account.report.general.ledger.display_account = not_zero is Có số dư khác 0 (With balance is not equal to 0)
purchase.order.line.display_type = line_note is Ghi chú (Note)
purchase.order.invoice_status = no is Không có gì để thanh toán (Nothing to Bill)
ir.module.module.state = to upgrade is Sẽ được nâng cấp (To be upgraded)
ir.module.module.state = to remove is Sẽ bị gỡ bỏ (To be removed)
ir.module.module.state = to install is Sẽ được cài đặt (To be installed)
ir.module.module.license = GPL-2 is GPL phiên bản 2 (GPL Version 2)
ir.module.module.license = GPL-2 or any later version is GPL-2 hoặc phiên bản mới hơn (GPL-2 or later version)
ir.module.module.dependency.state = to remove is Sẽ bị gỡ bỏ (To be removed)
ir.module.module.dependency.state = to install is Sẽ được cài đặt (To be installed)
ir.module.module.dependency.state = unknown is Không xác định (Unknown)
ir.module.module.exclusion.state = uninstallable is Không thể cài đặt được (Uninstallable)
ir.module.module.exclusion.state = uninstalled is Chưa cài đặt (Not Installed)
ir.module.module.exclusion.state = installed is Đã cài đặt (Installed)
hr.work.location.location_type = home is Trang chủ (Home)
account.reconcile.model.match_amount = greater is Lớn hơn (Is Greater Than)
account.reconcile.model.match_amount = between is Trong khoảng (Is Between)
ir.actions.server.state = mail_post is Gửi email (Send Email)
account.reconcile.model.match_label = contains is Chứa (Contains)
account.reconcile.model.match_label = not_contains is Không chứa (Not Contains)
ir.actions.server.state = followers is Thêm Người theo dõi (Add Followers)
ir.actions.server.state = remove_followers is Xoá người theo dõi (Remove Followers)
account.reconcile.model.match_label = match_regex is Khớp Regex (Match Regex)
account.reconcile.model.match_note = contains is Chứa (Contains)
account.reconcile.model.match_note = not_contains is Không chứa (Not Contains)
account.reconcile.model.match_note = match_regex is Khớp Regex (Match Regex)
account.reconcile.model.match_transaction_type = contains is Chứa (Contains)
account.reconcile.model.match_transaction_type = not_contains is Không chứa (Not Contains)
account.reconcile.model.payment_tolerance_type = percentage is theo phần trăm (in percentage)
account.reconcile.model.payment_tolerance_type = fixed_amount is theo số tiền (in amount)
account.payment.term.early_pay_discount_computation = included is Khi thanh toán sớm (On early payment)
hr.employee.gender = male is Nam (Male)
loyalty.reward.discount_applicability = order is Đơn hàng (Order)
loyalty.program.program_type = promotion is Khuyến mãi (Promotions)
loyalty.generate.wizard.mode = selected is Khách hàng được chọn (Selected Customers)
loyalty.reward.discount_applicability = specific is Sản phẩm cụ thể (Specific Products)
loyalty.program.trigger = with_code is Sử dụng một mã (Use a code)
loyalty.mail.trigger = points_reach is Khi Đạt (When Reaching)
loyalty.rule.mode = with_code is Với một mã khuyến mãi (With a promotion code)
loyalty.program.program_type = ewallet is Ví điện tử (eWallet)
stock.lot.activity_state = overdue is Overdue
stock.lot.activity_state = today is Today
stock.lot.activity_state = planned is Planned
stock.lot.activity_exception_decoration = warning is Alert
stock.lot.activity_exception_decoration = danger is Error
loyalty.rule.minimum_amount_tax_mode = excl is chưa gồm thuế (tax excluded)
ir.module.module.exclusion.state = to upgrade is Sẽ được nâng cấp (To be upgraded)
ir.module.module.exclusion.state = to remove is Sẽ bị gỡ bỏ (To be removed)
loyalty.rule.minimum_amount_tax_mode = incl is bao gồm thuế (tax included)
ir.module.module.exclusion.state = to install is Sẽ được cài đặt (To be installed)
ir.module.module.exclusion.state = unknown is Không xác định (Unknown)
stock.picking.activity_state = overdue is Overdue
stock.picking.activity_state = today is Today
stock.picking.activity_state = planned is Planned
stock.picking.activity_exception_decoration = warning is Alert
stock.picking.activity_exception_decoration = danger is Error
report.paperformat.format = A0 is A0  5   841 x 1189 mm (A0  5   841 x 1189 mm)
report.paperformat.format = A1 is A1  6   594 x 841 mm (A1  6   594 x 841 mm)
report.paperformat.format = A2 is A2  7   420 x 594 mm (A2  7   420 x 594 mm)
report.paperformat.format = A3 is A3  8   297 x 420 mm (A3  8   297 x 420 mm)
report.paperformat.format = A4 is A4  0   210 x 297 mm, 8.26 x 11.69 inches (A4  0   210 x 297 mm, 8.26 x 11.69 inches)
report.paperformat.format = A5 is A5  9   148 x 210 mm (A5  9   148 x 210 mm)
report.paperformat.orientation = Portrait is Dọc (Portrait)
base.enable.profiling.wizard.duration = minutes_5 is 5 Phút (5 Minutes)
base.enable.profiling.wizard.duration = hours_1 is 1 Giờ (1 Hour)
base.enable.profiling.wizard.duration = days_1 is 1 Ngày (1 Day)
base.enable.profiling.wizard.duration = months_1 is 1 Tháng (1 Month)
res.partner.tz = Africa/Accra is Châu Phi/Accra (Africa/Accra)
res.partner.tz = Africa/Addis_Ababa is Châu Phi/Addis_Ababa (Africa/Addis_Ababa)
res.partner.tz = Africa/Algiers is Châu Phi/Algiers (Africa/Algiers)
res.partner.tz = Africa/Asmara is Châu Phi/Asmara (Africa/Asmara)
res.partner.tz = Africa/Bamako is Châu Phi/Bamako (Africa/Bamako)
res.partner.tz = Africa/Bangui is Châu Phi/Bangui (Africa/Bangui)
account.report.general.ledger.sortby = sort_date is Ngày (Date)
account.report.general.ledger.sortby = sort_journal_partner is Sổ nhật ký & Đối tác (Journal & Partner)
res.partner.tz = Africa/Banjul is Châu Phi/Banjul (Africa/Banjul)
account.balance.report.target_move = posted is Tất cả bút toán đã vào sổ (All Posted Entries)
account.balance.report.target_move = all is Tất cả bút toán (All Entries)
account.balance.report.display_account = all is Tất cả (All)
account.balance.report.display_account = movement is Có phát sinh (With movements)
loyalty.reward.reward_type = shipping is Miễn phí giao hàng (Free Shipping)
res.partner.tz = Africa/Bissau is Châu Phi/Bissau (Africa/Bissau)
res.partner.tz = Africa/Blantyre is Châu Phi/Blantyre (Africa/Blantyre)
res.partner.tz = Africa/Brazzaville is Châu Phi/Brazzaville (Africa/Brazzaville)
res.partner.tz = Africa/Bujumbura is Châu Phi/Bujumbura (Africa/Bujumbura)
res.partner.tz = Africa/Cairo is Châu Phi/Cairo (Africa/Cairo)
res.partner.tz = Africa/Casablanca is Châu Phi/Casablanca (Africa/Casablanca)
res.partner.tz = Africa/Ceuta is Châu Phi/Ceuta (Africa/Ceuta)
res.partner.tz = Africa/Conakry is Châu Phi/Conakry (Africa/Conakry)
res.partner.tz = Africa/Dakar is Châu Phi/Dakar (Africa/Dakar)
res.partner.tz = Africa/Dar_es_Salaam is Châu Phi/Dar_es_Salaam (Africa/Dar_es_Salaam)
res.partner.tz = Africa/Djibouti is Châu Phi/Djibouti (Africa/Djibouti)
res.partner.tz = Africa/Douala is Châu Phi/Douala (Africa/Douala)
account.payment.term.early_pay_discount_computation = mixed is Luôn luôn (khi xuất hóa đơn) (Always (upon invoice))
account.payment.term.line.value = percent is Phần trăm (Percent)
barcode.rule.encoding = any is Bất kỳ (Any)
barcode.rule.encoding = ean13 is EAN-13 (EAN-13)
account.payment.term.line.value = fixed is Cố định (Fixed)
lunch.supplier.activity_state = overdue is Overdue
lunch.supplier.activity_state = today is Today
lunch.supplier.activity_state = planned is Planned
payment.transaction.state = error is Lỗi (Error)
account.payment.term.line.delay_type = days_after is Ngày sau ngày lập hoá đơn (Days after invoice date)
hr.employee.hr_icon_display = presence_out_of_working_hour is Ngoài giờ làm việc (Out of Working hours)
hr.employee.hr_presence_state = out_of_working_hour is Ngoài giờ làm việc (Out of Working hours)
lunch.supplier.activity_exception_decoration = warning is Alert
lunch.supplier.activity_exception_decoration = danger is Error
res.partner.tz = Africa/El_Aaiun is Châu Phi/El_Aaiun (Africa/El_Aaiun)
res.partner.tz = Africa/Freetown is Châu Phi/Freetown (Africa/Freetown)
res.partner.tz = Africa/Gaborone is Châu Phi/Gaborone (Africa/Gaborone)
res.partner.tz = Africa/Harare is Châu Phi/Harare (Africa/Harare)
res.partner.tz = Africa/Johannesburg is Châu Phi/Johannesburg (Africa/Johannesburg)
res.partner.tz = America/Argentina/Ushuaia is Châu Mỹ/Argentina/Ushuaia (America/Argentina/Ushuaia)
res.partner.tz = America/Asuncion is Châu Mỹ/Asuncion (America/Asuncion)
res.partner.tz = America/Atikokan is Châu Mỹ/Atikokan (America/Atikokan)
res.partner.tz = America/Atka is Châu Mỹ/Atka (America/Atka)
res.partner.tz = America/Bahia is Châu Mỹ/Bahia (America/Bahia)
res.partner.tz = America/Bahia_Banderas is Châu Mỹ/Bahia_Banderas (America/Bahia_Banderas)
res.partner.tz = America/Barbados is Châu Mỹ/Barbados (America/Barbados)
res.partner.tz = America/Belem is Châu Mỹ/Belem (America/Belem)
res.partner.tz = America/Belize is Châu Mỹ/Belize (America/Belize)
res.partner.tz = America/Blanc-Sablon is Châu Mỹ/Blanc-Sablon (America/Blanc-Sablon)
res.partner.tz = America/Boa_Vista is Châu Mỹ/Boa_Vista (America/Boa_Vista)
res.partner.tz = America/Bogota is Châu Mỹ/Bogota (America/Bogota)
barcode.rule.encoding = ean8 is EAN-8 (EAN-8)
mail.alias.alias_contact = followers is Chỉ những người dõi theo (Followers only)
account.asset.category.activity_state = overdue is Overdue
account.asset.category.activity_state = today is Today
account.asset.category.activity_state = planned is Planned
account.asset.category.activity_exception_decoration = warning is Alert
account.asset.category.activity_exception_decoration = danger is Error
mail.activity.type.delay_unit = days is ngày (days)
account.asset.asset.activity_state = overdue is Overdue
account.asset.asset.activity_state = today is Today
account.asset.asset.activity_state = planned is Planned
account.asset.asset.activity_exception_decoration = warning is Alert
account.asset.asset.activity_exception_decoration = danger is Error
purchase.order.activity_state = overdue is Overdue
purchase.order.activity_state = today is Today
purchase.order.activity_state = planned is Planned
purchase.order.activity_exception_decoration = warning is Alert
purchase.order.activity_exception_decoration = danger is Error
account.financial.report.style_overwrite = 1 is Tiêu đề chính 1 (đậm, gạch chân) (Main Title 1 (bold, underlined))
account.financial.report.style_overwrite = 2 is Tiêu đề 2 (đậm) (Title 2 (bold))
account.financial.report.style_overwrite = 3 is Tiêu đề 3 (đậm, nhỏ hơn) (Title 3 (bold, smaller))
account.financial.report.style_overwrite = 4 is Văn bản thường (Normal Text)
account.financial.report.style_overwrite = 5 is Chữ nghiêng (nhỏ hơn) (Italic Text (smaller))
account.financial.report.style_overwrite = 6 is Chữ nhỏ nhất (Smallest Text)
account.daybook.report.target_move = posted is Posted Entries
account.daybook.report.target_move = all is All Entries
account.cashbook.report.target_move = posted is Posted Entries
account.cashbook.report.target_move = all is All Entries
account.cashbook.report.display_account = all is All
account.cashbook.report.display_account = movement is With movements
product.pricelist.item.base = pricelist is Bảng giá khác (Other Pricelist)
mail.notification.failure_type = sms_number_format is Định dạng số sai (Wrong Number Format)
sms.sms.failure_type = sms_number_format is Định dạng số sai (Wrong Number Format)
account.payment.term.line.delay_type = days_after_end_of_month is Ngày sau ngày cuối tháng (Days after end of month)
account.payment.term.line.delay_type = days_after_end_of_next_month is Ngày sau ngày cuối tháng kế tiếp (Days after end of next month)
account.payment.term.line.delay_type = days_end_of_month_on_the is Ngày sau ngày cuối tháng vào ngày (Days end of month on the)
hr.employee.base.hr_icon_display = presence_out_of_working_hour is Ngoài giờ làm việc (Out of Working hours)
hr.employee.base.hr_presence_state = out_of_working_hour is Ngoài giờ làm việc (Out of Working hours)
mail.activity.type.delay_unit = weeks is tuần (weeks)
mail.activity.type.delay_unit = months is tháng (months)
account.move.line.display_type = product is Sản phẩm (Product)
account.cashbook.report.display_account = not_zero is With balance is not equal to 0
account.cashbook.report.sortby = sort_date is Date
account.cashbook.report.sortby = sort_journal_partner is Journal & Partner
account.bankbook.report.target_move = posted is Posted Entries
account.bankbook.report.target_move = all is All Entries
account.bankbook.report.display_account = all is All
account.bankbook.report.display_account = movement is With movements
account.bankbook.report.display_account = not_zero is With balance is not equal to 0
account.bankbook.report.sortby = sort_date is Date
account.bankbook.report.sortby = sort_journal_partner is Journal & Partner
sale.order.activity_state = overdue is Overdue
sale.order.activity_state = today is Today
sale.order.activity_state = planned is Planned
sale.order.activity_exception_decoration = warning is Alert
sale.order.activity_exception_decoration = danger is Error
stock.putaway.rule.sublocation = no is Không (No)
payment.method.support_refund = partial is Toàn bộ & một phần (Full & Partial)
payment.provider.support_refund = partial is Toàn bộ & một phần (Full & Partial)
res.partner.tz = America/Boise is Châu Mỹ/Boise (America/Boise)
res.partner.tz = America/Cambridge_Bay is Châu Mỹ/Cambridge_Bay (America/Cambridge_Bay)
res.partner.tz = America/Campo_Grande is Châu Mỹ/Campo_Grande (America/Campo_Grande)
res.partner.tz = America/Cancun is Châu Mỹ/Cancun (America/Cancun)
res.partner.tz = America/Danmarkshavn is Châu Mỹ/Danmarkshavn (America/Danmarkshavn)
res.partner.tz = America/Dawson is Châu Mỹ/Dawson (America/Dawson)
res.users.dialog_size = minimize is Minimize
res.users.dialog_size = maximize is Maximize
res.users.sidebar_type = invisible is Invisible
res.users.sidebar_type = small is Small
res.users.sidebar_type = large is Large
res.users.chatter_position = side is Side
res.users.chatter_position = bottom is Bottom
res.partner.tz = America/Dawson_Creek is Châu Mỹ/Dawson_Creek (America/Dawson_Creek)
res.partner.tz = America/Denver is Châu Mỹ/Denver (America/Denver)
res.partner.tz = America/Detroit is Châu Mỹ/Detroit (America/Detroit)
res.partner.tz = America/Dominica is Châu Mỹ/Dominica (America/Dominica)
res.partner.tz = America/Edmonton is Châu Mỹ/Edmonton (America/Edmonton)
res.partner.tz = America/Eirunepe is Châu Mỹ/Eirunepe (America/Eirunepe)
res.partner.tz = America/El_Salvador is Châu Mỹ/El_Salvador (America/El_Salvador)
res.partner.tz = America/Ensenada is Châu Mỹ/Ensenada (America/Ensenada)
queue.job.activity_state = overdue is Overdue
queue.job.activity_state = today is Today
queue.job.activity_state = planned is Planned
queue.job.activity_exception_decoration = warning is Alert
queue.job.activity_exception_decoration = danger is Error
queue.job.state = wait_dependencies is Wait Dependencies
queue.job.state = pending is Pending
queue.job.state = enqueued is Enqueued
queue.job.state = started is Started
queue.job.state = done is Done
queue.job.state = cancelled is Cancelled
queue.job.state = failed is Failed
ecommerce.warehouse.mapping.activity_state = overdue is Overdue
ecommerce.warehouse.mapping.activity_state = today is Today
ecommerce.warehouse.mapping.activity_state = planned is Planned
ecommerce.warehouse.mapping.activity_exception_decoration = warning is Alert
ecommerce.warehouse.mapping.activity_exception_decoration = danger is Error
res.partner.tz = America/Fort_Nelson is Châu Mỹ/Fort_Nelson (America/Fort_Nelson)
res.partner.tz = America/Fortaleza is Châu Mỹ/Fortaleza (America/Fortaleza)
res.partner.tz = America/Glace_Bay is Châu Mỹ/Glace_Bay (America/Glace_Bay)
res.partner.tz = America/Goose_Bay is Châu Mỹ/Goose_Bay (America/Goose_Bay)
res.partner.tz = America/Grand_Turk is Châu Mỹ/Grand_Turk (America/Grand_Turk)
res.partner.tz = America/Grenada is Châu Mỹ/Grenada (America/Grenada)
res.partner.tz = America/Guadeloupe is Châu Mỹ/Guadeloupe (America/Guadeloupe)
res.partner.tz = America/Guatemala is Châu Mỹ/Guatemala (America/Guatemala)
res.partner.tz = America/Guayaquil is Châu Mỹ/Guayaquil (America/Guayaquil)
res.partner.tz = America/Guyana is Châu Mỹ/Guyana (America/Guyana)
res.partner.tz = America/Halifax is Châu Mỹ/Halifax (America/Halifax)
account.fiscal.position.foreign_vat_header_mode = templates_found is Đã tìm thấy mẫu (Templates Found)
res.partner.tz = America/Havana is Châu Mỹ/Havana (America/Havana)
res.partner.tz = America/Hermosillo is Châu Mỹ/Hermosillo (America/Hermosillo)
res.partner.tz = America/Indiana/Indianapolis is Châu Mỹ/Indiana/Indianapolis (America/Indiana/Indianapolis)
res.partner.tz = America/Indiana/Knox is Châu Mỹ/Indiana/Knox (America/Indiana/Knox)
res.partner.tz = America/Indiana/Marengo is Châu Mỹ/Indiana/Marengo (America/Indiana/Marengo)
res.partner.tz = America/Indiana/Petersburg is Châu Mỹ/Indiana/Petersburg (America/Indiana/Petersburg)
res.partner.tz = America/Indiana/Tell_City is Châu Mỹ/Indiana/Tell_City (America/Indiana/Tell_City)
res.partner.tz = America/Indiana/Vevay is Châu Mỹ/Indiana/Vevay (America/Indiana/Vevay)
res.partner.tz = America/Indiana/Vincennes is Châu Mỹ/Indiana/Vincennes (America/Indiana/Vincennes)
res.partner.tz = America/Indiana/Winamac is Châu Mỹ/Indiana/Winamac (America/Indiana/Winamac)
res.partner.tz = America/Inuvik is Châu Mỹ/Inuvik (America/Inuvik)
res.partner.tz = America/Iqaluit is Châu Mỹ/Iqaluit (America/Iqaluit)
res.partner.tz = America/Jamaica is Châu Mỹ/Jamaica (America/Jamaica)
res.partner.tz = America/Juneau is Châu Mỹ/Juneau (America/Juneau)
res.partner.tz = America/Kentucky/Louisville is Châu Mỹ/Kentucky/Louisville (America/Kentucky/Louisville)
res.partner.tz = America/Kentucky/Monticello is Châu Mỹ/Kentucky/Monticello (America/Kentucky/Monticello)
res.partner.tz = America/Kralendijk is Châu Mỹ/Kralendijk (America/Kralendijk)
res.partner.tz = America/La_Paz is Châu Mỹ/La_Paz (America/La_Paz)
res.partner.tz = America/Los_Angeles is Châu Mỹ/Los_Angeles (America/Los_Angeles)
res.partner.tz = America/Lower_Princes is Châu Mỹ/Lower_Princes (America/Lower_Princes)
res.partner.tz = America/Maceio is Châu Mỹ/Maceio (America/Maceio)
res.partner.tz = America/Managua is Châu Mỹ/Managua (America/Managua)
res.partner.tz = America/Manaus is Châu Mỹ/Manaus (America/Manaus)
mrp.production.components_availability_state = late is Trễ (Late)
stock.rule.action = manufacture is Sản xuất (Manufacture)
mrp.workcenter.productivity.loss.type.loss_type = quality is Chất lượng (Quality)
res.partner.tz = America/Marigot is Châu Mỹ/Marigot (America/Marigot)
res.partner.tz = America/Martinique is Châu Mỹ/Martinique (America/Martinique)
res.partner.tz = America/Matamoros is Châu Mỹ/Matamoros (America/Matamoros)
res.partner.tz = America/Mazatlan is Châu Mỹ/Mazatlan (America/Mazatlan)
res.partner.tz = America/Menominee is Châu Mỹ/Menominee (America/Menominee)
res.partner.tz = America/Merida is Châu Mỹ/Merida (America/Merida)
res.partner.tz = America/Metlakatla is Châu Mỹ/Metlakatla (America/Metlakatla)
res.partner.tz = America/Mexico_City is Châu Mỹ/Mexico_City (America/Mexico_City)
res.partner.tz = America/Miquelon is Châu Mỹ/Miquelon (America/Miquelon)
res.partner.tz = America/Moncton is Châu Mỹ/Moncton (America/Moncton)
res.partner.tz = America/Monterrey is Châu Mỹ/Monterrey (America/Monterrey)
res.partner.tz = America/Montevideo is Châu Mỹ/Montevideo (America/Montevideo)
res.partner.tz = America/Montreal is Châu Mỹ/Montreal (America/Montreal)
mrp.production.reservation_state = confirmed is Đang chờ (Waiting)
mrp.production.reservation_state = waiting is Đang chờ hoạt động khác (Waiting Another Operation)
mrp.workorder.state = pending is Đang chờ công đoạn khác (Waiting for another WO)
stock.picking.type.done_mrp_lot_label_to_print = zpl is ZPL (ZPL)
stock.picking.type.generated_mrp_lot_label_to_print = zpl is ZPL (ZPL)
account.account.internal_group = off is Ngoài bảng cân đối (Off Balance)
account.journal.type = sale is Bán hàng (Sales)
account.journal.type = purchase is Mua hàng (Purchase)
account.journal.type = cash is Tiền mặt (Cash)
account.journal.type = bank is Ngân hàng (Bank)
account.journal.type = credit is Thẻ (Credit Card)
account.journal.type = general is Thông tin khác (Miscellaneous)
account.journal.invoice_reference_type = none is Mở (Open)
account.balance.report.display_account = not_zero is Có số dư khác 0 (With balance is not equal to 0)
account.tax.report.wizard.target_move = posted is Tất cả bút toán đã vào sổ (All Posted Entries)
product.category.property_cost_method = standard is Giá tiêu chuẩn (Standard Price)
lunch.alert.notification_moment = am is AM (AM)
lunch.supplier.moment = am is AM (AM)
lunch.alert.mode = alert is Cảnh báo trong ứng dụng (Alert in app)
lunch.order.state = cancelled is Đã hủy (Cancelled)
lunch.alert.mode = chat is Thông báo trò chuyện (Chat notification)
lunch.supplier.delivery = delivery is Giao hàng (Delivery)
lunch.supplier.send_by = mail is Email (Email)
lunch.alert.recipients = last_month is Nhân viên đã đặt hàng tháng trước (Employee who ordered last month)
lunch.alert.recipients = last_week is Nhân viên đã đặt hàng tuần trước (Employee who ordered last week)
lunch.alert.recipients = last_year is Nhân viên đã đặt hàng năm ngoái (Employee who ordered last year)
lunch.alert.recipients = everyone is Mọi người (Everyone)
lunch.supplier.delivery = no_delivery is Không giao hàng (No Delivery)
lunch.supplier.topping_quantity_1 = 0_more is Không có hoặc nhiều hơn (None or More)
ir.model.fields.ttype = binary is nhị phân (binary)
ir.model.fields.ttype = boolean is boolean (boolean)
ir.model.fields.ttype = date is ngày (date)
ir.model.fields.ttype = datetime is ngày giờ (datetime)
lunch.supplier.topping_quantity_2 = 0_more is Không có hoặc nhiều hơn (None or More)
lunch.supplier.topping_quantity_3 = 0_more is Không có hoặc nhiều hơn (None or More)
lunch.supplier.topping_quantity_1 = 1_more is Một hoặc nhiều (One or More)
lunch.supplier.topping_quantity_2 = 1_more is Một hoặc nhiều (One or More)
ir.model.fields.ttype = float is dự trữ (float)
ir.model.fields.ttype = html is html (html)
ir.model.fields.ttype = integer is số nguyên (integer)
product.template.purchase_method = purchase is Theo số lượng đã đặt (On ordered quantities)
product.template.purchase_method = receive is Theo số lượng đã nhận (On received quantities)
res.config.settings.default_purchase_method = purchase is Số lượng đã đặt (Ordered quantities)
account.analytic.applicability.business_domain = purchase_order is Đơn mua hàng (Purchase Order)
purchase.order.state = purchase is Đơn mua hàng (Purchase Order)
purchase.report.state = purchase is Đơn mua hàng (Purchase Order)
purchase.order.state = draft is YCBG (RFQ)
purchase.order.state = sent is YCBG đã gửi (RFQ Sent)
purchase.report.state = sent is YCBG đã gửi (RFQ Sent)
res.config.settings.default_purchase_method = receive is Số lượng đã nhận (Received quantities)
purchase.order.line.display_type = line_section is Phần (Section)
purchase.order.state = to approve is Cần phê duyệt (To Approve)
purchase.report.state = to approve is Cần phê duyệt (To Approve)
purchase.order.priority = 1 is Khẩn cấp (Urgent)
ir.model.fields.ttype = json is json (json)
ir.model.fields.ttype = many2many is many2many (many2many)
ir.model.fields.ttype = many2one is many2one (many2one)
ir.model.fields.ttype = many2one_reference is many2one_reference (many2one_reference)
ir.model.fields.ttype = monetary is tiền tệ (monetary)
ir.model.fields.ttype = one2many is one2many (one2many)
ir.model.fields.ttype = properties is thuộc tính (properties)
res.partner.tz = America/Montserrat is Châu Mỹ/Montserrat (America/Montserrat)
res.partner.tz = America/Nassau is Châu Mỹ/Nassau (America/Nassau)
res.partner.tz = America/New_York is Châu Mỹ/New_York (America/New_York)
res.partner.tz = America/Nipigon is Châu Mỹ/Nipigon (America/Nipigon)
account.move.payment_state = not_paid is Chưa thanh toán (Not Paid)
account.move.payment_state = in_payment is Đang thanh toán (In Payment)
account.move.payment_state = paid is Đã thanh toán (Paid)
account.move.payment_state = partial is Đã thanh toán một phần (Partially Paid)
account.move.payment_state = reversed is Đã đảo (Reversed)
account.move.payment_state = blocked is Đã bị chặn (Blocked)
account.move.status_in_payment = not_paid is Chưa thanh toán (Not Paid)
account.move.status_in_payment = invoicing_legacy is Kế thừa ứng dụng hóa đơn (Invoicing App Legacy)
account.move.status_in_payment = draft is Nháp (Draft)
account.move.status_in_payment = cancel is Đã hủy (Cancelled)
account.move.move_sent_values = sent is Đã gửi (Sent)
account.move.move_sent_values = not_sent is Chưa gửi (Not Sent)
res.company.fiscalyear_last_month = 1 is Tháng 1 (January)
res.company.fiscalyear_last_month = 2 is Tháng 2 (February)
ir.model.fields.ttype = properties_definition is properties_definition (properties_definition)
ir.model.fields.ttype = reference is tham chiếu (reference)
ir.model.fields.ttype = selection is lựa chọn (selection)
ir.model.fields.ttype = text is văn bản (text)
ir.model.fields.state = manual is Trường Tùy chỉnh (Custom Field)
ir.model.fields.state = base is Trường Cơ sở (Base Field)
account.tax.report.wizard.target_move = all is Tất cả bút toán (All Entries)
ir.model.fields.on_delete = cascade is Cascade (Cascade)
ir.model.fields.on_delete = set null is Để TRỐNG (Set NULL)
ir.model.fields.on_delete = restrict is Giới hạn (Restrict)
ir.sequence.implementation = standard is Standard (Standard)
ir.sequence.implementation = no_gap is No gap (No gap)
ir.ui.menu.action = ir.actions.report is ir.actions.report (ir.actions.report)
res.partner.tz = America/Noronha is Châu Mỹ/Noronha (America/Noronha)
base.import.module.state = done is hoàn thành (done)
lunch.supplier.topping_quantity_3 = 1_more is Một hoặc nhiều (One or More)
lunch.supplier.topping_quantity_1 = 1 is Chỉ một (Only One)
lunch.supplier.topping_quantity_2 = 1 is Chỉ một (Only One)
lunch.supplier.topping_quantity_3 = 1 is Chỉ một (Only One)
lunch.order.state = ordered is Đã đặt (Ordered)
lunch.alert.notification_moment = pm is PM (PM)
base.import.module.state = init is init (init)
account.aged.trial.balance.target_move = posted is Tất cả bút toán đã vào sổ (All Posted Entries)
account.aged.trial.balance.target_move = all is Tất cả bút toán (All Entries)
account.aged.trial.balance.result_selection = customer is Tài khoản phải thu (Receivable Accounts)
account.aged.trial.balance.result_selection = supplier is Tài khoản phải trả (Payable Accounts)
account.aged.trial.balance.result_selection = customer_supplier is Tài khoản phải thu và phải trả (Receivable and Payable Accounts)
account.account.type.type = asset_receivable is Phải thu (Receivable)
account.account.type.type = asset_cash is Tiền và tương đương tiền (Bank and Cash)
account.account.type.type = asset_current is Tài sản ngắn hạn (Current Assets)
account.account.type.type = asset_non_current is Tài sản dài hạn (Non-current Assets)
account.account.type.type = asset_prepayments is Trả trước (Prepayments)
account.account.type.type = asset_fixed is Tài sản cố định (Fixed Assets)
account.account.type.type = liability_payable is Phải trả (Payable)
account.account.type.type = liability_credit_card is Thẻ tín dụng (Credit Card)
account.account.type.type = liability_current is Nợ ngắn hạn (Current Liabilities)
account.account.type.type = liability_non_current is Nợ dài hạn (Non-current Liabilities)
account.account.type.type = equity is Vốn chủ sở hữu (Equity)
account.account.type.type = equity_unaffected is Lợi nhuận năm nay (Current Year Earnings)
account.account.type.type = income is Thu nhập (Income)
account.tax.tax_exigibility = on_payment is Dựa trên thanh toán (Based on Payment)
account.tax.repartition.line.repartition_type = base is Cơ sở (Base)
lunch.supplier.moment = pm is PM (PM)
lunch.supplier.send_by = phone is Điện thoại (Phone)
lunch.order.state = confirmed is Đã nhận (Received)
lunch.order.state = sent is Đã gửi (Sent)
lunch.order.state = new is Cần đặt hàng (To Order)
resource.calendar.attendance.dayofweek = 0 is Thứ Hai (Monday)
resource.calendar.attendance.dayofweek = 3 is Thứ năm (Thursday)
barcode.rule.gs1_content_type = alpha is Tên chứa chữ và số (Alpha-Numeric Name)
account.tax.repartition.line.repartition_type = tax is thuế (of tax)
account.tax.repartition.line.document_type = invoice is Hóa đơn (Invoice)
account.tax.repartition.line.document_type = refund is Đơn hoàn tiền (Refund)
account.reconcile.model.line.amount_type = fixed is Cố định (Fixed)
account.reconcile.model.line.amount_type = percentage is Phần trăm của số dư (Percentage of balance)
bus.presence.status = away is Vắng mặt (Away)
bus.presence.status = offline is Offline (Offline)
bus.presence.status = online is Online (Online)
onboarding.onboarding.step.current_step_state = not_done is Chưa xong (Not done)
onboarding.progress.step.step_state = not_done is Chưa xong (Not done)
account.move.line.display_type = cogs is Giá vốn hàng bán (Cost of Goods Sold)
account.move.line.display_type = discount is Chiết khấu (Discount)
account.move.line.display_type = rounding is Làm tròn (Rounding)
account.move.line.display_type = payment_term is Điều khoản thanh toán (Payment Term)
account.move.line.display_type = line_section is Phần (Section)
account.account.type.type = income_other is Thu nhập khác (Other Income)
account.account.type.type = expense is Chi phí (Expenses)
account.account.type.type = expense_depreciation is Khấu hao (Depreciation)
account.account.type.type = expense_direct_cost is Giá vốn hàng bán (Cost of Revenue)
account.account.type.type = off_balance is Ngoại bảng (Off-Balance Sheet)
account.financial.report.type = sum is Xem tổng hợp (View)
account.financial.report.type = accounts is Tài khoản (Accounts)
account.financial.report.type = account_type is Loại tài khoản (Account Type)
account.financial.report.type = account_report is Giá trị báo cáo (Report Value)
account.financial.report.sign = -1 is Đảo ngược dấu số dư (Reverse balance sign)
account.financial.report.sign = 1 is Giữ nguyên dấu số dư (Preserve balance sign)
account.financial.report.display_detail = no_detail is Không có chi tiết (No detail)
account.financial.report.display_detail = detail_flat is Hiển thị các mục con phẳng (Display children flat)
account.financial.report.display_detail = detail_with_hierarchy is Hiển thị các mục con theo cấp bậc (Display children with hierarchy)
account.financial.report.style_overwrite = 0 is Định dạng tự động (Automatic formatting)
ir.ui.menu.action = ir.actions.act_window is ir.actions.act_window (ir.actions.act_window)
ir.ui.menu.action = ir.actions.act_url is ir.actions.act_url (ir.actions.act_url)
ir.ui.menu.action = ir.actions.server is ir.actions.server (ir.actions.server)
ir.ui.menu.action = ir.actions.client is ir.actions.client (ir.actions.client)
ir.ui.view.type = list is Danh sách (List)
ir.ui.view.type = form is Biểu mẫu (Form)
ir.ui.view.type = graph is Biểu đồ (Graph)
ir.ui.view.type = pivot is Pivot (Pivot)
ir.ui.view.type = calendar is Lịch (Calendar)
ir.ui.view.type = kanban is Kanban (Kanban)
res.partner.tz = America/North_Dakota/Beulah is Châu Mỹ/North_Dakota/Beulah (America/North_Dakota/Beulah)
res.partner.tz = America/North_Dakota/Center is Châu Mỹ/North_Dakota/Center (America/North_Dakota/Center)
res.partner.tz = America/North_Dakota/New_Salem is Châu Mỹ/North_Dakota/New_Salem (America/North_Dakota/New_Salem)
res.partner.tz = America/Nuuk is Châu Mỹ/Nuuk (America/Nuuk)
res.partner.tz = America/Ojinaga is Châu Mỹ/Ojinaga (America/Ojinaga)
res.partner.tz = America/Panama is Châu Mỹ/Panama (America/Panama)
res.partner.tz = America/Pangnirtung is Châu Mỹ/Pangnirtung (America/Pangnirtung)
res.partner.tz = America/Paramaribo is Châu Mỹ/Paramaribo (America/Paramaribo)
res.partner.tz = America/Phoenix is Châu Mỹ/Phoenix (America/Phoenix)
account.move.line.display_type = line_note is Ghi chú (Note)
account.move.line.display_type = epd is Chiết khấu thanh toán sớm (Early Payment Discount)
account.payment.state = draft is Nháp (Draft)
account.payment.state = in_process is Đang thực hiện (In Process)
account.payment.state = paid is Đã thanh toán (Paid)
account.payment.state = canceled is Đã huỷ (Canceled)
account.payment.state = rejected is Bị từ chối (Rejected)
account.payment.payment_type = outbound is Gửi (Send)
account.payment.payment_type = inbound is Nhận (Receive)
account.payment.partner_type = customer is Khách hàng (Customer)
account.payment.partner_type = supplier is Nhà cung cấp (Vendor)
account.payment.reconciled_invoices_type = credit_note is Giấy báo có (Credit Note)
account.payment.reconciled_invoices_type = invoice is Hóa đơn (Invoice)
account.payment.method.payment_type = inbound is Nhận về (Inbound)
account.payment.method.payment_type = outbound is Gửi đi (Outbound)
account.analytic.applicability.business_domain = invoice is Hóa đơn (Invoice)
account.analytic.applicability.business_domain = bill is Hóa đơn mua hàng (Vendor Bill)
account.analytic.line.category = invoice is Hóa đơn bán hàng (Customer Invoice)
ir.ui.view.type = search is Tìm kiếm (Search)
account.analytic.line.category = vendor_bill is Hóa đơn mua hàng (Vendor Bill)
account.cash.rounding.strategy = biggest_tax is Chỉnh tiền thuế (Modify tax amount)
account.cash.rounding.strategy = add_invoice_line is Thêm một dòng làm tròn (Add a rounding line)
account.cash.rounding.rounding_method = DOWN is Xuống (DOWN)
account.cash.rounding.rounding_method = HALF-UP is LÊN MỘT NỬA (HALF-UP)
account.report.availability_condition = country is Quốc gia trùng khớp (Country Matches)
account.report.availability_condition = coa is Hệ thống tài khoản phù hợp (Chart of Accounts Matches)
ir.ui.view.type = qweb is QWeb (QWeb)
ir.ui.view.mode = primary is Chế độ xem cơ sở (Base view)
ir.ui.view.mode = extension is Chế độ xem Mở rộng (Extension View)
reset.view.arch.wizard.reset_mode = soft is Khôi phục phiên bản trước (soft reset). (Restore previous version (soft reset).)
reset.view.arch.wizard.reset_mode = hard is Đặt lại về phiên bản tệp (khôi phục cài đặt gốc). (Reset to file version (hard reset).)
reset.view.arch.wizard.reset_mode = other_view is Đặt lại thành chế độ xem khác. (Reset to another view.)
ir.asset.directive = append is Append (Append)
ir.asset.directive = prepend is Prepend (Prepend)
ir.asset.directive = after is Sau (After)
ir.asset.directive = before is Trước (Before)
ir.asset.directive = remove is Xóa (Remove)
ir.asset.directive = replace is Thay thế (Replace)
ir.asset.directive = include is Bao gồm (Include)
ir.actions.actions.binding_type = action is Tác vụ (Action)
ir.actions.actions.binding_type = report is Báo cáo (Report)
ir.actions.act_window.binding_type = action is Tác vụ (Action)
ir.actions.act_window.view.view_mode = list is Danh sách (List)
hr.employee.hr_icon_display = presence_absent is Vắng mặt (Absent)
hr.employee.hr_presence_state = absent is Vắng mặt (Absent)
hr.employee.base.hr_icon_display = presence_absent is Vắng mặt (Absent)
account.report.availability_condition = always is Luôn luôn (Always)
account.report.integer_rounding = HALF-UP is Lên một nửa (xa 0) (Half-up (away from 0))
account.report.integer_rounding = UP is Lên (Up)
account.report.integer_rounding = DOWN is Xuống (Down)
account.report.default_opening_date_filter = this_year is Năm nay (This Year)
account.report.default_opening_date_filter = this_quarter is Quý này (This Quarter)
account.report.default_opening_date_filter = this_month is Tháng này (This Month)
account.report.default_opening_date_filter = today is Hôm nay (Today)
account.report.default_opening_date_filter = previous_month is Tháng trước (Last Month)
account.report.default_opening_date_filter = previous_quarter is Quý trước (Last Quarter)
account.report.default_opening_date_filter = previous_year is Năm trước (Last Year)
account.report.default_opening_date_filter = this_tax_period is Kỳ thuế này (This Tax Period)
account.report.default_opening_date_filter = previous_tax_period is Kỳ thuế cuối cùng (Last Tax Period)
account.report.currency_translation = current is Sử dụng tỷ giá mới nhất tại thời điểm báo cáo (Use the most recent rate at the date of the report)
account.report.currency_translation = cta is Sử dụng CTA (Use CTA)
account.report.filter_multi_company = disabled is Vô hiệu hoá (Disabled)
account.invoice.report.state = draft is Nháp (Draft)
account.report.filter_multi_company = selector is Sử dụng bộ chọn công ty (Use Company Selector)
barcode.rule.type = weight is Sản phẩm đã cân (Weighted Product)
stock.putaway.rule.sublocation = last_used is Được sử dụng cuối cùng (Last Used)
stock.putaway.rule.sublocation = closest_location is Địa điểm gần nhất (Closest Location)
res.company.annual_inventory_month = 1 is Tháng 1 (January)
res.company.annual_inventory_month = 2 is Tháng 2 (February)
res.company.annual_inventory_month = 3 is Tháng 3 (March)
res.company.annual_inventory_month = 4 is Tháng 4 (April)
res.company.annual_inventory_month = 5 is Tháng 5 (May)
res.company.annual_inventory_month = 6 is Tháng 6 (June)
account.report.filter_multi_company = tax_units is Sử dụng đơn vị thuế (Use Tax Units)
account.report.filter_hide_0_lines = by_default is Được bật theo mặc định (Enabled by Default)
account.report.filter_hide_0_lines = optional is Tuỳ chọn (Optional)
account.report.filter_hide_0_lines = never is Không bao giờ (Never)
account.report.filter_hierarchy = by_default is Được bật theo mặc định (Enabled by Default)
account.report.filter_hierarchy = optional is Tuỳ chọn (Optional)
account.report.filter_hierarchy = never is Không bao giờ (Never)
ir.actions.server.binding_type = report is Báo cáo (Report)
ir.actions.server.usage = ir_actions_server is Tác vụ phía máy chủ (Server Action)
account.report.filter_account_type = both is Phải trả và phải thu (Payable and receivable)
account.report.filter_account_type = payable is Phải trả (Payable)
account.report.filter_account_type = receivable is Phải thu (Receivable)
account.report.filter_account_type = disabled is Vô hiệu hoá (Disabled)
account.report.line.horizontal_split_side = left is Trái (Left)
account.report.line.horizontal_split_side = right is Phải (Right)
account.report.expression.engine = domain is Miền Odoo (Odoo Domain)
account.report.expression.engine = tax_tags is Thẻ thuế (Tax Tags)
account.report.expression.engine = aggregation is Tổng hợp các công thức khác (Aggregate Other Formulas)
account.report.expression.engine = account_codes is Tiền tố của mã tài khoản (Prefix of Account Codes)
onboarding.onboarding.current_onboarding_state = done is Hoàn tất (Done)
ir.actions.server.usage = ir_cron is Tác vụ đã lên lịch (Scheduled Action)
ir.actions.server.state = object_write is Cập nhật bản ghi (Update Record)
ir.actions.server.update_m2m_operation = add is Thêm (Adding)
ir.actions.server.update_m2m_operation = remove is Xoá (Removing)
ir.actions.server.update_m2m_operation = set is Cài đặt thành (Setting it to)
ir.actions.server.update_m2m_operation = clear is Xoá (Clearing it)
ir.actions.server.update_boolean_value = true is Có (Đúng) (Yes (True))
ir.actions.server.update_boolean_value = false is Không (Sai) (No (False))
ir.actions.server.evaluation_type = value is Cập nhật (Update)
ir.actions.server.evaluation_type = equation is Tính toán (Compute)
ir.actions.server.value_field_to_show = value is giá trị (value)
ir.actions.server.value_field_to_show = resource_ref is tham chiếu (reference)
ir.actions.server.value_field_to_show = update_boolean_value is update_boolean_value (update_boolean_value)
ir.actions.server.value_field_to_show = selection_value is selection_value (selection_value)
ir.actions.todo.state = open is Cần làm (To Do)
ir.actions.todo.state = done is Hoàn tất (Done)
resource.calendar.attendance.day_period = afternoon is Buổi chiều (Afternoon)
resource.calendar.attendance.day_period = lunch is Nghỉ (Break)
resource.calendar.attendance.week_type = 0 is Đầu tiên (First)
resource.calendar.attendance.dayofweek = 4 is Thứ Sáu (Friday)
resource.resource.resource_type = user is Con người (Human)
resource.resource.resource_type = material is Nguyên liệu/Máy móc (Material)
ir.model.fields.ttype = job_serialized is Job Serialized
account.report.expression.engine = external is Giá trị ngoài (External Value)
account.report.expression.engine = custom is Hàm python tùy chỉnh (Custom Python Function)
account.report.expression.date_scope = from_beginning is Từ đầu (From the very start)
account.report.expression.date_scope = from_fiscalyear is Từ đầu năm tài chính (From the start of the fiscal year)
account.report.expression.date_scope = to_beginning_of_fiscalyear is Vào đầu năm tài chính (At the beginning of the fiscal year)
account.report.expression.date_scope = to_beginning_of_period is Vào đầu kỳ (At the beginning of the period)
account.report.expression.date_scope = strict_range is Đúng vào những ngày nhất định (Strictly on the given dates)
account.report.expression.date_scope = previous_tax_period is Từ kỳ tính thuế trước (From previous tax period)
account.report.expression.figure_type = monetary is Tiền tệ (Monetary)
account.report.expression.figure_type = percentage is Phần trăm (Percentage)
res.partner.peppol_eas = 0096 is 0096 - Cơ chế PHÒNG THƯƠNG MẠI ĐAN MẠCH (tuân thủ EDIRA) (0096 - DANISH CHAMBER OF COMMERCE Scheme (EDIRA compliant))
res.partner.peppol_eas = 0097 is 0097 - FTI - Ediforum Ý, (tuân thủ EDIRA) (0097 - FTI - Ediforum Italia, (EDIRA compliant))
account.report.expression.figure_type = integer is Số nguyên (Integer)
ir.actions.client.binding_type = report is Báo cáo (Report)
ir.actions.client.target = current is Cửa sổ Hiện tại (Current Window)
ir.actions.client.target = new is Cửa sổ mới (New Window)
ir.actions.client.target = fullscreen is Toàn Màn hình (Full Screen)
ir.actions.client.target = main is Hoạt động chính của cửa sổ hiện tại (Main action of Current Window)
ir.actions.report.binding_type = action is Tác vụ (Action)
ir.actions.report.binding_type = report is Báo cáo (Report)
ir.actions.report.report_type = qweb-html is HTML (HTML)
ir.actions.report.report_type = qweb-pdf is PDF (PDF)
ir.actions.report.report_type = qweb-text is Văn bản (Text)
ir.attachment.type = url is URL (URL)
ir.attachment.type = binary is Tệp (File)
barcode.rule.gs1_content_type = date is Ngày (Date)
barcode.rule.encoding = gs1-128 is GS1-128 (GS1-128)
barcode.rule.gs1_content_type = measure is Số đo (Measure)
account.report.expression.figure_type = float is Float (Float)
account.report.expression.figure_type = date is Ngày (Date)
account.report.expression.figure_type = datetime is Ngày giờ (Datetime)
account.report.expression.figure_type = boolean is Boolean (Boolean)
account.report.expression.figure_type = string is Chuỗi (String)
account.report.column.figure_type = monetary is Tiền tệ (Monetary)
account.report.column.figure_type = percentage is Phần trăm (Percentage)
account.report.column.figure_type = integer is Số nguyên (Integer)
account.report.column.figure_type = float is Float (Float)
account.report.column.figure_type = date is Ngày (Date)
account.report.column.figure_type = datetime is Ngày giờ (Datetime)
account.report.column.figure_type = boolean is Boolean (Boolean)
account.report.column.figure_type = string is Chuỗi (String)
account.automatic.entry.wizard.action = change_period is Đổi kỳ (Change Period)
account.automatic.entry.wizard.action = change_account is Đổi tài khoản (Change Account)
account.automatic.entry.wizard.account_type = income is Doanh thu (Revenue)
account.automatic.entry.wizard.account_type = expense is Chi phí (Expense)
account.resequence.wizard.ordering = keep is Giữ thứ tự hiện tại (Keep current order)
account.resequence.wizard.ordering = date is Sắp xếp lại theo ngày hạch toán (Reorder by accounting date)
account.payment.register.installments_mode = next is Khoản trả góp tiếp theo (Next Installment)
account.payment.register.installments_mode = overdue is Số tiền quá hạn (Overdue Amount)
account.payment.register.installments_mode = before_date is Trước ngày thanh toán tiếp theo (Before Next Payment Date)
account.payment.register.installments_mode = full is Toàn bộ số tiền (Full Amount)
account.payment.register.payment_type = outbound is Gửi tiền (Send Money)
account.payment.register.payment_type = inbound is Nhận tiền (Receive Money)
account.payment.register.partner_type = customer is Khách hàng (Customer)
account.payment.register.partner_type = supplier is Nhà cung cấp (Vendor)
account.payment.register.payment_difference_handling = open is Để mở (Keep open)
account.payment.register.payment_difference_handling = reconcile is Đánh dấu là đã thanh toán đủ (Mark as fully paid)
account.merge.wizard.line.display_type = line_section is Phần (Section)
account.merge.wizard.line.display_type = account is Tài khoản (Account)
account.invoice.report.move_type = out_invoice is Hóa đơn bán hàng (Customer Invoice)
account.invoice.report.move_type = in_invoice is Hóa đơn mua hàng (Vendor Bill)
account.invoice.report.move_type = out_refund is Giấy báo có khách hàng (Customer Credit Note)
account.invoice.report.move_type = in_refund is Giấy báo có nhà cung cấp (Vendor Credit Note)
account.invoice.report.state = posted is Mở (Open)
account.invoice.report.state = cancel is Đã hủy (Cancelled)
account.invoice.report.payment_state = not_paid is Chưa thanh toán (Not Paid)
res.partner.bank.proxy_type = atm_card is Số thẻ ATM (ATM Card Number)
ir.module.module.license = GPL-3 is GPL phiên bản 3 (GPL Version 3)
ir.module.module.license = GPL-3 or any later version is GPL-3 hoặc phiên bản mới hơn (GPL-3 or later version)
ir.module.module.license = AGPL-3 is Affero GPL-3 (Affero GPL-3)
ir.module.module.license = LGPL-3 is LGPL Phiên bản 3 (LGPL Version 3)
ir.module.module.license = Other OSI approved licence is Giấy phép khác được OSI chấp thuận (Other OSI Approved License)
ir.module.module.license = OEEL-1 is Giấy phép Phiên bản Odoo Enterprise v1.0 (Odoo Enterprise Edition License v1.0)
ir.module.module.license = OPL-1 is Giấy phép Độc quyền Odoo v1.0 (Odoo Proprietary License v1.0)
ir.module.module.license = Other proprietary is Chứng nhận Độc quyền khác (Other Proprietary)
ir.module.module.dependency.state = uninstallable is Không thể cài đặt được (Uninstallable)
ir.module.module.dependency.state = uninstalled is Chưa cài đặt (Not Installed)
ir.module.module.dependency.state = installed is Đã cài đặt (Installed)
ir.module.module.dependency.state = to upgrade is Sẽ được nâng cấp (To be upgraded)
mail.alias.alias_contact = everyone is Mọi người (Everyone)
account.invoice.report.payment_state = in_payment is Đang thanh toán (In Payment)
res.partner.peppol_eas = 0002 is 0002 - System Information et Repertoire des Entreprise et des Etablissements: SIRENE (0002 - System Information et Repertoire des Entreprise et des Etablissements: SIRENE)
account.invoice.report.payment_state = paid is Đã thanh toán (Paid)
account.invoice.report.payment_state = partial is Đã thanh toán một phần (Partially Paid)
res.partner.peppol_eas = 0007 is 0007 - Mã số tổ chức (Pháp nhân Thụy Điển) (0007 - Organisationsnummer (Swedish legal entities))
account.invoice.report.payment_state = reversed is Đã đảo (Reversed)
account.invoice.report.payment_state = blocked is Đã bị chặn (Blocked)
account.invoice.report.payment_state = invoicing_legacy is Kế thừa ứng dụng hóa đơn (Invoicing App Legacy)
hr.employee.base.hr_presence_state = absent is Vắng mặt (Absent)
hr.employee.public.hr_presence_state = present is Có mặt (Present)
hr.employee.employee_type = student is Học sinh (Student)
hr.employee.employee_type = trainee is Thực tập sinh (Trainee)
hr.employee.hr_icon_display = presence_undetermined is Không xác định (Undetermined)
hr.employee.base.hr_icon_display = presence_undetermined is Không xác định (Undetermined)
report.paperformat.format = A6 is A6  10  105 x 148 mm (A6  10  105 x 148 mm)
report.paperformat.format = A7 is A7  11  74 x 105 mm (A7  11  74 x 105 mm)
report.paperformat.format = A8 is A8  12  52 x 74 mm (A8  12  52 x 74 mm)
report.paperformat.format = A9 is A9  13  37 x 52 mm (A9  13  37 x 52 mm)
report.paperformat.format = B0 is B0  14  1000 x 1414 mm (B0  14  1000 x 1414 mm)
report.paperformat.format = B1 is B1  15  707 x 1000 mm (B1  15  707 x 1000 mm)
report.paperformat.format = B2 is B2  17  500 x 707 mm (B2  17  500 x 707 mm)
report.paperformat.format = B3 is B3  18  353 x 500 mm (B3  18  353 x 500 mm)
report.paperformat.format = B4 is B4  19  250 x 353 mm (B4  19  250 x 353 mm)
report.paperformat.format = B5 is B5  1   176 x 250 mm, 6.93 x 9.84 inches (B5  1   176 x 250 mm, 6.93 x 9.84 inches)
report.paperformat.format = B6 is B6  20  125 x 176 mm (B6  20  125 x 176 mm)
report.paperformat.format = B7 is B7  21  88 x 125 mm (B7  21  88 x 125 mm)
report.paperformat.format = B8 is B8  22  62 x 88 mm (B8  22  62 x 88 mm)
report.paperformat.format = B9 is B9  23  33 x 62 mm (B9  23  33 x 62 mm)
report.paperformat.format = B10 is B10    16  31 x 44 mm (B10    16  31 x 44 mm)
report.paperformat.format = C5E is C5E 24  163 x 229 mm (C5E 24  163 x 229 mm)
report.paperformat.format = Comm10E is Comm10E 25  105 x 241 mm, U.S. Common 10 Envelope (Comm10E 25  105 x 241 mm, U.S. Common 10 Envelope)
report.paperformat.format = DLE is DLE 26 110 x 220 mm (DLE 26 110 x 220 mm)
res.company.annual_inventory_month = 7 is Tháng 7 (July)
report.paperformat.format = Executive is Điều hành 4 7,5 x 10 inch, 190,5 x 254 mm (Executive 4   7.5 x 10 inches, 190.5 x 254 mm)
report.paperformat.format = Folio is Folio 27  210 x 330 mm (Folio 27  210 x 330 mm)
res.company.annual_inventory_month = 8 is Tháng 8 (August)
res.company.annual_inventory_month = 9 is Tháng 9 (September)
report.paperformat.format = Ledger is Ledger  28  431.8 x 279.4 mm (Ledger  28  431.8 x 279.4 mm)
report.paperformat.format = Legal is Pháp lý    3   8,5 x 14 inch, 215,9 x 355,6 mm (Legal    3   8.5 x 14 inches, 215.9 x 355.6 mm)
report.paperformat.format = Letter is Letter 2 8.5 x 11 inches, 215.9 x 279.4 mm (Letter 2 8.5 x 11 inches, 215.9 x 279.4 mm)
res.company.annual_inventory_month = 10 is Tháng 10 (October)
res.company.annual_inventory_month = 11 is Tháng 11 (November)
res.company.annual_inventory_month = 12 is Tháng 12 (December)
stock.location.usage = inventory is Thất thoát tồn kho (Inventory Loss)
stock.move.priority = 0 is Bình thường (Normal)
report.paperformat.format = Tabloid is Tabloid 29 279.4 x 431.8 mm (Tabloid 29 279.4 x 431.8 mm)
report.paperformat.format = custom is Tùy chỉnh (Custom)
report.paperformat.orientation = Landscape is Ngang (Landscape)
mail.activity.plan.template.delay_from = after_plan_date is Sau ngày kế hoạch (After Plan Date)
mail.mail.state = sent is Đã gửi (Sent)
stock.move.state = partially_available is Có hàng một phần (Partially Available)
stock.move.state = assigned is Có hàng (Available)
stock.move.state = done is Hoàn tất (Done)
stock.move.state = cancel is Đã hủy (Cancelled)
stock.move.procure_method = make_to_stock is Mặc định: Lấy từ tồn kho (Default: Take From Stock)
stock.move.procure_method = make_to_order is Nâng cao: Áp dụng quy tắc mua sắm (Advanced: Apply Procurement Rules)
stock.rule.group_propagation_option = none is Để trống (Leave Empty)
stock.rule.group_propagation_option = propagate is Thông báo (Propagate)
stock.rule.group_propagation_option = fixed is Cố định (Fixed)
stock.rule.action = pull is Kéo từ (Pull From)
res.country.name_position = before is Trước Địa chỉ (Before Address)
res.country.name_position = after is Sau Địa chỉ (After Address)
res.lang.direction = ltr is Trái sang Phải (Left-to-Right)
res.lang.direction = rtl is Phải sang Trái (Right-to-Left)
res.lang.week_start = 1 is Thứ Hai (Monday)
res.lang.week_start = 2 is Thứ Ba (Tuesday)
res.lang.week_start = 3 is Thứ Tư (Wednesday)
stock.rule.action = push is Đẩy tới (Push To)
stock.rule.action = pull_push is Kéo và đẩy (Pull & Push)
stock.rule.procure_method = make_to_stock is Lấy từ tồn kho (Take From Stock)
stock.rule.procure_method = make_to_order is Kích hoạt một quy tắc khác (Trigger Another Rule)
stock.rule.procure_method = mts_else_mto is Lấy từ tồn kho, nếu không có, kích hoạt quy tắc khác (Take From Stock, if unavailable, Trigger Another Rule)
stock.rule.auto = manual is Hoạt động thủ công (Manual Operation)
stock.rule.auto = transparent is Không có bước nào được thêm tự động (Automatic No Step Added)
procurement.group.move_type = direct is Một phần (Partial)
procurement.group.move_type = one is Cùng một lúc (All at once)
stock.warehouse.orderpoint.trigger = auto is Tự động (Auto)
stock.warehouse.orderpoint.trigger = manual is Thủ công (Manual)
stock.picking.type.product_label_format = dymo is Dymo (Dymo)
stock.picking.type.product_label_format = 2x7xprice is 2 x 7 kèm giá (2 x 7 with price)
stock.picking.type.product_label_format = 4x7xprice is 4 x 7 kèm giá (4 x 7 with price)
stock.picking.type.product_label_format = 4x12 is 4 x 12 (4 x 12)
stock.picking.type.product_label_format = 4x12xprice is 4 x 12 kèm giá (4 x 12 with price)
stock.picking.type.product_label_format = zplxprice is Nhãn ZPL có giá (ZPL Labels with price)
stock.picking.type.lot_label_format = zpl_units is Nhãn ZPL - Một trên mỗi đơn vị (ZPL Labels - One per unit)
stock.picking.priority = 1 is Khẩn cấp (Urgent)
stock.picking.products_availability_state = expected is Dự kiến (Expected)
stock.picking.products_availability_state = late is Trễ (Late)
stock.picking.search_date_category = before is Trước (Before)
stock.picking.search_date_category = yesterday is Hôm qua (Yesterday)
product.template.tracking = lot is Theo lô (By Lots)
stock.package_level.state = draft is Nháp (Draft)
report.stock.quantity.state = in is Nhập kho dự báo (Forecasted Receipts)
report.stock.quantity.state = out is Xuất kho dự báo (Forecasted Deliveries)
product.label.layout.print_format = zpl is Nhãn ZPL (ZPL Labels)
product.label.layout.print_format = zplxprice is Nhãn ZPL có giá (ZPL Labels with price)
product.label.layout.move_quantity = move is Số lượng hoạt động (Operation Quantities)
product.label.layout.move_quantity = custom is Tùy chỉnh (Custom)
picking.label.type.label_type = products is Nhãn sản phẩm (Product Labels)
picking.label.type.label_type = lots is Nhãn số lô/sê-ri (Lot/SN Labels)
lot.label.layout.label_quantity = lots is Một trên mỗi số lô/sê-ri (One per lot/SN)
lot.label.layout.label_quantity = units is Một trên mỗi đơn vị (One per unit)
res.lang.week_start = 4 is Thứ năm (Thursday)
res.lang.week_start = 5 is Thứ Sáu (Friday)
res.lang.week_start = 6 is Thứ Bảy (Saturday)
res.partner.tz = Africa/Abidjan is Châu Phi/Abidjan (Africa/Abidjan)
uom.uom.uom_type = bigger is Lớn hơn đơn vị đo lường gốc (Bigger than the reference Unit of Measure)
uom.uom.uom_type = reference is Đơn vị gốc của nhóm này (Reference Unit of Measure for this category)
resource.calendar.attendance.week_type = 1 is Giây (Second)
resource.calendar.attendance.display_type = line_section is Phần (Section)
resource.calendar.attendance.dayofweek = 6 is Chủ nhật (Sunday)
resource.calendar.leaves.time_type = leave is Nghỉ phép (Time Off)
resource.calendar.attendance.dayofweek = 1 is Thứ Ba (Tuesday)
resource.calendar.attendance.dayofweek = 2 is Thứ Tư (Wednesday)
barcode.rule.type = quantity is Số lượng (Quantity)
iap.account.state = banned is Đã cấm (Banned)
iap.account.state = registered is Đã đăng ký (Registered)
iap.account.state = unregistered is Chưa đăng ký (Unregistered)
ir.actions.server.mail_post_method = email is Email (Email)
res.partner.tz = Africa/Juba is Châu Phi/Juba (Africa/Juba)
discuss.channel.default_display_mode = video_full_screen is Video toàn màn hình (Full screen video)
res.partner.activity_state = overdue is Quá hạn (Overdue)
res.partner.activity_state = today is Hôm nay (Today)
res.partner.activity_state = planned is Đã lên kế hoạch (Planned)
res.partner.tz = Africa/Kampala is Châu Phi/Kampala (Africa/Kampala)
res.partner.tz = Africa/Khartoum is Châu Phi/Khartoum (Africa/Khartoum)
res.partner.tz = Africa/Kigali is Châu Phi/Kigali (Africa/Kigali)
res.partner.tz = Africa/Kinshasa is Châu Phi/Kinshasa (Africa/Kinshasa)
res.users.state = active is Đã xác nhận (Confirmed)
res.config.settings.auth_signup_uninvited = b2c is Đăng ký miễn phí (Free sign up)
res.users.state = new is Chưa từng kết nối (Never Connected)
res.config.settings.auth_signup_uninvited = b2b is Khi mời (On invitation)
fetchmail.server.server_type = gmail is Xác thực OAuth Gmail (Gmail OAuth Authentication)
ir.mail_server.smtp_authentication = gmail is Xác thực OAuth Gmail (Gmail OAuth Authentication)
res.users.odoobot_state = disabled is Đã vô hiệu (Disabled)
res.users.odoobot_state = idle is Rảnh rỗi (Idle)
digest.digest.state = activated is Đã kích hoạt (Activated)
digest.digest.periodicity = daily is Hàng ngày (Daily)
digest.digest.state = deactivated is Vô hiệu hóa (Deactivated)
digest.digest.periodicity = monthly is Hàng tháng (Monthly)
payment.provider.support_refund = full_only is Full Only (Full Only)
lot.label.layout.print_format = 4x12 is 4 x 12 (4 x 12)
lot.label.layout.print_format = zpl is Nhãn ZPL (ZPL Labels)
stock.orderpoint.snooze.predefined_date = day is 1 Ngày (1 Day)
stock.orderpoint.snooze.predefined_date = week is 1 Tuần (1 Week)
stock.orderpoint.snooze.predefined_date = month is 1 Tháng (1 Month)
stock.orderpoint.snooze.predefined_date = custom is Tùy chỉnh (Custom)
stock.request.count.set_count = empty is Để trống (Leave Empty)
stock.request.count.set_count = set is Thiết lập giá trị hiện tại (Set Current Value)
res.partner.peppol_eas = 0009 is 0009 - MÃ-SIRET (0009 - SIRET-CODE)
res.partner.peppol_eas = 0060 is 0060 - Hệ thống mã số dữ liệu toàn cầu (Số D-U-N-S) (0060 - Data Universal Numbering System (D-U-N-S Number))
res.partner.peppol_eas = 0088 is 0088 - Mã số vị trí EAN (0088 - EAN Location Code)
res.partner.tz = Africa/Lagos is Châu Phi/Lagos (Africa/Lagos)
res.partner.tz = Africa/Libreville is Châu Phi/Libreville (Africa/Libreville)
res.partner.tz = Africa/Lome is Châu Phi/Lome (Africa/Lome)
res.partner.tz = Africa/Luanda is Châu Phi/Luanda (Africa/Luanda)
res.partner.tz = Africa/Lubumbashi is Châu Phi/Lubumbashi (Africa/Lubumbashi)
res.partner.activity_exception_decoration = warning is Cảnh báo (Alert)
res.partner.activity_exception_decoration = danger is Lỗi (Error)
mail.compose.message.composition_mode = comment is Đưa vào tài liệu (Post on a document)
web_editor.converter.test.selection_str = C is Qu'est-ce qu'il fout ce maudit pancake, tabernacle ? (Qu'est-ce qu'il fout ce maudit pancake, tabernacle ?)
web_editor.converter.test.selection_str = A is Qu'il n'est pas arrivé à Toronto (Qu'il n'est pas arrivé à Toronto)
web_editor.converter.test.selection_str = B is Qu'il était supposé arriver à Toronto (Qu'il était supposé arriver à Toronto)
account.analytic.applicability.applicability = mandatory is Bắt buộc (Mandatory)
account.analytic.plan.default_applicability = mandatory is Bắt buộc (Mandatory)
account.analytic.applicability.business_domain = general is Thông tin khác (Miscellaneous)
account.analytic.plan.default_applicability = optional is Tuỳ chọn (Optional)
account.analytic.plan.default_applicability = unavailable is Không khả dụng (Unavailable)
res.users.odoobot_state = not_initialized is Không được khởi tạo (Not initialized)
mail.compose.message.composition_mode = mass_mail is Email Gửi thư hàng loạt (Email Mass Mailing)
mail.compose.message.message_type = auto_comment is Thông báo được nhắm mục tiêu tự động (Automated Targeted Notification)
res.users.odoobot_state = onboarding_attachement is Onboarding Tệp đính kèm (Onboarding attachment)
res.users.odoobot_state = onboarding_canned is Onboarding canned (Onboarding canned)
res.partner.peppol_eas = 0106 is 0106 - Hiệp hội Phòng Thương mại và Công nghiệp Hà Lan, (tuân thủ EDIRA) (0106 - Association of Chambers of Commerce and Industry in the Netherlands, (EDIRA compliant))
res.partner.peppol_eas = 0135 is 0135 - Mã định danh đối tượng SIA (0135 - SIA Object Identifiers)
update.product.attribute.value.mode = update_extra_price is Cập nhật giá bổ sung cho các sản phẩm hiện có (Update the extra price on existing products)
res.partner.tz = Africa/Lusaka is Châu Phi/Lusaka (Africa/Lusaka)
res.partner.tz = Africa/Malabo is Châu Phi/Malabo (Africa/Malabo)
res.partner.tz = Africa/Maputo is Châu Phi/Maputo (Africa/Maputo)
res.partner.tz = Africa/Maseru is Châu Phi/Maseru (Africa/Maseru)
res.partner.tz = Africa/Mbabane is Châu Phi/Mbabane (Africa/Mbabane)
res.partner.tz = Africa/Mogadishu is Châu Phi/Mogadishu (Africa/Mogadishu)
sparse_fields.test.selection = one is Một (One)
sparse_fields.test.selection = two is Hai (Two)
ir.model.fields.ttype = serialized is tuần tự hoá (serialized)
product.pricelist.item.applied_on = 2_product_category is Danh mục sản phẩm (Product Category)
product.pricelist.item.applied_on = 0_product_variant is Biến thể sản phẩm (Product Variant)
product.attribute.display_type = radio is Radio (Radio)
product.pricelist.item.base = list_price is Giá bán (Sales Price)
product.attribute.display_type = select is Chọn (Select)
mail.notification.failure_type = sms_not_allowed is Được cho phép (Not Allowed)
res.partner.peppol_eas = 0209 is 0209 - khoá định danh GS1 (0209 - GS1 identification keys)
res.partner.peppol_eas = 0210 is 0210 - CODICE FISCALE (0210 - CODICE FISCALE)
res.partner.tz = Africa/Monrovia is Châu Phi/Monrovia (Africa/Monrovia)
res.partner.tz = Africa/Nairobi is Châu Phi/Nairobi (Africa/Nairobi)
res.partner.tz = Africa/Ndjamena is Châu Phi/Ndjamena (Africa/Ndjamena)
res.partner.peppol_eas = 0211 is 0211 - PARTITA IVA (0211 - PARTITA IVA)
res.partner.peppol_eas = 0216 is 0216 - mã OVT (0216 - OVTcode)
res.partner.peppol_eas = 0221 is 0221 - Số đăng ký của tổ chức phát hành hóa đơn đủ điều kiện (Nhật Bản) (0221 - The registered number of the qualified invoice issuer (Japan))
res.partner.peppol_eas = 0230 is 0230 - Khung hóa đơn điện tử quốc gia (Malaysia) (0230 - National e-Invoicing Framework (Malaysia))
res.partner.peppol_eas = 9925 is 9925 - Mã số thuế GTGT của Bỉ (9925 - Belgium VAT number)
res.partner.peppol_eas = 9927 is 9927 - Mã số thuế GTGT của Thuỵ Sĩ (9927 - Switzerland VAT number)
res.partner.peppol_eas = 9928 is 9928 - Mã số thuế GTGT của Síp (9928 - Cyprus VAT number)
res.partner.peppol_eas = 9929 is 9929 - Mã số thuế GTGT của Cộng hoà Séc (9929 - Czech Republic VAT number)
res.partner.peppol_eas = 9930 is 9930 - Mã số thuế GTGT của Đức (9930 - Germany VAT number)
res.partner.peppol_eas = 9931 is 9931 - Mã số thuế GTGT của Estonia (9931 - Estonia VAT number)
res.partner.peppol_eas = 9932 is 9932 - Mã số thuế GTGT của Vương quốc Anh (9932 - United Kingdom VAT number)
res.partner.peppol_eas = 9936 is 9936 - Mã số thuế GTGT của Liechtenstein (9936 - Liechtenstein VAT number)
res.partner.tz = America/Port-au-Prince is Châu Mỹ/Port-au-Prince (America/Port-au-Prince)
res.partner.tz = America/Port_of_Spain is Châu Mỹ/Port_of_Spain (America/Port_of_Spain)
res.partner.tz = America/Porto_Acre is Châu Mỹ/Porto_Acre (America/Porto_Acre)
product.template.type = service is Dịch vụ (Service)
rating.mixin.rating_avg_text = ko is Không hài lòng (Dissatisfied)
rating.rating.rating_text = ko is Không hài lòng (Dissatisfied)
rating.mixin.rating_avg_text = none is Chưa có đánh giá     (No Rating yet)
rating.rating.rating_text = none is Chưa có đánh giá     (No Rating yet)
payment.refund.wizard.support_refund = partial is Một phần (Partial)
payment.refund.wizard.support_refund = none is Không hỗ trợ (Unsupported)
res.partner.tz = Africa/Niamey is Châu Phi/Niamey (Africa/Niamey)
res.partner.tz = Africa/Nouakchott is Châu Phi/Nouakchott (Africa/Nouakchott)
res.partner.tz = Africa/Ouagadougou is Châu Phi/Ouagadougou (Africa/Ouagadougou)
res.partner.tz = Africa/Porto-Novo is Châu Phi/Porto-Novo (Africa/Porto-Novo)
res.partner.tz = Africa/Sao_Tome is Châu Phi/Sao_Tome (Africa/Sao_Tome)
res.partner.tz = Africa/Timbuktu is Châu Phi/Timbuktu (Africa/Timbuktu)
res.partner.tz = Africa/Tripoli is Châu Phi/Tripoli (Africa/Tripoli)
res.partner.tz = Africa/Tunis is Châu Phi/Tunis (Africa/Tunis)
res.partner.tz = Africa/Windhoek is Châu Phi/Windhoek (Africa/Windhoek)
res.partner.tz = America/Adak is Châu Mỹ/Adak (America/Adak)
res.partner.tz = America/Anchorage is Châu Mỹ/Anchorage (America/Anchorage)
res.partner.tz = America/Anguilla is Châu Mỹ/Anguilla (America/Anguilla)
res.partner.tz = America/Antigua is Châu Mỹ/Antigua (America/Antigua)
account.reconcile.model.match_transaction_type = match_regex is Khớp Regex (Match Regex)
account.move.line.display_type = tax is Thuế (Tax)
res.partner.tz = America/Araguaina is Châu Mỹ/Araguaina (America/Araguaina)
res.partner.tz = America/Argentina/Buenos_Aires is Châu Mỹ/Argentina/Buenos_Aires (America/Argentina/Buenos_Aires)
res.partner.tz = America/Argentina/Catamarca is Châu Mỹ/Argentina/Catamarca (America/Argentina/Catamarca)
res.partner.tz = America/Porto_Velho is Châu Mỹ/Porto_Velho (America/Porto_Velho)
res.partner.tz = America/Puerto_Rico is Châu Mỹ/Puerto_Rico (America/Puerto_Rico)
res.partner.tz = America/Punta_Arenas is Châu Mỹ/Punta_Arenas (America/Punta_Arenas)
res.partner.peppol_eas = 9937 is 9937 - Mã số thuế GTGT của Lithuania (9937 - Lithuania VAT number)
res.partner.peppol_eas = 9938 is 9938 - Mã số thuế GTGT của Luxemburg (9938 - Luxemburg VAT number)
res.partner.peppol_eas = 9939 is 9939 - Mã số thuế GTGT của Latvia (9939 - Latvia VAT number)
res.partner.peppol_eas = 9940 is 9940 - Mã số thuế GTGT của Monaco (9940 - Monaco VAT number)
res.partner.peppol_eas = 9941 is 9941 - Mã số thuế GTGT của Montenegro (9941 - Montenegro VAT number)
res.partner.peppol_eas = 9942 is 9942 - Mã số thuế GTGT của Cộng hoà Macedonia thuộc Nam Tư cũ (9942 - Macedonia, the former Yugoslav Republic of VAT number)
res.partner.peppol_eas = 9943 is 9943 - Mã số thuế GTGT của Malta (9943 - Malta VAT number)
res.partner.peppol_eas = 9944 is 9944 - Mã số thuế GTGT của Hà Lan (9944 - Netherlands VAT number)
res.partner.peppol_eas = 9945 is 9945 - Mã số thuế GTGT của Ba Lan (9945 - Poland VAT number)
res.partner.peppol_eas = 9946 is 9946 - Mã số thuế GTGT của Bồ Đào Nha (9946 - Portugal VAT number)
res.partner.peppol_eas = 9947 is 9947 - Mã số thuế GTGT của Romania (9947 - Romania VAT number)
res.partner.peppol_eas = 9948 is 9948 - Mã số thuế GTGT của Serbia (9948 - Serbia VAT number)
res.partner.peppol_eas = 9949 is 9949 - Mã số thuế GTGT của Slovenia (9949 - Slovenia VAT number)
res.partner.peppol_eas = 9950 is 9950 - Mã số thuế GTGT của Slovakia (9950 - Slovakia VAT number)
product.margin.invoice_state = draft_open_paid is Nháp, mở và đã thanh toán (Draft, Open and Paid)
res.company.po_lock = edit is Cho phép sửa đơn mua hàng (Allow to edit purchase orders)
res.partner.peppol_eas = 9951 is 9951 - Mã số thuế GTGT của San Marino (9951 - San Marino VAT number)
res.partner.peppol_eas = 9952 is 9952 - Mã số thuế GTGT của Thổ Nhĩ Kỳ (9952 - Türkiye VAT number)
res.partner.peppol_eas = 9953 is 9953 - Mã số thuế GTGT của Holy See (Thành quốc Vatican) (9953 - Holy See (Vatican City State) VAT number)
res.partner.peppol_eas = 9957 is 9957 - Mã số thuế GTGT của Pháp (9957 - French VAT number)
res.partner.peppol_eas = 9959 is 9959 - Mã số thuế của chủ lao động (EIN, Hoa Kỳ) (9959 - Employer Identification Number (EIN, USA))
res.partner.invoice_edi_format = ubl_bis3 is BIS Billing 3.0 (BIS Billing 3.0)
res.partner.invoice_edi_format = ubl_a_nz is BIS Billing 3.0 A-NZ (BIS Billing 3.0 A-NZ)
product.template.purchase_line_warn = block is Chặn tin nhắn (Blocking Message)
res.partner.purchase_warn = block is Chặn tin nhắn (Blocking Message)
res.partner.invoice_edi_format = ubl_sg is BIS Billing 3.0 SG (BIS Billing 3.0 SG)
purchase.order.state = cancel is Đã hủy (Cancelled)
purchase.report.state = cancel is Đã hủy (Cancelled)
res.company.po_double_validation = one_step is Xác nhận đơn mua hàng bằng một bước (Confirm purchase orders in one step)
product.document.attached_on_sale = inside is PDF bên trong báo giá (Inside quote pdf)
sale.pdf.form.field.document_type = product_document is Tài liệu sản phẩm (Product Document)
res.partner.tz = America/Argentina/Cordoba is Châu Mỹ/Argentina/Cordoba (America/Argentina/Cordoba)
res.partner.tz = America/Argentina/Jujuy is Châu Mỹ/Argentina/Jujuy (America/Argentina/Jujuy)
res.partner.tz = America/Argentina/La_Rioja is Châu Mỹ/Argentina/La_Rioja (America/Argentina/La_Rioja)
res.partner.tz = America/Argentina/Mendoza is Châu Mỹ/Argentina/Mendoza (America/Argentina/Mendoza)
res.partner.tz = America/Argentina/Rio_Gallegos is Châu Mỹ/Argentina/Rio_Gallegos (America/Argentina/Rio_Gallegos)
res.partner.tz = America/Argentina/Salta is Châu Mỹ/Argentina/Salta (America/Argentina/Salta)
res.partner.invoice_edi_format = facturx is Factur-X (CII) (Factur-X (CII))
res.partner.invoice_edi_format = nlcius is NLCIUS (NLCIUS)
res.partner.invoice_edi_format = xrechnung is XRechnung CIUS (XRechnung CIUS)
res.partner.bank.proxy_type = none is Không (None)
res.partner.tz = America/Argentina/San_Juan is Châu Mỹ/Argentina/San_Juan (America/Argentina/San_Juan)
res.partner.tz = America/Argentina/San_Luis is Châu Mỹ/Argentina/San_Luis (America/Argentina/San_Luis)
res.partner.tz = America/Argentina/Tucuman is Châu Mỹ/Argentina/Tucuman (America/Argentina/Tucuman)
res.partner.tz = America/Aruba is Châu Mỹ/Aruba (America/Aruba)
mrp.bom.ready_to_produce = all_available is Khi có đủ tất cả nguyên liệu ( When all components are available)
mrp.production.search_date_category = before is Trước (Before)
mrp.production.state = done is Hoàn tất (Done)
mrp.unbuild.state = done is Hoàn tất (Done)
mrp.production.state = draft is Nháp (Draft)
mrp.production.priority = 0 is Bình thường (Normal)
mrp.routing.workcenter.worksheet_type = pdf is PDF (PDF)
stock.picking.type.done_mrp_lot_label_to_print = pdf is PDF (PDF)
stock.picking.type.generated_mrp_lot_label_to_print = pdf is PDF (PDF)
stock.picking.type.mrp_product_label_to_print = pdf is PDF (PDF)
mrp.workcenter.productivity.loss.type.loss_type = performance is Hiệu suất (Performance)
mrp.workcenter.productivity.loss.type.loss_type = productive is Hiệu quả (Productive)
mrp.production.reservation_state = assigned is Sẵn sàng (Ready)
mrp.production.search_date_category = day_1 is Ngày mai (Tomorrow)
mrp.production.priority = 1 is Khẩn cấp (Urgent)
mrp.workorder.state = waiting is Đang chờ nguyên liệu (Waiting for components)
product.product.invoice_state = draft_open_paid is Nháp, mở và đã thanh toán (Draft, Open and Paid)
product.margin.invoice_state = paid is Đã thanh toán (Paid)
stock.rule.action = buy is Mua (Buy)
purchase.order.receipt_status = full is Đã nhận hết (Fully Received)
res.partner.peppol_eas = 0184 is 0184 - DIGSTORG (0184 - DIGSTORG)
stock.picking.type.product_label_format = zpl is Nhãn ZPL (ZPL Labels)
mrp.bom.ready_to_produce = asap is Khi nguyên liệu của hoạt động đầu tiên sẵn sàng (When components for 1st operation are available)
res.partner.invoice_sending_method = snailmail is bằng đường bưu điện (by Post)
mrp.production.search_date_category = yesterday is Hôm qua (Yesterday)
product.margin.invoice_state = open_paid is Mở và đã thanh toán (Open and Paid)
account.analytic.applicability.business_domain = manufacturing_order is Lệnh sản xuất (Manufacturing Order)
sale.order.template.line.display_type = line_note is Ghi chú (Note)
sale.order.template.line.display_type = line_section is Phần (Section)
purchase.order.receipt_status = pending is Chưa nhận (Not Received)
purchase.order.receipt_status = partial is Đã nhận một phần (Partially Received)
purchase.order.line.qty_received_method = stock_moves is Điều chuyển tồn kho (Stock Moves)
sale.order.line.qty_delivered_method = analytic is Phân tích từ chi phí (Analytic From Expenses)
product.template.expense_policy = cost is Theo chi phí (At cost)
payment.provider.so_reference_type = partner is Dựa vào ID khách hàng (Based on Customer ID)
payment.provider.so_reference_type = so_name is Dựa vào mã chứng từ (Based on Document Reference)
product.template.sale_line_warn = block is Chặn tin nhắn (Blocking Message)
res.partner.sale_warn = block is Chặn tin nhắn (Blocking Message)
sale.order.state = cancel is Đã hủy (Cancelled)
sale.order.line.invoice_status = invoiced is Đã xuất hoá đơn hết (Fully Invoiced)
sale.report.invoice_status = invoiced is Đã xuất hoá đơn hết (Fully Invoiced)
sale.report.line_invoice_status = invoiced is Đã xuất hoá đơn hết (Fully Invoiced)
sale.order.discount.discount_type = so_discount is Chiết khấu toàn bộ (Global Discount)
sale.order.line.invoice_status = no is Không có hóa đơn cần xuất  (Nothing to Invoice)
sale.report.invoice_status = no is Không có hóa đơn cần xuất  (Nothing to Invoice)
sale.report.line_invoice_status = no is Không có hóa đơn cần xuất  (Nothing to Invoice)
sale.order.discount.discount_type = sol_discount is Trên tất cả dòng đơn bán hàng (On All Order Lines)
product.document.attached_on_sale = sale_order is Khi đã xác nhận đơn hàng (On confirmed order)
product.category.property_valuation = real_time is Tự động (Automated)
sale.order.picking_policy = direct is Càng sớm càng tốt (As soon as possible)
res.partner.bank.proxy_type = bank_acc is Số TK ngân hàng (Bank Account)
sale.order.delivery_status = full is Đã giao hết (Fully Delivered)
sale.order.delivery_status = pending is Chưa giao (Not Delivered)
sale.order.delivery_status = partial is Đã giao một phần (Partially Delivered)
res.config.settings.default_picking_policy = one is Giao tất cả sản phẩm một lần (Ship all products at once)
res.config.settings.default_picking_policy = direct is Giao sản phẩm ngay khi có hàng, cùng với đơn hàng chậm trễ (Ship products as soon as available, with back orders)
sale.order.delivery_status = started is Đã bắt đầu (Started)
sale.order.line.qty_delivered_method = stock_move is Điều chuyển tồn kho (Stock Moves)
product.template.invoice_policy = delivery is Số lượng đã giao (Delivered quantities)
sale.advance.payment.inv.advance_payment_method = fixed is Khoản trả trước (số tiền cố định) (Down payment (fixed amount))
sale.advance.payment.inv.advance_payment_method = percentage is Khoản trả trước (phần trăm) (Down payment (percentage))
sale.payment.provider.onboarding.wizard.payment_method = digital_signature is Chữ ký điện tử (Electronic signature)
sale.order.discount.discount_type = amount is Số tiền cố định (Fixed Amount)
sale.order.invoice_status = invoiced is Đã xuất hoá đơn hết (Fully Invoiced)
res.partner.bank.proxy_type = merchant_id is ID Người bán (Merchant ID)
res.partner.bank.proxy_type = payment_service is Dịch vụ Thanh toán (Payment Service)
account.analytic.line.category = manufacturing_order is Lệnh sản xuất (Manufacturing Order)
res.partner.tz = America/Caracas is Châu Mỹ/Caracas (America/Caracas)
res.partner.tz = America/Cayenne is Châu Mỹ/Cayenne (America/Cayenne)
res.partner.tz = America/Cayman is Châu Mỹ/Cayman (America/Cayman)
res.partner.tz = America/Chicago is Châu Mỹ/Chicago (America/Chicago)
product.category.property_cost_method = average is Giá vốn trung bình (AVCO) (Average Cost (AVCO))
product.document.attached_on_sale = quotation is Khi báo giá (On quote)
product.template.invoice_policy = order is Số lượng đã đặt (Ordered quantities)
res.company.sale_onboarding_payment_method = other is Thanh toán với nhà cung cấp dịch vụ thanh toán khác (Pay with another payment provider)
res.company.sale_onboarding_payment_method = paypal is PayPal (PayPal)
sale.order.state = draft is Báo giá (Quotation)
res.company.sale_onboarding_payment_method = stripe is Stripe (Stripe)
sale.order.invoice_status = to invoice is Cần xuất hoá đơn (To Invoice)
sale.order.line.invoice_status = to invoice is Cần xuất hoá đơn (To Invoice)
sale.report.invoice_status = to invoice is Cần xuất hoá đơn (To Invoice)
sale.report.line_invoice_status = to invoice is Cần xuất hoá đơn (To Invoice)
sale.order.invoice_status = upselling is Cơ hội bán thêm (Upselling Opportunity)
sale.order.line.invoice_status = upselling is Cơ hội bán thêm (Upselling Opportunity)
sale.report.invoice_status = upselling is Cơ hội bán thêm (Upselling Opportunity)
sale.payment.provider.onboarding.wizard.payment_method = paypal is PayPal (PayPal)
res.company.po_lock = lock is Không thể chỉnh sửa đơn mua hàng đã xác nhận. (Confirmed purchase orders are not editable)
barcode.rule.type = use_date is Sử dụng tốt nhất trước ngày (Best before Date)
barcode.rule.type = location is Vị trí (Location)
payment.refund.wizard.support_refund = full_only is Full Only (Full Only)
product.product.invoice_state = open_paid is Mở và đã thanh toán (Open and Paid)
product.product.invoice_state = paid is Đã thanh toán (Paid)
purchase.report.state = done is Hoàn tất (Done)
res.partner.tz = America/Chihuahua is Châu Mỹ/Chihuahua (America/Chihuahua)
res.partner.tz = America/Ciudad_Juarez is Châu Mỹ/Ciudad_Juarez (America/Ciudad_Juarez)
res.partner.tz = America/Coral_Harbour is Châu Mỹ/Coral_Harbour (America/Coral_Harbour)
product.document.attached_on_sale = hidden is Ẩn (Hidden)
res.config.settings.default_invoice_policy = delivery is Xuất hoá đơn theo số lượng hàng đã giao (Invoice what is delivered)
res.config.settings.default_invoice_policy = order is Xuất hoá đơn theo số lượng hàng đã đặt (Invoice what is ordered)
sale.order.line.qty_delivered_method = manual is Thủ công (Manual)
res.company.sale_onboarding_payment_method = manual is Thanh toán thủ công (Manual Payment)
product.template.service_type = manual is Thiết lập thủ công số lượng trên đơn hàng (Manually set quantities on order)
product.template.expense_policy = no is Không (No)
product.template.sale_line_warn = no-message is Không có tin nhắn (No Message)
res.partner.sale_warn = no-message is Không có tin nhắn (No Message)
sale.order.line.display_type = line_note is Ghi chú (Note)
sale.order.invoice_status = no is Không có hóa đơn cần xuất  (Nothing to Invoice)
sale.report.state = draft is Báo giá (Quotation)
sale.order.state = sent is Báo giá đã gửi (Quotation Sent)
sale.report.state = sent is Báo giá đã gửi (Quotation Sent)
sale.advance.payment.inv.advance_payment_method = delivered is Hóa đơn thường xuyên (Regular invoice)
sale.order.state = sale is Đơn bán hàng (Sales Order)
sale.report.order_reference = sale.order is Đơn bán hàng (Sales Order)
sale.report.state = sale is Đơn bán hàng (Sales Order)
product.template.expense_policy = sales_price is Giá bán (Sales price)
sale.order.line.display_type = line_section is Phần (Section)
res.company.sale_onboarding_payment_method = digital_signature is Ký online (Sign online)
sale.order.picking_policy = one is Khi tất cả sản phẩm đã sẵn sàng (When all products are ready)
purchase.report.state = draft is YCBG nháp (Draft RFQ)
purchase.order.invoice_status = invoiced is Đã thanh toán hết (Fully Billed)
res.partner.tz = America/Costa_Rica is Châu Mỹ/Costa_Rica (America/Costa_Rica)
res.partner.tz = America/Creston is Châu Mỹ/Creston (America/Creston)
res.partner.tz = America/Cuiaba is Châu Mỹ/Cuiaba (America/Cuiaba)
res.partner.tz = America/Curacao is Châu Mỹ/Curacao (America/Curacao)
res.company.po_double_validation = two_step is Phê duyệt 2 lần để xác nhận đơn mua hàng (Get 2 levels of approvals to confirm a purchase order)
account.analytic.applicability.business_domain = sale_order is Đơn bán hàng (Sale Order)
mail.notification.notification_type = email is Email (Email)
mail.notification.notification_status = ready is Sẵn sàng để Gửi (Ready to Send)
mail.notification.notification_status = process is Đang xử lý (Processing)
mail.notification.notification_status = pending is Đã gửi (Sent)
mail.notification.notification_status = sent is Đã giao (Delivered)
mail.notification.notification_status = bounce is Bị trả về (Bounced)
product.category.property_cost_method = fifo is Nhập trước xuất trước (FIFO) (First In First Out (FIFO))
quotation.document.document_type = footer is Footer (Footer)
quotation.document.document_type = header is Header (Header)
sale.pdf.form.field.document_type = quotation_document is Header/Footer (Header/Footer)
res.partner.tz = America/Lima is Châu Mỹ/Lima (America/Lima)
res.partner.tz = America/Nome is Châu Mỹ/Nome (America/Nome)
res.partner.tz = EST5EDT is EST5EDT (EST5EDT)
barcode.nomenclature.upc_ean_conv = none is Không bao giờ (Never)
res.lang.week_start = 7 is Chủ nhật (Sunday)
barcode.rule.encoding = upca is UPC-A (UPC-A)
barcode.nomenclature.upc_ean_conv = upc2ean is UPC-A sang EAN-13 (UPC-A to EAN-13)
onboarding.onboarding.step.current_step_state = done is Hoàn tất (Done)
onboarding.progress.onboarding_state = done is Hoàn tất (Done)
onboarding.progress.step.step_state = done is Hoàn tất (Done)
onboarding.onboarding.current_onboarding_state = just_done is Vừa xong (Just done)
onboarding.onboarding.step.current_step_state = just_done is Vừa xong (Just done)
onboarding.progress.onboarding_state = just_done is Vừa xong (Just done)
onboarding.progress.step.step_state = just_done is Vừa xong (Just done)
onboarding.onboarding.current_onboarding_state = not_done is Chưa xong (Not done)
onboarding.progress.onboarding_state = not_done is Chưa xong (Not done)
resource.calendar.attendance.day_period = morning is Buổi sáng (Morning)
resource.calendar.leaves.time_type = other is Khác (Other)
resource.calendar.attendance.dayofweek = 5 is Thứ Bảy (Saturday)
ir.actions.act_window.view.view_mode = hierarchy is Phân cấp bậc (Hierarchy)
ir.ui.view.type = hierarchy is Phân cấp bậc (Hierarchy)
barcode.rule.gs1_content_type = identifier is Mã dạng số (Numeric Identifier)
mail.alias.alias_contact = partners is Đối tác đã xác thực (Authenticated Partners)
mail.notification.notification_status = exception is Ngoại lệ (Exception)
mail.notification.notification_status = canceled is Đã hủy (Cancelled)
mail.notification.failure_type = unknown is Lỗi không xác định (Unknown error)
mail.notification.failure_type = mail_bounce is Trả về (Bounce)
mail.notification.failure_type = mail_from_invalid is Địa chỉ gửi từ không hợp lệ (Invalid from address)
mail.notification.failure_type = mail_from_missing is Thiếu địa chỉ gửi từ (Missing from address)
mail.activity.state = done is Hoàn tất (Done)
mail.activity.plan.template.delay_unit = days is ngày (days)
mail.mail.failure_type = mail_email_missing is Thiếu email (Missing email)
mail.mail.failure_type = mail_bl is Địa chỉ bị hạn chế (Blacklisted Address)
mail.mail.failure_type = mail_optout is Hủy tham gia (Opted Out)
mail.mail.failure_type = mail_dup is Email trùng lặp (Duplicated Email)
mail.template.template_category = base_template is Mẫu cơ sở (Base Template)
mail.template.template_category = hidden_template is Mẫu bị ẩn (Hidden Template)
mail.template.template_category = custom_template is Mẫu tuỳ chỉnh (Custom Template)
mail.ice.server.server_type = stun is stun: (stun:)
mail.ice.server.server_type = turn is turn: (turn:)
res.users.settings.channel_notifications = all is Tất cả tin nhắn (All Messages)
res.users.settings.channel_notifications = no_notif is Không có gì (Nothing)
ir.actions.act_window.view.view_mode = activity is Hoạt động (Activity)
ir.actions.server.state = next_activity is Tạo hoạt động (Create Activity)
res.config.settings.tenor_content_filter = medium is Phương tiện (Medium)
res.config.settings.tenor_content_filter = low is Thấp (Low)
account.fiscal.position.foreign_vat_header_mode = no_template is Không có mẫu (No Template)
res.partner.trust = good is Nợ tốt (Good Debtor)
res.partner.trust = normal is Nợ thông thường (Normal Debtor)
res.partner.trust = bad is Đối tượng nợ xấu (Bad Debtor)
res.partner.invoice_warn = warning is Cảnh báo (Warning)
account.account.tag.applicability = taxes is Thuế (Taxes)
account.account.tag.applicability = products is Sản phẩm (Products)
account.journal.invoice_reference_model = odoo is Odoo (Odoo)
account.journal.invoice_reference_model = euro is Châu Âu (European)
account.move.state = draft is Nháp (Draft)
account.move.state = posted is Đã vào sổ (Posted)
account.move.state = cancel is Đã hủy (Cancelled)
account.move.move_type = entry is Bút toán (Journal Entry)
account.move.move_type = out_invoice is Hóa đơn bán hàng (Customer Invoice)
sale.payment.provider.onboarding.wizard.payment_method = manual is Hướng dẫn thanh toán tùy chỉnh (Custom payment instructions)
payment.method.support_refund = full_only is Full Only (Full Only)
account.move.move_type = out_refund is Giấy báo có khách hàng (Customer Credit Note)
account.move.move_type = in_invoice is Hóa đơn mua hàng (Vendor Bill)
res.partner.tz = America/Rainy_River is Châu Mỹ/Rainy_River (America/Rainy_River)
res.partner.tz = America/Rankin_Inlet is Châu Mỹ/Rankin_Inlet (America/Rankin_Inlet)
res.partner.tz = America/Recife is Châu Mỹ/Recife (America/Recife)
res.partner.tz = America/Regina is Châu Mỹ/Regina (America/Regina)
res.partner.tz = America/Resolute is Châu Mỹ/Resolute (America/Resolute)
res.partner.tz = America/Rio_Branco is Châu Mỹ/Rio_Branco (America/Rio_Branco)
res.partner.tz = America/Santa_Isabel is Châu Mỹ/Santa_Isabel (America/Santa_Isabel)
res.partner.tz = America/Santarem is Châu Mỹ/Santarem (America/Santarem)
res.partner.tz = America/Santiago is Châu Mỹ/Santiago (America/Santiago)
account.cash.rounding.rounding_method = UP is Lên (UP)
ir.model.state = manual is Đối tượng Tùy chỉnh (Custom Object)
ir.model.state = base is Đối tượng Cơ sở (Base Object)
ir.model.fields.ttype = char is char (char)
res.partner.tz = America/Santo_Domingo is Châu Mỹ/Santo_Domingo (America/Santo_Domingo)
res.partner.tz = America/Sao_Paulo is Châu Mỹ/Sao_Paulo (America/Sao_Paulo)
res.partner.tz = America/Scoresbysund is Châu Mỹ/Scoresbysund (America/Scoresbysund)
res.partner.tz = America/Shiprock is Châu Mỹ/Shiprock (America/Shiprock)
res.partner.tz = America/Sitka is Châu Mỹ/Sitka (America/Sitka)
res.partner.tz = America/St_Barthelemy is Châu Mỹ/St_Barthelemy (America/St_Barthelemy)
res.partner.tz = America/St_Johns is Châu Mỹ/St_Johns (America/St_Johns)
res.partner.tz = America/St_Kitts is Châu Mỹ/St_Kitts (America/St_Kitts)
res.partner.tz = America/St_Lucia is Châu Mỹ/St_Lucia (America/St_Lucia)
res.partner.tz = America/St_Thomas is Châu Mỹ/St_Thomas (America/St_Thomas)
res.partner.tz = America/St_Vincent is Châu Mỹ/St_Vincent (America/St_Vincent)
res.partner.tz = America/Swift_Current is Châu Mỹ/Swift_Current (America/Swift_Current)
res.partner.tz = America/Tegucigalpa is Châu Mỹ/Tegucigalpa (America/Tegucigalpa)
res.partner.tz = America/Thule is Châu Mỹ/Thule (America/Thule)
res.partner.tz = America/Thunder_Bay is Châu Mỹ/Thunder_Bay (America/Thunder_Bay)
res.partner.tz = America/Tijuana is Châu Mỹ/Tijuana (America/Tijuana)
res.partner.tz = America/Toronto is Châu Mỹ/Toronto (America/Toronto)
res.partner.tz = America/Tortola is Châu Mỹ/Tortola (America/Tortola)
res.partner.tz = America/Vancouver is Châu Mỹ/Vancouver (America/Vancouver)
res.partner.tz = America/Virgin is Châu Mỹ/Virgin (America/Virgin)
res.partner.tz = America/Whitehorse is Châu Mỹ/Whitehorse (America/Whitehorse)
res.partner.tz = America/Winnipeg is Châu Mỹ/Winnipeg (America/Winnipeg)
res.partner.tz = America/Yakutat is Châu Mỹ/Yakutat (America/Yakutat)
res.partner.tz = America/Yellowknife is Châu Mỹ/Yellowknife (America/Yellowknife)
res.partner.tz = Antarctica/Casey is Châu Nam Cực/Casey (Antarctica/Casey)
res.partner.tz = Antarctica/Davis is Châu Nam Cực/Davis (Antarctica/Davis)
res.partner.tz = Antarctica/DumontDUrville is Châu Nam Cực/DumontDUrville (Antarctica/DumontDUrville)
res.partner.tz = Antarctica/Macquarie is Châu Nam Cực/Macquarie (Antarctica/Macquarie)
res.partner.tz = Antarctica/Mawson is Châu Nam Cực/Mawson (Antarctica/Mawson)
res.partner.tz = Antarctica/McMurdo is Châu Nam Cực/McMurdo (Antarctica/McMurdo)
res.partner.tz = Antarctica/Palmer is Châu Nam Cực/Palmer (Antarctica/Palmer)
res.partner.tz = Antarctica/Rothera is Châu Nam Cực/Rothera (Antarctica/Rothera)
res.partner.tz = Antarctica/Syowa is Châu Nam Cực/Syowa (Antarctica/Syowa)
res.partner.tz = Antarctica/Troll is Châu Nam Cực/Troll (Antarctica/Troll)
res.partner.tz = Antarctica/Vostok is Châu Nam Cực/Vostok (Antarctica/Vostok)
res.partner.tz = Arctic/Longyearbyen is Bắc Cực/Longyearbyen (Arctic/Longyearbyen)
res.partner.tz = Asia/Aden is Châu Á/Aden (Asia/Aden)
res.partner.tz = Asia/Almaty is Châu Á/Almaty (Asia/Almaty)
res.partner.tz = Asia/Amman is Châu Á/Amman (Asia/Amman)
res.partner.tz = Asia/Anadyr is Châu Á/Anadyr (Asia/Anadyr)
res.partner.tz = Asia/Aqtau is Châu Á/Aqtau (Asia/Aqtau)
res.partner.tz = Asia/Aqtobe is Châu Á/Aqtobe (Asia/Aqtobe)
res.partner.tz = Asia/Ashgabat is Châu Á/Ashgabat (Asia/Ashgabat)
res.partner.tz = Asia/Atyrau is Châu Á/Atyrau (Asia/Atyrau)
res.partner.tz = Asia/Baghdad is Châu Á/Baghdad (Asia/Baghdad)
res.partner.tz = Asia/Bahrain is Châu Á/Bahrain (Asia/Bahrain)
res.partner.tz = Asia/Baku is Châu Á/Baku (Asia/Baku)
res.partner.tz = Asia/Bangkok is Châu Á/Bangkok (Asia/Bangkok)
res.partner.tz = Asia/Barnaul is Châu Á/Barnaul (Asia/Barnaul)
res.partner.tz = Asia/Beirut is Châu Á/Beirut (Asia/Beirut)
res.partner.tz = Asia/Bishkek is Châu Á/Bishkek (Asia/Bishkek)
res.partner.tz = Asia/Brunei is Châu Á/Brunei (Asia/Brunei)
res.partner.tz = Asia/Chita is Châu Á/Chita (Asia/Chita)
res.partner.tz = Asia/Choibalsan is Châu Á/Choibalsan (Asia/Choibalsan)
res.partner.tz = Asia/Chongqing is Châu Á/Trùng_Khánh (Asia/Chongqing)
res.partner.tz = Asia/Colombo is Châu Á/Colombo (Asia/Colombo)
ir.actions.act_window_close.binding_type = action is Tác vụ (Action)
ir.actions.act_window_close.binding_type = report is Báo cáo (Report)
ir.actions.act_url.binding_type = action is Tác vụ (Action)
ir.actions.act_url.binding_type = report is Báo cáo (Report)
ir.actions.act_url.target = new is Cửa sổ mới (New Window)
res.partner.tz = Asia/Damascus is Châu Á/Damascus (Asia/Damascus)
res.partner.tz = Asia/Dhaka is Châu Á/Dhaka (Asia/Dhaka)
res.partner.tz = Asia/Dili is Châu Á/Dili (Asia/Dili)
res.partner.tz = Asia/Dubai is Châu Á/Dubai (Asia/Dubai)
res.partner.tz = Asia/Dushanbe is Châu Á/Dushanbe (Asia/Dushanbe)
res.partner.tz = Asia/Famagusta is Châu Á/Famagusta (Asia/Famagusta)
res.partner.tz = Asia/Gaza is Châu Á/Gaza (Asia/Gaza)
res.partner.tz = Asia/Harbin is Châu Á/Harbin (Asia/Harbin)
res.partner.tz = Asia/Hebron is Châu Á/Hebron (Asia/Hebron)
res.partner.tz = Asia/Ho_Chi_Minh is Châu Á/Hồ_Chí_Minh (Asia/Ho_Chi_Minh)
res.partner.tz = Asia/Hong_Kong is Châu Á/Hồng_Kông (Asia/Hong_Kong)
res.partner.tz = Asia/Hovd is Châu Á/Hovd (Asia/Hovd)
res.partner.tz = Asia/Irkutsk is Châu Á/Irkutsk (Asia/Irkutsk)
res.partner.tz = Asia/Istanbul is Châu Á/Istanbul (Asia/Istanbul)
res.partner.tz = Asia/Jakarta is Châu Á/Jakarta (Asia/Jakarta)
res.partner.tz = Asia/Jayapura is Châu Á/Jayapura (Asia/Jayapura)
res.partner.tz = Asia/Jerusalem is Châu Á/Jerusalem (Asia/Jerusalem)
res.partner.tz = Asia/Kabul is Châu Á/Kabul (Asia/Kabul)
res.partner.tz = Asia/Kamchatka is Châu Á/Kamchatka (Asia/Kamchatka)
res.partner.tz = Asia/Karachi is Châu Á/Karachi (Asia/Karachi)
res.partner.tz = Asia/Kashgar is Châu Á/Kashgar (Asia/Kashgar)
res.partner.tz = Asia/Kathmandu is Châu Á/Kathmandu (Asia/Kathmandu)
res.partner.tz = Asia/Khandyga is Châu Á/Khandyga (Asia/Khandyga)
res.partner.tz = Asia/Kolkata is Châu Á/Kolkata (Asia/Kolkata)
res.partner.tz = Asia/Krasnoyarsk is Châu Á/Krasnoyarsk (Asia/Krasnoyarsk)
res.partner.tz = Asia/Kuala_Lumpur is Châu Á/Kuala_Lumpur (Asia/Kuala_Lumpur)
res.partner.tz = Asia/Kuching is Châu Á/Kuching (Asia/Kuching)
res.partner.tz = Asia/Kuwait is Châu Á/Kuwait (Asia/Kuwait)
res.partner.tz = Asia/Macau is Châu Á/Macau (Asia/Macau)
res.partner.tz = Asia/Magadan is Châu Á/Magadan (Asia/Magadan)
res.partner.tz = Asia/Makassar is Châu Á/Makassar (Asia/Makassar)
res.partner.tz = Asia/Manila is Châu Á/Manila (Asia/Manila)
res.partner.tz = Asia/Muscat is Châu Á/Muscat (Asia/Muscat)
res.partner.tz = Asia/Nicosia is Châu Á/Nicosia (Asia/Nicosia)
res.partner.tz = Asia/Novokuznetsk is Châu Á/Novokuznetsk (Asia/Novokuznetsk)
res.partner.tz = Asia/Novosibirsk is Châu Á/Novosibirsk (Asia/Novosibirsk)
res.partner.tz = Asia/Omsk is Châu Á/Omsk (Asia/Omsk)
res.partner.tz = Asia/Oral is Châu Á/Oral (Asia/Oral)
res.partner.tz = Asia/Phnom_Penh is Châu Á/Phnom_Penh (Asia/Phnom_Penh)
res.partner.tz = Asia/Pontianak is Châu Á/Pontianak (Asia/Pontianak)
res.partner.tz = Asia/Pyongyang is Châu Á/Bình_Nhưỡng (Asia/Pyongyang)
res.partner.tz = Asia/Qatar is Châu Á/Qatar (Asia/Qatar)
res.partner.tz = Asia/Qostanay is Châu Á/Qostanay (Asia/Qostanay)
res.partner.tz = Asia/Qyzylorda is Châu Á/Qyzylorda (Asia/Qyzylorda)
res.partner.tz = Asia/Riyadh is Châu Á/Riyadh (Asia/Riyadh)
res.partner.tz = Asia/Sakhalin is Châu Á/Sakhalin (Asia/Sakhalin)
res.partner.tz = Asia/Samarkand is Châu Á/Samarkand (Asia/Samarkand)
res.partner.tz = Asia/Seoul is Châu Á/Seoul (Asia/Seoul)
res.partner.tz = Asia/Shanghai is Châu Á/Thượng_Hải (Asia/Shanghai)
res.partner.tz = Asia/Singapore is Châu Á/Singapore (Asia/Singapore)
res.partner.tz = Asia/Srednekolymsk is Châu Á/Srednekolymsk (Asia/Srednekolymsk)
res.partner.tz = Asia/Taipei is Châu Á/Đài_Bắc (Asia/Taipei)
res.partner.tz = Asia/Tashkent is Châu Á/Tashkent (Asia/Tashkent)
res.partner.tz = Asia/Tbilisi is Châu Á/Tbilisi (Asia/Tbilisi)
res.partner.tz = Asia/Tehran is Châu Á/Tehran (Asia/Tehran)
res.partner.tz = Asia/Tel_Aviv is Châu Á/Tel_Aviv (Asia/Tel_Aviv)
res.partner.tz = Asia/Thimphu is Châu Á/Thimphu (Asia/Thimphu)
res.partner.tz = Asia/Tokyo is Châu Á/Tokyo (Asia/Tokyo)
res.partner.tz = Asia/Tomsk is Châu Á/Tomsk (Asia/Tomsk)
res.partner.tz = Asia/Ulaanbaatar is Châu Á/Ulaanbaatar (Asia/Ulaanbaatar)
res.partner.tz = Asia/Urumqi is Châu Á/Urumqi (Asia/Urumqi)
res.partner.tz = Asia/Ust-Nera is Châu Á/Ust-Nera (Asia/Ust-Nera)
res.partner.tz = Asia/Vientiane is Châu Á/Viêng_Chăn (Asia/Vientiane)
res.partner.tz = Asia/Vladivostok is Châu Á/Vladivostok (Asia/Vladivostok)
res.partner.tz = Asia/Yakutsk is Châu Á/Yakutsk (Asia/Yakutsk)
res.partner.tz = Asia/Yangon is Châu Á/Yangon (Asia/Yangon)
res.partner.tz = Asia/Yekaterinburg is Châu Á/Yekaterinburg (Asia/Yekaterinburg)
res.partner.tz = Asia/Yerevan is Châu Á/Yerevan (Asia/Yerevan)
res.partner.tz = Atlantic/Azores is Đại Tây Dương/Azores (Atlantic/Azores)
res.partner.tz = Atlantic/Bermuda is Đại Tây Dương/Bermuda (Atlantic/Bermuda)
res.partner.tz = Atlantic/Canary is Đại Tây Dương/Canary (Atlantic/Canary)
res.partner.tz = Atlantic/Cape_Verde is Đại Tây Dương/Cape_Verde (Atlantic/Cape_Verde)
res.partner.tz = Atlantic/Faroe is Đại Tây Dương/Faroe (Atlantic/Faroe)
res.partner.tz = Atlantic/Jan_Mayen is Đại Tây Dương/Jan_Mayen (Atlantic/Jan_Mayen)
res.partner.tz = Atlantic/Madeira is Đại Tây Dương/Madeira (Atlantic/Madeira)
res.partner.tz = Atlantic/Reykjavik is Đại Tây Dương/Reykjavik (Atlantic/Reykjavik)
res.partner.tz = Atlantic/South_Georgia is Đại Tây Dương/South_Georgia (Atlantic/South_Georgia)
res.partner.tz = Atlantic/St_Helena is Đại Tây Dương/St_Helena (Atlantic/St_Helena)
res.partner.tz = Atlantic/Stanley is Đại Tây Dương/Stanley (Atlantic/Stanley)
res.partner.tz = Australia/Adelaide is Úc/Adelaide (Australia/Adelaide)
res.partner.tz = Australia/Brisbane is Úc/Brisbane (Australia/Brisbane)
res.partner.tz = Australia/Broken_Hill is Úc/Broken_Hill (Australia/Broken_Hill)
res.partner.tz = Australia/Canberra is Úc/Canberra (Australia/Canberra)
res.partner.tz = Australia/Currie is Úc/Currie (Australia/Currie)
res.partner.tz = Australia/Darwin is Úc/Darwin (Australia/Darwin)
res.partner.tz = Australia/Eucla is Úc/Eucla (Australia/Eucla)
res.partner.tz = Australia/Hobart is Úc/Hobart (Australia/Hobart)
res.partner.tz = Australia/Lindeman is Úc/Lindeman (Australia/Lindeman)
res.partner.tz = Australia/Lord_Howe is Úc/Lord_Howe (Australia/Lord_Howe)
res.partner.tz = Australia/Melbourne is Úc/Melbourne (Australia/Melbourne)
res.partner.tz = Australia/Perth is Úc/Perth (Australia/Perth)
res.partner.tz = Australia/Sydney is Úc/Sydney (Australia/Sydney)
res.partner.tz = Australia/Yancowinna is Úc/Yancowinna (Australia/Yancowinna)
res.partner.tz = CST6CDT is CST6CDT (CST6CDT)
res.partner.tz = EET is EET (EET)
res.partner.tz = EST is EST (EST)
res.partner.tz = Europe/Amsterdam is Châu Âu/Amsterdam (Europe/Amsterdam)
res.partner.tz = Europe/Andorra is Châu Âu/Andorra (Europe/Andorra)
res.partner.tz = Europe/Astrakhan is Châu Âu/Astrakhan (Europe/Astrakhan)
res.partner.tz = Europe/Athens is Châu Âu/Athens (Europe/Athens)
res.partner.tz = Europe/Belfast is Châu Âu/Belfast (Europe/Belfast)
res.partner.tz = Europe/Belgrade is Châu Âu/Belgrade (Europe/Belgrade)
res.partner.tz = Europe/Berlin is Châu Âu/Berlin (Europe/Berlin)
res.partner.tz = Europe/Bratislava is Châu Âu/Bratislava (Europe/Bratislava)
res.partner.tz = Europe/Brussels is Châu Âu/Brussels (Europe/Brussels)
res.partner.tz = Europe/Bucharest is Châu Âu/Bucharest (Europe/Bucharest)
res.partner.tz = Europe/Budapest is Châu Âu/Budapest (Europe/Budapest)
res.partner.tz = Europe/Busingen is Châu Âu/Busingen (Europe/Busingen)
res.partner.tz = Europe/Chisinau is Châu Âu/Chisinau (Europe/Chisinau)
res.partner.tz = Europe/Copenhagen is Châu Âu/Copenhagen (Europe/Copenhagen)
res.partner.tz = Europe/Dublin is Châu Âu/Dublin (Europe/Dublin)
res.partner.tz = Europe/Gibraltar is Châu Âu/Gibraltar (Europe/Gibraltar)
res.partner.tz = Europe/Guernsey is Châu Âu/Guernsey (Europe/Guernsey)
res.partner.tz = Europe/Helsinki is Châu Âu/Helsinki (Europe/Helsinki)
res.partner.tz = Europe/Isle_of_Man is Châu Âu/Isle_of_Man (Europe/Isle_of_Man)
res.partner.tz = Europe/Istanbul is Châu Âu/Istanbul (Europe/Istanbul)
res.partner.tz = Europe/Jersey is Châu Âu/Jersey (Europe/Jersey)
res.partner.tz = Europe/Kaliningrad is Châu Âu/Kaliningrad (Europe/Kaliningrad)
res.partner.tz = Europe/Kirov is Châu Âu/Kirov (Europe/Kirov)
res.partner.tz = Europe/Kyiv is Châu Âu/Kyiv (Europe/Kyiv)
res.partner.tz = Europe/Lisbon is Châu Âu/Lisbon (Europe/Lisbon)
res.partner.tz = Europe/Ljubljana is Châu Âu/Ljubljana (Europe/Ljubljana)
res.partner.tz = Europe/London is Châu Âu/London (Europe/London)
res.partner.tz = Europe/Luxembourg is Châu Âu/Luxembourg (Europe/Luxembourg)
res.partner.tz = Europe/Madrid is Châu Âu/Madrid (Europe/Madrid)
res.partner.tz = Europe/Malta is Châu Âu/Malta (Europe/Malta)
res.partner.tz = Europe/Mariehamn is Châu Âu/Mariehamn (Europe/Mariehamn)
res.partner.tz = Europe/Minsk is Châu Âu/Minsk (Europe/Minsk)
res.partner.tz = Europe/Monaco is Châu Âu/Monaco (Europe/Monaco)
res.partner.tz = Europe/Moscow is Châu Âu/Moscow (Europe/Moscow)
res.partner.tz = Europe/Nicosia is Châu Âu/Nicosia (Europe/Nicosia)
res.partner.tz = Europe/Oslo is Châu Âu/Oslo (Europe/Oslo)
res.partner.tz = Europe/Paris is Châu Âu/Paris (Europe/Paris)
res.partner.tz = Europe/Podgorica is Châu Âu/Podgorica (Europe/Podgorica)
res.partner.tz = Europe/Prague is Châu Âu/Prague (Europe/Prague)
res.partner.tz = Europe/Riga is Châu Âu/Riga (Europe/Riga)
res.partner.tz = Europe/Rome is Châu Âu/Rome (Europe/Rome)
res.partner.tz = Europe/Samara is Châu Âu/Samara (Europe/Samara)
res.partner.tz = Europe/San_Marino is Châu Âu/San_Marino (Europe/San_Marino)
res.partner.tz = Europe/Sarajevo is Châu Âu/Sarajevo (Europe/Sarajevo)
res.partner.tz = Europe/Saratov is Châu Âu/Saratov (Europe/Saratov)
res.partner.tz = Europe/Simferopol is Châu Âu/Simferopol (Europe/Simferopol)
res.partner.tz = Europe/Skopje is Châu Âu/Skopje (Europe/Skopje)
res.partner.tz = Europe/Sofia is Châu Âu/Sofia (Europe/Sofia)
res.partner.tz = Europe/Stockholm is Châu Âu/Stockholm (Europe/Stockholm)
res.partner.tz = Europe/Tallinn is Châu Âu/Tallinn (Europe/Tallinn)
res.partner.tz = Europe/Tirane is Châu Âu/Tirane (Europe/Tirane)
res.partner.tz = Europe/Tiraspol is Châu Âu/Tiraspol (Europe/Tiraspol)
res.partner.tz = Europe/Ulyanovsk is Châu Âu/Ulyanovsk (Europe/Ulyanovsk)
res.partner.tz = Europe/Vaduz is Châu Âu/Vaduz (Europe/Vaduz)
res.partner.tz = Europe/Vatican is Châu Âu/Vatican (Europe/Vatican)
res.partner.tz = Europe/Vienna is Châu Âu/Viên (Europe/Vienna)
res.partner.tz = Europe/Vilnius is Châu Âu/Vilnius (Europe/Vilnius)
res.partner.tz = Europe/Volgograd is Châu Âu/Volgograd (Europe/Volgograd)
res.partner.tz = Europe/Warsaw is Châu Âu/Warsaw (Europe/Warsaw)
res.partner.tz = Europe/Zagreb is Châu Âu/Zagreb (Europe/Zagreb)
res.partner.tz = Europe/Zurich is Châu Âu/Zurich (Europe/Zurich)
res.partner.tz = HST is HST (HST)
res.partner.tz = Indian/Antananarivo is Ấn Độ Dương/Antananarivo (Indian/Antananarivo)
res.partner.tz = Indian/Chagos is Ấn Độ Dương/Chagos (Indian/Chagos)
res.partner.tz = Indian/Christmas is Ấn Độ Dương/Christmas (Indian/Christmas)
res.partner.tz = Indian/Cocos is Ấn Độ Dương/Cocos (Indian/Cocos)
res.partner.tz = Indian/Comoro is Ấn Độ Dương/Comoro (Indian/Comoro)
res.partner.tz = Indian/Kerguelen is Ấn Độ Dương/Kerguelen (Indian/Kerguelen)
res.partner.tz = Indian/Mahe is Ấn Độ Dương/Mahe (Indian/Mahe)
res.partner.tz = Indian/Maldives is Ấn Độ Dương/Maldives (Indian/Maldives)
res.partner.tz = Indian/Mauritius is Ấn Độ Dương/Mauritius (Indian/Mauritius)
res.partner.tz = Indian/Mayotte is Ấn Độ Dương/Mayotte (Indian/Mayotte)
res.partner.tz = Indian/Reunion is Ấn Độ Dương/Reunion (Indian/Reunion)
res.partner.tz = MET is MET (MET)
res.partner.tz = MST is MST (MST)
res.partner.tz = MST7MDT is MST7MDT (MST7MDT)
res.partner.tz = PST8PDT is PST8PDT (PST8PDT)
res.partner.tz = Pacific/Apia is Thái Bình Dương/Apia (Pacific/Apia)
res.partner.tz = Pacific/Auckland is Thái Bình Dương/Auckland (Pacific/Auckland)
res.partner.tz = Pacific/Bougainville is Thái Bình Dương/Bougainville (Pacific/Bougainville)
res.partner.tz = Pacific/Chatham is Thái Bình Dương/Chatham (Pacific/Chatham)
res.partner.tz = Pacific/Chuuk is Thái Bình Dương/Chuuk (Pacific/Chuuk)
res.partner.tz = Pacific/Easter is Thái Bình Dương/Easter (Pacific/Easter)
res.partner.tz = Pacific/Efate is Thái Bình Dương/Efate (Pacific/Efate)
res.partner.tz = Pacific/Fakaofo is Thái Bình Dương/Fakaofo (Pacific/Fakaofo)
res.partner.tz = Pacific/Fiji is Thái Bình Dương/Fiji (Pacific/Fiji)
res.partner.tz = Pacific/Funafuti is Thái Bình Dương/Funafuti (Pacific/Funafuti)
res.partner.tz = Pacific/Galapagos is Thái Bình Dương/Galapagos (Pacific/Galapagos)
res.partner.tz = Pacific/Gambier is Thái Bình Dương/Gambier (Pacific/Gambier)
res.partner.tz = Pacific/Guadalcanal is Thái Bình Dương/Guadalcanal (Pacific/Guadalcanal)
res.partner.tz = Pacific/Guam is Thái Bình Dương/Guam (Pacific/Guam)
res.partner.tz = Pacific/Honolulu is Thái Bình Dương/Honolulu (Pacific/Honolulu)
res.partner.tz = Pacific/Johnston is Thái Bình Dương/Johnston (Pacific/Johnston)
res.partner.tz = Pacific/Kanton is Thái Bình Dương/Kanton (Pacific/Kanton)
res.partner.tz = Pacific/Kiritimati is Thái Bình Dương/Kiritimati (Pacific/Kiritimati)
res.partner.tz = Pacific/Kosrae is Thái Bình Dương/Kosrae (Pacific/Kosrae)
res.partner.tz = Pacific/Kwajalein is Thái Bình Dương/Kwajalein (Pacific/Kwajalein)
res.partner.tz = Pacific/Majuro is Thái Bình Dương/Majuro (Pacific/Majuro)
res.partner.tz = Pacific/Marquesas is Thái Bình Dương/Marquesas (Pacific/Marquesas)
res.partner.tz = Pacific/Midway is Thái Bình Dương/Midway (Pacific/Midway)
res.partner.tz = Pacific/Nauru is Thái Bình Dương/Nauru (Pacific/Nauru)
res.partner.tz = Pacific/Niue is Thái Bình Dương/Niue (Pacific/Niue)
res.partner.tz = Pacific/Norfolk is Thái Bình Dương/Norfolk (Pacific/Norfolk)
res.partner.tz = Pacific/Noumea is Thái Bình Dương/Noumea (Pacific/Noumea)
res.partner.tz = Pacific/Pago_Pago is Thái Bình Dương/Pago_Pago (Pacific/Pago_Pago)
res.partner.tz = Pacific/Palau is Thái Bình Dương/Palau (Pacific/Palau)
res.partner.tz = Pacific/Pitcairn is Thái Bình Dương/Pitcairn (Pacific/Pitcairn)
res.partner.tz = Pacific/Pohnpei is Thái Bình Dương/Pohnpei (Pacific/Pohnpei)
res.partner.tz = Pacific/Port_Moresby is Thái Bình Dương/Port_Moresby (Pacific/Port_Moresby)
res.partner.tz = Pacific/Rarotonga is Thái Bình Dương/Rarotonga (Pacific/Rarotonga)
res.partner.tz = Pacific/Saipan is Thái Bình Dương/Saipan (Pacific/Saipan)
res.partner.tz = Pacific/Samoa is Thái Bình Dương/Samoa (Pacific/Samoa)
res.partner.tz = Pacific/Tahiti is Thái Bình Dương/Tahiti (Pacific/Tahiti)
res.partner.tz = Pacific/Tarawa is Thái Bình Dương/Tarawa (Pacific/Tarawa)
res.partner.tz = Pacific/Tongatapu is Thái Bình Dương/Tongatapu (Pacific/Tongatapu)
res.partner.tz = Pacific/Wake is Thái Bình Dương/Wake (Pacific/Wake)
res.partner.tz = Pacific/Wallis is Thái Bình Dương/Wallis (Pacific/Wallis)
res.partner.tz = Pacific/Yap is Thái Bình Dương/Yap (Pacific/Yap)
res.partner.tz = UTC is UTC (UTC)
res.partner.tz = WET is WET (WET)
res.partner.tz = Etc/GMT is Etc/GMT (Etc/GMT)
res.partner.tz = Etc/GMT+0 is Etc/GMT+0 (Etc/GMT+0)
res.partner.tz = Etc/GMT+1 is Etc/GMT+1 (Etc/GMT+1)
res.partner.tz = Etc/GMT+10 is Etc/GMT+10 (Etc/GMT+10)
res.partner.tz = Etc/GMT+11 is Etc/GMT+11 (Etc/GMT+11)
res.partner.tz = Etc/GMT+12 is Etc/GMT+12 (Etc/GMT+12)
res.partner.tz = Etc/GMT+2 is Etc/GMT+2 (Etc/GMT+2)
res.partner.tz = Etc/GMT+3 is Etc/GMT+3 (Etc/GMT+3)
res.partner.tz = Etc/GMT+4 is Etc/GMT+4 (Etc/GMT+4)
res.partner.tz = Etc/GMT+5 is Etc/GMT+5 (Etc/GMT+5)
res.partner.tz = Etc/GMT+6 is Etc/GMT+6 (Etc/GMT+6)
res.partner.tz = Etc/GMT+7 is Etc/GMT+7 (Etc/GMT+7)
res.partner.tz = Etc/GMT+8 is Etc/GMT+8 (Etc/GMT+8)
res.partner.tz = Etc/GMT+9 is Etc/GMT+9 (Etc/GMT+9)
res.partner.tz = Etc/GMT-0 is Etc/GMT-0 (Etc/GMT-0)
res.partner.tz = Etc/GMT-1 is Etc/GMT-1 (Etc/GMT-1)
res.partner.tz = Etc/GMT-10 is Etc/GMT-10 (Etc/GMT-10)
res.partner.tz = Etc/GMT-11 is Etc/GMT-11 (Etc/GMT-11)
res.partner.tz = Etc/GMT-12 is Etc/GMT-12 (Etc/GMT-12)
res.partner.tz = Etc/GMT-13 is Etc/GMT-13 (Etc/GMT-13)
res.partner.tz = Etc/GMT-14 is Etc/GMT-14 (Etc/GMT-14)
res.partner.tz = Etc/GMT-2 is Etc/GMT-2 (Etc/GMT-2)
res.partner.tz = Etc/GMT-3 is Etc/GMT-3 (Etc/GMT-3)
res.partner.tz = Etc/GMT-4 is Etc/GMT-4 (Etc/GMT-4)
res.partner.tz = Etc/GMT-5 is Etc/GMT-5 (Etc/GMT-5)
res.partner.tz = Etc/GMT-6 is Etc/GMT-6 (Etc/GMT-6)
res.partner.tz = Etc/GMT-7 is Etc/GMT-7 (Etc/GMT-7)
res.partner.tz = Etc/GMT-8 is Etc/GMT-8 (Etc/GMT-8)
res.partner.tz = Etc/GMT-9 is Etc/GMT-9 (Etc/GMT-9)
res.partner.tz = Etc/GMT0 is Etc/GMT0 (Etc/GMT0)
res.partner.tz = Etc/Greenwich is Etc/Greenwich (Etc/Greenwich)
res.partner.tz = Etc/UCT is Etc/UCT (Etc/UCT)
res.partner.tz = Etc/UTC is Etc/UTC (Etc/UTC)
res.partner.tz = Etc/Universal is Etc/Quốc tế (Etc/Universal)
res.partner.tz = Etc/Zulu is Etc/Zulu (Etc/Zulu)
res.partner.type = contact is Liên hệ (Contact)
res.partner.type = invoice is Địa chỉ xuất hoá đơn (Invoice Address)
res.partner.type = delivery is Địa chỉ giao hàng (Delivery Address)
res.partner.type = other is Địa chỉ khác (Other Address)
res.partner.company_type = person is Cá nhân (Individual)
res.partner.company_type = company is Công ty (Company)
res.currency.position = after is Sau Tổng tiền (After Amount)
res.currency.position = before is Trước Số tiền (Before Amount)
res.company.font = Lato is Lato (Lato)
res.company.font = Roboto is Roboto (Roboto)
res.company.font = Open_Sans is Open Sans (Open Sans)
res.company.font = Montserrat is Montserrat (Montserrat)
res.company.font = Oswald is Oswald (Oswald)
res.company.font = Raleway is Raleway (Raleway)
res.company.font = Tajawal is Tajawal (Tajawal)
res.company.font = Fira_Mono is Fira Mono (Fira Mono)
res.company.layout_background = Blank is Trống (Blank)
res.company.layout_background = Demo logo is Logo demo (Demo logo)
res.company.layout_background = Custom is Tùy chỉnh (Custom)
res.users.identitycheck.auth_method = password is Mật khẩu (Password)
res.users.deletion.state = todo is Cần làm (To Do)
res.users.deletion.state = done is Hoàn tất (Done)
res.users.deletion.state = fail is Không đạt (Failed)
res.device.log.device_type = computer is Máy tính (Computer)
res.device.log.device_type = mobile is Di động (Mobile)
res.device.device_type = computer is Máy tính (Computer)
res.device.device_type = mobile is Di động (Mobile)
base.module.update.state = init is init (init)
base.module.update.state = done is hoàn thành (done)
base.language.export.format = csv is Tập tin CSV (CSV File)
base.language.export.format = po is Tập tin PO (PO File)
base.language.export.format = tgz is Lưu trữ TGZ (TGZ Archive)
base.language.export.export_type = module is Phân hệ (Module)
base.language.export.export_type = model is Mô hình (Model)
base.language.export.state = choose is chọn (choose)
base.language.export.state = get is lấy (get)
base.partner.merge.automatic.wizard.state = option is Tùy chọn (Option)
base.partner.merge.automatic.wizard.state = selection is Lựa chọn (Selection)
base.partner.merge.automatic.wizard.state = finished is Đã hoàn thành (Finished)
res.partner.tz = Africa/Asmera is Châu Phi/Asmera (Africa/Asmera)
res.partner.tz = America/Argentina/ComodRivadavia is Châu Mỹ/Argentina/ComodRivadavia (America/Argentina/ComodRivadavia)
res.partner.tz = America/Buenos_Aires is Châu Mỹ/Buenos_Aires (America/Buenos_Aires)
res.partner.tz = America/Catamarca is Châu Mỹ/Catamarca (America/Catamarca)
res.partner.tz = America/Cordoba is Châu Mỹ/Cordoba (America/Cordoba)
res.partner.tz = America/Fort_Wayne is Châu Mỹ/Fort_Wayne (America/Fort_Wayne)
res.partner.tz = America/Godthab is Châu Mỹ/Godthab (America/Godthab)
res.partner.tz = America/Indianapolis is Châu Mỹ/Indianapolis (America/Indianapolis)
res.partner.tz = America/Jujuy is Châu Mỹ/Jujuy (America/Jujuy)
res.partner.tz = America/Knox_IN is Châu Mỹ/Knox_IN (America/Knox_IN)
res.partner.tz = America/Louisville is Châu Mỹ/Louisville (America/Louisville)
res.partner.tz = America/Mendoza is Châu Mỹ/Mendoza (America/Mendoza)
res.partner.tz = America/Rosario is Châu Mỹ/Rosario (America/Rosario)
res.partner.tz = Antarctica/South_Pole is Châu Nam Cực/South_Pole (Antarctica/South_Pole)
res.partner.tz = Asia/Ashkhabad is Châu Á/Ashkhabad (Asia/Ashkhabad)
res.partner.tz = Asia/Calcutta is Châu Á/Calcutta (Asia/Calcutta)
res.partner.tz = Asia/Chungking is Châu Á/Trùng_Khánh (Asia/Chungking)
res.partner.tz = Asia/Dacca is Châu Á/Dacca (Asia/Dacca)
res.partner.tz = Asia/Katmandu is Châu Á/Katmandu (Asia/Katmandu)
res.partner.tz = Asia/Macao is Châu Á/Macao (Asia/Macao)
res.partner.tz = Asia/Rangoon is Châu Á/Rangoon (Asia/Rangoon)
res.partner.tz = Asia/Saigon is Châu Á/Sài_Gòn (Asia/Saigon)
res.partner.tz = Asia/Thimbu is Châu Á/Thimbu (Asia/Thimbu)
res.partner.tz = Asia/Ujung_Pandang is Châu Á/Ujung_Pandang (Asia/Ujung_Pandang)
res.partner.tz = Asia/Ulan_Bator is Châu Á/Ulan_Bator (Asia/Ulan_Bator)
res.partner.tz = Atlantic/Faeroe is Đại Tây Dương/Faeroe (Atlantic/Faeroe)
res.partner.tz = Australia/ACT is Úc/ACT (Australia/ACT)
res.partner.tz = Australia/LHI is Úc/LHI (Australia/LHI)
res.partner.tz = Australia/NSW is Úc/NSW (Australia/NSW)
res.partner.tz = Australia/North is Úc/Miền Bắc (Australia/North)
res.partner.tz = Australia/Queensland is Úc/Queensland (Australia/Queensland)
res.partner.tz = Australia/South is Úc/Miền Nam (Australia/South)
res.partner.tz = Australia/Tasmania is Úc/Tasmania (Australia/Tasmania)
res.partner.tz = Australia/Victoria is Úc/Victoria (Australia/Victoria)
res.partner.tz = Australia/West is Úc/Miền Tây (Australia/West)
res.partner.tz = Brazil/Acre is Brazil/Acre (Brazil/Acre)
res.partner.tz = Brazil/DeNoronha is Brazil/DeNoronha (Brazil/DeNoronha)
res.partner.tz = Brazil/East is Brazil/Miền Đông (Brazil/East)
res.partner.tz = Brazil/West is Brazil/Miền Tây (Brazil/West)
res.partner.tz = Canada/Atlantic is Canada/Đại Tây Dương (Canada/Atlantic)
res.partner.tz = Canada/Central is Canada/Miền Trung (Canada/Central)
res.partner.tz = Canada/Eastern is Canada/Miền Đông (Canada/Eastern)
res.partner.tz = Canada/Mountain is Canada/Miền núi (Canada/Mountain)
res.partner.tz = Canada/Newfoundland is Canada/Newfoundland (Canada/Newfoundland)
res.partner.tz = Canada/Pacific is Canada/Thái Bình Dương (Canada/Pacific)
res.partner.tz = Canada/Saskatchewan is Canada/Saskatchewan (Canada/Saskatchewan)
res.partner.tz = Canada/Yukon is Canada/Yukon (Canada/Yukon)
res.partner.tz = Chile/Continental is Chile/Đất liền (Chile/Continental)
res.partner.tz = Chile/EasterIsland is Chile/Đảo_Phục_Sinh (Chile/EasterIsland)
res.partner.tz = Cuba is Cuba (Cuba)
res.partner.tz = Egypt is Ai Cập (Egypt)
res.partner.tz = Eire is Eire (Eire)
res.partner.tz = Europe/Kiev is Châu Âu/Kiev (Europe/Kiev)
res.partner.tz = Europe/Uzhgorod is Châu Âu/Uzhgorod (Europe/Uzhgorod)
res.partner.tz = Europe/Zaporozhye is Châu Âu/Zaporozhye (Europe/Zaporozhye)
res.partner.tz = GB is GB (GB)
res.partner.tz = GB-Eire is GB-Eire (GB-Eire)
res.partner.tz = GMT+0 is GMT+0 (GMT+0)
res.partner.tz = GMT-0 is GMT-0 (GMT-0)
res.partner.tz = GMT0 is GMT0 (GMT0)
res.partner.tz = Greenwich is Greenwich (Greenwich)
res.partner.tz = Hongkong is Hongkong (Hongkong)
res.partner.tz = Iceland is Iceland (Iceland)
res.partner.tz = Iran is Iran (Iran)
res.partner.tz = Israel is Israel (Israel)
res.partner.tz = Jamaica is Jamaica (Jamaica)
res.partner.tz = Japan is Nhật Bản (Japan)
res.partner.tz = Kwajalein is Kwajalein (Kwajalein)
res.partner.tz = Libya is Libya (Libya)
res.partner.tz = Mexico/BajaNorte is Mexico/BajaNorte (Mexico/BajaNorte)
res.partner.tz = Mexico/BajaSur is Mexico/BajaSur (Mexico/BajaSur)
res.partner.tz = Mexico/General is Mexico/General (Mexico/General)
res.partner.tz = NZ is NZ (NZ)
res.partner.tz = NZ-CHAT is NZ-CHAT (NZ-CHAT)
res.partner.tz = Navajo is Navajo (Navajo)
res.partner.tz = PRC is PRC (PRC)
res.partner.tz = Pacific/Enderbury is Thái Bình Dương/Enderbury (Pacific/Enderbury)
res.partner.tz = Pacific/Ponape is Thái Bình Dương/Ponape (Pacific/Ponape)
res.partner.tz = Pacific/Truk is Thái Bình Dương/Truk (Pacific/Truk)
res.partner.tz = Poland is Ba Lan (Poland)
res.partner.tz = Portugal is Bồ Đào Nha (Portugal)
res.partner.tz = ROC is ROC (ROC)
res.partner.tz = ROK is ROK (ROK)
res.partner.tz = Singapore is Singapore (Singapore)
res.partner.tz = Turkey is Thổ Nhĩ Kỳ (Turkey)
res.partner.tz = UCT is UCT (UCT)
res.partner.tz = US/Alaska is Hoa Kỳ/Alaska (US/Alaska)
res.partner.tz = US/Aleutian is Hoa Kỳ/Aleutian (US/Aleutian)
res.partner.tz = US/Arizona is Hoa Kỳ/Arizona (US/Arizona)
res.partner.tz = US/Central is Hoa Kỳ/Miền Trung (US/Central)
res.partner.tz = US/East-Indiana is Hoa Kỳ/East-Indiana (US/East-Indiana)
res.partner.tz = US/Eastern is Hoa Kỳ/Miền Đông (US/Eastern)
res.partner.tz = US/Hawaii is Hoa Kỳ/Hawaii (US/Hawaii)
res.partner.tz = US/Indiana-Starke is Hoa Kỳ/Indiana-Starke (US/Indiana-Starke)
res.partner.tz = US/Michigan is Hoa Kỳ/Michigan (US/Michigan)
uom.uom.uom_type = smaller is Nhỏ hơn đơn vị gốc của nhóm này (Smaller than the reference Unit of Measure)
barcode.nomenclature.upc_ean_conv = ean2upc is EAN-13 sang UPC-A (EAN-13 to UPC-A)
mail.activity.mixin.activity_exception_decoration = warning is Cảnh báo (Alert)
mail.activity.mixin.activity_exception_decoration = danger is Lỗi (Error)
fetchmail.server.state = draft is Chưa được xác nhận (Not Confirmed)
fetchmail.server.state = done is Đã xác nhận (Confirmed)
fetchmail.server.server_type = imap is Địa chỉ máy chủ IMAP (IMAP Server)
mail.notification.failure_type = mail_email_invalid is Địa chỉ email không hợp lệ (Invalid email address)
mail.notification.failure_type = mail_smtp is Kết nối thất bại (vấn đề máy chủ gửi email) (Connection failed (outgoing mail server problem))
mail.activity.type.category = phonecall is Cuộc gọi điện thoại (Phonecall)
mail.activity.plan.template.delay_unit = weeks is tuần (weeks)
mail.message.message_type = notification is Thông báo của hệ thống (System notification)
mail.message.message_type = auto_comment is Thông báo được nhắm mục tiêu tự động (Automated Targeted Notification)
mail.message.message_type = user_notification is Thông báo cụ thể cho người dùng (User Specific Notification)
mail.mail.state = received is Đã nhận (Received)
mail.mail.state = exception is Giao thư đã lỗi (Delivery Failed)
ir.actions.server.activity_date_deadline_range_type = weeks is Tuần (Weeks)
ir.ui.view.type = activity is Hoạt động (Activity)
res.users.notification_type = inbox is Xử lý trong hệ thống (Handle in Odoo)
discuss.channel.member.fold_state = open is Mở (Open)
discuss.channel.member.fold_state = folded is Thu gọn (Folded)
discuss.channel.member.fold_state = closed is Đã chốt (Closed)
discuss.channel.member.custom_notifications = all is Tất cả tin nhắn (All Messages)
discuss.channel.member.custom_notifications = mentions is Chỉ đề cập (Mentions Only)
discuss.channel.member.custom_notifications = no_notif is Không có gì (Nothing)
discuss.channel.channel_type = chat is Nhắn tin (Chat)
discuss.channel.channel_type = channel is Kênh (Channel)
discuss.channel.channel_type = group is Nhóm (Group)
mail.compose.message.message_type = comment is Bình luận (Comment)
mail.compose.message.message_type = notification is Thông báo của hệ thống (System notification)
mail.compose.message.reply_to_mode = update is Lưu trữ email và email trả lời trong cửa sổ trò chuyện của từng bản ghi (Store email and replies in the chatter of each record)
mail.compose.message.reply_to_mode = new is Thu thập thư trả lời trên một địa chỉ email cụ thể (Collect replies on a specific email address)
account.analytic.line.category = other is Khác (Other)
account.analytic.applicability.applicability = unavailable is Không khả dụng (Unavailable)
product.template.type = combo is Combo (Combo)
res.config.settings.product_volume_volume_in_cubic_feet = 0 is Mét khối (Cubic Meters)
product.label.layout.print_format = dymo is Dymo (Dymo)
rating.rating.rating_text = ok is Bình thường (Okay)
sms.sms.state = error is Lỗi (Error)
mail.notification.failure_type = sms_expired is Đã hết hạn (Expired)
mail.notification.failure_type = sms_invalid_destination is Địa chỉ đích không hợp lệ (Invalid Destination)
sms.sms.failure_type = sms_optout is Hủy tham gia (Opted Out)
sms.composer.composition_mode = comment is Đưa vào tài liệu (Post on a document)
payment.provider.onboarding.wizard.payment_method = stripe is Thẻ tín dụng và ghi nợ (qua Stripe) (Credit & Debit card (via Stripe))
payment.provider.onboarding.wizard.payment_method = manual is Hướng dẫn thanh toán tùy chỉnh (Custom payment instructions)
payment.transaction.state = pending is Đang chờ (Pending)
res.company.payment_onboarding_payment_method = stripe is Stripe (Stripe)
payment.transaction.operation = validation is Xác thực phương thức thanh toán (Validation of the payment method)
res.partner.tz = CET is CET (CET)
res.partner.tz = GMT is GMT (GMT)
barcode.rule.type = location_dest is Vị trí đích (Destination location)
barcode.rule.type = expiration_date is Ngày hết hạn (Expiration Date)
mail.alias.alias_status = not_tested is Chưa được kiểm thử (Not Tested)
mail.activity.type.category = upload_file is Tải lên (Upload Document)
mail.activity.state = planned is Đã lên kế hoạch (Planned)
mail.activity.plan.template.delay_unit = months is tháng (months)
mail.activity.plan.template.delay_from = before_plan_date is Trước ngày kế hoạch (Before Plan Date)
mail.activity.plan.template.responsible_type = on_demand is Hỏi khi triển khai (Ask at launch)
mail.activity.plan.template.responsible_type = other is Người dùng mặc định (Default user)
barcode.rule.type = package_type is Kiểu đóng gói (Package Type)
barcode.rule.type = lot is Lô (Lot)
barcode.rule.type = package is Kiện hàng (Package)
res.partner.tz = US/Mountain is Hoa Kỳ/Miền núi (US/Mountain)
res.partner.tz = US/Pacific is Hoa Kỳ/Thái Bình Dương (US/Pacific)
res.partner.tz = US/Samoa is Hoa Kỳ/Samoa (US/Samoa)
res.partner.tz = Universal is Quốc tế (Universal)
res.partner.tz = W-SU is W-SU (W-SU)
res.partner.tz = Zulu is Zulu (Zulu)
barcode.rule.type = product is Đơn vị sản phẩm (Unit Product)
mail.message.message_type = email is Email đến (Incoming Email)
mail.message.message_type = comment is Bình luận (Comment)
mail.message.message_type = email_outgoing is Email đi (Outgoing Email)
mail.mail.state = outgoing is Xuất đi (Outgoing)
mail.mail.state = cancel is Đã hủy (Cancelled)
mail.mail.failure_type = unknown is Lỗi không xác định (Unknown error)
ir.actions.server.state = object_create is Tạo bản ghi (Create Record)
ir.actions.server.state = code is Thực thi mã (Execute Code)
ir.actions.server.state = webhook is Gửi thông báo webhook (Send Webhook Notification)
ir.actions.server.state = multi is Thực thi tác vụ hiện có (Execute Existing Actions)
