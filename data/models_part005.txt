        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.automatic.entry.wizard is <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> toán tự động (Create Automatic Entries)|(account.automatic.entry.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.template is Mẫu email (Email Templates)|(mail.template)|Templates for sending email
sale.order.warning.wizard is SaleOrderWarningWizard|(sale.order.warning.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
barcode.rule is Quy tắc mã vạch (Barcode Rule)|(barcode.rule)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auth_totp.wizard is <PERSON><PERSON><PERSON> cụ cài đặt 2 yếu tố (2-Factor Setup Wizard)|(auth_totp.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.fiscal.position is Vị trí tài chính (Fiscal Position)|(account.fiscal.position)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.rule is Auditlog - Rule|(auditlog.rule)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.http.session is Auditlog - HTTP User session log|(auditlog.http.session)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.full.reconcile is Đối chiếu toàn bộ (Full Reconcile)|(account.full.reconcile)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.http.request is Auditlog - HTTP request log|(auditlog.http.request)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.log is Auditlog - Log|(auditlog.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.log.line is Auditlog - Log details (fields updated)|(auditlog.log.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.log.line.view is Auditlog - Log details (fields updated)|(auditlog.log.line.view)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
auditlog.autovacuum is Auditlog - Delete old logs|(auditlog.autovacuum)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
payment.transaction is Giao dịch thanh toán (Payment Transaction)|(payment.transaction)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.message is Tin nhắn (Message)|(mail.message)| Override MailMessage class in order to add a new type: SMS messages.
    Those messages comes with their own notification method, using SMS
    gateway. 
res.company is Công ty (Companies)|(res.company)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.financial.year.op is Số dư đầu kỳ của năm tài chính (Opening Balance of Financial Year)|(account.financial.year.op)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.activity is Hoạt động (Activity)|(mail.activity)| An actual activity to perform. Activities are linked to
    documents using res_id and res_model_id fields. Activities have a deadline
    that can be used in kanban view to display a status. Once done activities
    are unlinked and a message is posted. This message has a new activity_type_id
    field that indicates the activity linked to the message. 
account.partial.reconcile is Đối chiếu một phần (Partial Reconcile)|(account.partial.reconcile)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.reconcile.model.partner.mapping is Mapping đối tác cho mẫu đối chiếu (Partner mapping for reconciliation models)|(account.reconcile.model.partner.mapping)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.payment.term is Điều khoản thanh toán (Payment Terms)|(account.payment.term)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.order.state.timeline is Ecommerce Order State Timeline|(ecommerce.order.state.timeline)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.resequence.wizard is Tạo lại số thứ tự bút toán. (Remake the sequence of Journal Entries.)|(account.resequence.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.reconcile.model.line is Quy tắc dành cho mẫu đối chiếu (Rules for the reconciliation model)|(account.reconcile.model.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.guest is Khách (Guest)|(mail.guest)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.tax.group is Nhóm thuế (Tax Group)|(account.tax.group)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.ice.server is Máy chủ ICE (ICE server)|(mail.ice.server)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.wizard.invite is Đồ thuật Mời (Invite wizard)|(mail.wizard.invite)| Wizard to invite partners (or channels) and make them followers. 
mail.gateway.allowed is Cổng email được cho phép (Mail Gateway Allowed)|(mail.gateway.allowed)|List of trusted email address which won't have the quota restriction.

    The incoming emails have a restriction of the number of records they can
    create with alias, defined by the 2 systems parameters;
    - mail.gateway.loop.minutes
    - mail.gateway.loop.threshold

    But we might have some legit use cases for which we want to receive a ton of emails
    from an automated-source. This model stores those trusted source and this restriction
    won't apply to them.
    
validate.account.move is Xác thực bút toán tài khoản (Validate Account Move)|(validate.account.move)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
uom.uom is Đơn vị tính sản phẩm (Product Unit of Measure)|(uom.uom)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.page.properties.base is Page Properties Base|(website.page.properties.base)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
website.cover_properties.mixin is Bìa Thuộc tính Trang web Mixin (Cover Properties Website Mixin)|(website.cover_properties.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mail.push.device is Thiết bị nhận thông báo đẩy (Push Notification Device)|(mail.push.device)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.push is Thông báo đẩy (Push Notifications)|(mail.push)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.blacklist.remove is Công cụ xoá email khỏi danh sách hạn chế (Remove email from blacklist wizard)|(mail.blacklist.remove)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.message.schedule is Tin nhắn đã lên lịch (Scheduled Messages)|(mail.message.schedule)| Mail message notification schedule queue.

    This model is used to store the mail messages scheduled. So we can
    delay the sending of the notifications. A scheduled date field already
    exists on the <mail.mail> but it does not allow us to delay the sending
    of the <bus.bus> notifications.
    
mail.link.preview is Lưu dữ liệu xem trước liên kết (Store link preview data)|(mail.link.preview)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.analytic.distribution.model is Mô hình phân bổ phân tích (Analytic Distribution Model)|(account.analytic.distribution.model)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.controller.page is Trang mô hình (Model Page)|(website.controller.page)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.visitor is Khách truy cập trang web (Website Visitor)|(website.visitor)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.analytic.plan is Kế hoạch phân tích (Analytic Plans)|(account.analytic.plan)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.analytic.applicability is Các ứng dụng của kế hoạch phân tích (Analytic Plan's Applicabilities)|(account.analytic.applicability)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
