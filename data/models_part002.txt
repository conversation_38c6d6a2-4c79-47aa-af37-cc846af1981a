    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
sale.loyalty.reward.wizard is <PERSON><PERSON><PERSON><PERSON> hàng thân thiết bán hàng - <PERSON>ông cụ chọn phần thưởng (Sale Loyalty - Reward Selection Wizard)|(sale.loyalty.reward.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sale.order.coupon.points is <PERSON><PERSON><PERSON><PERSON> phiếu giảm giá đơn bán hàng - <PERSON> dõi cách đơn bán hàng tác động đến phiếu giảm giá (Sale Order Coupon Points - Keeps track of how a sale order impacts a coupon)|(sale.order.coupon.points)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
followup.print is In theo dõi & Gửi mail cho khách hàng (Print Follow-up & Send Mail to Customers)|(followup.print)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
website.page.properties is Thuộc tính trang (Page Properties)|(website.page.properties)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
analytic.mixin is Mixin phân tích (Analytic Mixin)|(analytic.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
recurring.payment.line is Dòng thanh toán định kỳ (Recurring Payment Line)|(recurring.payment.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
recurring.payment is Thanh toán định kỳ( (Recurring Payment()|(recurring.payment)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
change.lock.date is Thay đổi ngày khóa sổ (Change Lock Date)|(change.lock.date)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.recurring.template is Mẫu định kỳ (Recurring Template)|(account.recurring.template)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.tax.repartition.line is Dòng phân bổ thuế (Tax Repartition Line)|(account.tax.repartition.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
followup.sending.results is Kết quả từ việc gửi các thư và email khác nhau (Results from the sending of the different letters and emails)|(followup.sending.results)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
asset.depreciation.confirmation.wizard is asset.depreciation.confirmation.wizard (asset.depreciation.confirmation.wizard)|(asset.depreciation.confirmation.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.aged.trial.balance is Báo cáo tuổi nợ (Account Aged Trial balance Report)|(account.aged.trial.balance)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.common.account.report is Báo cáo chung tài khoản (Account Common Account Report)|(account.common.account.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
website.seo.metadata is Siêu dữ liệu SEO (SEO metadata)|(website.seo.metadata)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
hr.department is Phòng ban (Department)|(hr.department)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
theme.ir.asset is Asset chủ đề (Theme Asset)|(theme.ir.asset)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.partner.autocomplete.sync is Đồng bộ tự động hoàn thành đối tác (Partner Autocomplete Sync)|(res.partner.autocomplete.sync)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.common.partner.report is Báo cáo chung đối tác (Account Common Partner Report)|(account.common.partner.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
fetchmail.server is Máy chủ nhận thư (Incoming Mail Server)|(fetchmail.server)|Incoming POP/IMAP mail server account
mail.bot is Mail Bot (Mail Bot)|(mail.bot)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.analytic.account is Tài khoản phân tích (Analytic Account)|(account.analytic.account)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.common.report is Báo cáo chung (Account Common Report)|(account.common.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.report.partner.ledger is Sổ chi tiết công nợ (Account Partner Ledger)|(account.report.partner.ledger)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
coupon.share is Tạo đường link áp dụng phiếu giảm giá và chuyển hướng tới trang cụ thể (Create links that apply a coupon and redirect to a specific page)|(coupon.share)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
lot.label.layout is Chọn bố cục trang tính để in nhãn lô (Choose the sheet layout to print lot labels)|(lot.label.layout)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.product.report_pricelist is Báo cáo bảng giá (Pricelist Report)|(report.product.report_pricelist)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
iap.account is Tài khoản IAP (IAP Account)|(iap.account)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.print.journal is In sổ nhật ký (Account Print Journal)|(account.print.journal)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sms.sms is SMS đi (Outgoing SMS)|(sms.sms)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sms.resend.recipient is Gửi lại thông báo (Resend Notification)|(sms.resend.recipient)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
accounting.report is Báo cáo kế toán (Accounting Report)|(accounting.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.common.journal.report is Báo cáo chung sổ nhật ký (Common Journal Report)|(account.common.journal.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.accounting_pdf_reports.report_financial is Báo cáo tài chính (Financial Reports)|(report.accounting_pdf_reports.report_financial)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.report.general.ledger is Báo cáo sổ cái (General Ledger Report)|(account.report.general.ledger)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.accounting_pdf_reports.report_general_ledger is Báo cáo sổ cái (General Ledger Report)|(report.accounting_pdf_reports.report_general_ledger)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
picking.label.type is Chọn in nhãn sản phẩm hay số lô/sê-ri (Choose whether to print product or lot/sn labels)|(picking.label.type)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
digest.digest is Tóm tắt (Digest)|(digest.digest)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.fiscal.year is Năm tài chính (Fiscal Year)|(account.fiscal.year)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.accounting_pdf_reports.report_journal is Báo cáo kiểm toán sổ nhật ký (Journal Audit Report)|(report.accounting_pdf_reports.report_journal)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.tax.report.wizard is Báo cáo thuế (Tax Report)|(account.tax.report.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.accounting_pdf_reports.report_tax is Báo cáo thuế (Tax Report)|(report.accounting_pdf_reports.report_tax)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.balance.report is Báo cáo bảng cân đối thử (Trial Balance Report)|(account.balance.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sms.template.preview is Xem trước mẫu SMS (SMS Template Preview)|(sms.template.preview)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.inventory.conflict is Không nhất quán trong Tồn kho (Conflict in Inventory)|(stock.inventory.conflict)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.accounting_pdf_reports.report_trialbalance is Báo cáo bảng cân đối thử (Trial Balance Report)|(report.accounting_pdf_reports.report_trialbalance)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
queue.job is Queue Job|(queue.job)|Model storing the jobs to be executed.
queue.job.channel is Job Channels|(queue.job.channel)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
queue.job.function is Job Functions|(queue.job.function)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
queue.requeue.job is Wizard to requeue a selection of jobs|(queue.requeue.job)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
queue.jobs.to.done is Set all selected jobs to done|(queue.jobs.to.done)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
queue.jobs.to.cancelled is Cancel all selected jobs|(queue.jobs.to.cancelled)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
bus.listener.mixin is Có thể gửi tin nhắn qua bus.bus (Can send messages via bus.bus)|(bus.listener.mixin)|Allow sending messages related to the current model via as a bus.bus channel.

    The model needs to be allowed as a valid channel for the bus in `_build_bus_channel_list`.
    
report.om_account_followup.report_followup is Báo cáo theo dõi (Report Followup)|(report.om_account_followup.report_followup)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
report.accounting_pdf_reports.report_agedpartnerbalance is Báo cáo tuổi nợ đối tác (Aged Partner Balance Report)|(report.accounting_pdf_reports.report_agedpartnerbalance)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
rating.rating is Đánh giá (Rating)|(rating.rating)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
portal.mixin is Mixin cổng thông tin (Portal Mixin)|(portal.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
portal.wizard is Cấp quyền truy cập cổng thông tin (Grant Portal Access)|(portal.wizard)|
        A wizard to manage the creation/removal of portal users.
    
lunch.product is Sản phẩm ăn trưa (Lunch Product)|(lunch.product)| Products available to order. A product is linked to a specific vendor. 
payment.provider.onboarding.wizard is Công cụ onboarding về nhà cung cấp dịch vụ thanh toán (Payment provider onboarding wizard)|(payment.provider.onboarding.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.om_account_daily_reports.report_cashbook is Cash Book|(report.om_account_daily_reports.report_cashbook)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
report.om_account_daily_reports.report_bankbook is Bank Book|(report.om_account_daily_reports.report_bankbook)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
utm.medium is Phương tiện UTM (UTM Medium)|(utm.medium)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.order.line.fee is Phí dòng đơn hàng TMĐT (Ecommerce Order Line Fee)|(ecommerce.order.line.fee)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.package.item is Mặt hàng gói hàng TMĐT (Ecommerce Package Item)|(ecommerce.package.item)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.package.list is Danh sách gói hàng TMĐT (Ecommerce Package List)|(ecommerce.package.list)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.payment.method is Phương thức thanh toán TMĐT (EcommercePaymentMethod)|(ecommerce.payment.method)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.schedule.log is Nhật ký lịch trình TMĐT (EcommerceScheduleLog)|(ecommerce.schedule.log)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
order.line.update is Cập nhật dòng đơn hàng (OrderLineUpdate)|(order.line.update)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sms.resend is Gửi lại SMS (SMS Resend)|(sms.resend)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
spreadsheet.mixin is Mixin bảng tính (Spreadsheet mixin)|(spreadsheet.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
snailmail.letter is Thư bưu điện Thư (Snailmail Letter)|(snailmail.letter)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.packaging is Gói hàng (Product Packaging)|(product.packaging)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lunch.topping is Bữa trưa bổ sung (Lunch Extras)|(lunch.topping)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.category is Danh mục sản phẩm (Product Category)|(product.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
tiktok.config.line is Dòng cấu hình TikTok (TiktokConfigLine)|(tiktok.config.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.root is 2 số đầu của mã tài khoản (Account codes first 2 digits)|(account.root)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.putaway.rule is Quy tắc lưu kho (Putaway Rule)|(stock.putaway.rule)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.country.state is Tỉnh/TP/Bang (Country state)|(res.country.state)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.replenish.mixin is Mixin bổ sung sản phẩm (Product Replenish Mixin)|(stock.replenish.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
resource.mixin is Nhân lực Mixin (Resource Mixin)|(resource.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
payment.token is Mã thanh toán (Payment Token)|(payment.token)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lunch.cashmove.report is Báo cáo chuyển tiền (Cashmoves report)|(lunch.cashmove.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.report is Báo cáo kế toán (Accounting Report)|(account.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.removal is Chiến lược xuất kho (Removal Strategy)|(product.removal)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.package.destination is Điểm đến của kiện hàng (Stock Package Destination)|(stock.package.destination)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
lunch.alert is Cảnh báo bữa ăn (Lunch Alert)|(lunch.alert)| Alerts to display during a lunch order. An alert can be specific to a
    given day, weekly or daily. The alert is displayed from start to end hour. 
res.country.group is Nhóm Quốc gia (Country Group)|(res.country.group)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
iap.enrich.api is IAP API Tăng cường lead (IAP Lead Enrichment API)|(iap.enrich.api)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
lunch.cashmove is Chuyển tiền bữa trưa (Lunch Cashmove)|(lunch.cashmove)| Two types of cashmoves: payment (credit) or order (debit) 
stock.storage.category.capacity is Sức chứa danh mục lưu kho (Storage Category Capacity)|(stock.storage.category.capacity)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.partner.category is Từ khoá đối tác (Partner Tags)|(res.partner.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.partner.title is Danh xưng đối tác (Partner Title)|(res.partner.title)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.quantity.history is Lịch sử số lượng tồn kho (Stock Quantity History)|(stock.quantity.history)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.request.count is Tồn kho cần kiểm kho (Stock Request an Inventory Count)|(stock.request.count)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
lunch.location is Địa điểm ăn trưa (Lunch Locations)|(lunch.location)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lunch.order is Đặt bữa trưa (Lunch Order)|(lunch.order)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lunch.product.category is Danh mục Sản phẩm Bữa trưa (Lunch Product Category)|(lunch.product.category)| Category of the product such as pizza, sandwich, pasta, chinese, burger... 
account.report.column is Cột báo cáo kế toán (Accounting Report Column)|(account.report.column)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lunch.supplier is Nhà cung cấp bữa ăn (Lunch Supplier)|(lunch.supplier)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.rules.report is Báo cáo quy tắc tồn kho (Stock Rules report)|(stock.rules.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
iap.autocomplete.api is IAP API tự động hoàn thành đối tác (IAP Partner Autocomplete API)|(iap.autocomplete.api)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
website.track is Trang đã xem (Visited Pages)|(website.track)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.edi.common is Các chức năng chung cho tài liệu EDI: tạo dữ liệu, ràng buộc,... (Common functions for EDI documents: generate the data, the constraints, etc)|(account.edi.common)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.partner.industry is Ngành nghề (Industry)|(res.partner.industry)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.move.send is Account Move Send (Account Move Send)|(account.move.send)| Shared class between the two sending wizards.
    See 'account.move.send.batch.wizard' for multiple invoices sending wizard (async)
    and 'account.move.send.wizard' for single invoice sending wizard (sync).
    
theme.ir.attachment is Chủ đề đính kèm (Theme Attachments)|(theme.ir.attachment)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
theme.ir.ui.view is Giao diện chủ đề (Theme UI View)|(theme.ir.ui.view)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
theme.utils is Chủ đề Utils (Theme Utils)|(theme.utils)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.warn.insufficient.qty is Cảnh báo số lượng không đủ (Warn Insufficient Quantity)|(stock.warn.insufficient.qty)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.move.reversal is Đảo bút toán kế toán (Account Move Reversal)|(account.move.reversal)|
    Account move reversal wizard, it cancel an account move by reversing it.
    
account.secure.entries.wizard is Bảo mật bút toán (Secure Journal Entries)|(account.secure.entries.wizard)|
    This wizard is used to secure journal entries (with a hash)
    
stock.track.line is Dòng theo dõi tồn kho (Stock Track Line)|(stock.track.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
resource.calendar.leaves is Chi tiết nghỉ phép (Resource Time Off Detail)|(resource.calendar.leaves)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
utm.source is Nguồn UTM (UTM Source)|(utm.source)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.replenishment.info is Thông tin bổ sung nhà cung cấp tồn kho (Stock supplier replenishment information)|(stock.replenishment.info)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.edi.xml.cii is Factur-x/XRechnung CII 2.2.0 (Factur-x/XRechnung CII 2.2.0)|(account.edi.xml.cii)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.replenishment.option is Tuỳ chọn bổ sung kho hàng (Stock warehouse replenishment option)|(stock.replenishment.option)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.storage.category is Danh mục lưu kho (Storage Category)|(stock.storage.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
web_tour.tour.step is Bước của tour (Tour's step)|(web_tour.tour.step)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.activity.plan is Kế hoạch hoạt động (Activity Plan)|(mail.activity.plan)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
iap.service is Dịch vụ IAP (IAP Service)|(iap.service)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.edi.xml.ubl_sg is SG BIS Billing 3.0 (SG BIS Billing 3.0)|(account.edi.xml.ubl_sg)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
payment.link.wizard is Tạo liên kết thanh toán bán hàng (Generate Sales Payment Link)|(payment.link.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.move.send.wizard is Account Move Send Wizard (Account Move Send Wizard)|(account.move.send.wizard)|Wizard that handles the sending a single invoice.
ecommerce.statement is E-commerce Statement|(ecommerce.statement)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.setup.bank.manual.config is Thiết lập ngân hàng thủ công (Bank setup manual config)|(account.setup.bank.manual.config)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sms.account.code is Công cụ mã xác minh (SMS Account Verification Code Wizard)|(sms.account.code)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.edi.xml.ubl_nl is SI-UBL 2.0 (NLCIUS) (SI-UBL 2.0 (NLCIUS))|(account.edi.xml.ubl_nl)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
web_tour.tour is Du lịch (Tours)|(web_tour.tour)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.departure.wizard is Công cụ hỗ trợ quá trình nghỉ việc (Departure Wizard)|(hr.departure.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.warn.insufficient.qty.scrap is Cảnh báo số lượng phế phẩm không đủ (Warn Insufficient Scrap Quantity)|(stock.warn.insufficient.qty.scrap)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.edi.xml.ubl_20 is UBL 2.0 (UBL 2.0)|(account.edi.xml.ubl_20)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
utm.source.mixin is Mixin nguồn UTM (UTM Source Mixin)|(utm.source.mixin)|Mixin responsible of generating the name of the source based on the content
    (field defined by _rec_name) of the record (mailing, social post,...).
    
payment.capture.wizard is Công cụ thu hồi thanh toán (Payment Capture Wizard)|(payment.capture.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.merge.wizard.line is Dòng trình hướng dẫn hợp nhất tài khoản (Account merge wizard line)|(account.merge.wizard.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
