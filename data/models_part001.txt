phone.blacklist.remove is <PERSON><PERSON><PERSON> số điện thoại khỏi danh sách hạn chế (Remove phone from blacklist)|(phone.blacklist.remove)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.merge.wizard is <PERSON><PERSON><PERSON><PERSON> dẫn hợp nhất tà<PERSON> (Account merge wizard)|(account.merge.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sms.account.sender is <PERSON><PERSON><PERSON> cụ tên người gửi (SMS Account Sender Name Wizard)|(sms.account.sender)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
website.robots is <PERSON><PERSON><PERSON><PERSON> chỉnh sửa <PERSON>.txt (Robots.txt Editor)|(website.robots)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mail.activity.schedule is Tính năng kế hoạch lên lịch hoạt động (Activity schedule plan Wizard)|(mail.activity.schedule)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
spreadsheet.dashboard is Trang chủ bảng tính (Spreadsheet Dashboard)|(spreadsheet.dashboard)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
google.gmail.mixin is Google Gmail Mixin (Google Gmail Mixin)|(google.gmail.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
sale.order.template.option is Tùy chọn mẫu báo giá (Quotation Template Option)|(sale.order.template.option)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.custom_blocked_third_party_domains is Danh sách miền của bên thứ 3 bị chặn của người dùng (User list of blocked 3rd-party domains)|(website.custom_blocked_third_party_domains)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
product.document is Tài liệu sản phẩm (Product Document)|(product.document)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
link.tracker is Trình theo dõi Liên kết (Link Tracker)|(link.tracker)| Link trackers allow users to wrap any URL into a short URL that can be
    tracked by Odoo. Clicks are counter on each link. A tracker is linked to
    UTMs allowing to analyze marketing actions.

    This model is also used in mass_mailing where each link in html body is
    automatically converted into a short link that is tracked and integrates
    UTMs. 
product.catalog.mixin is Mixin danh mục sản phẩm (Product Catalog Mixin)|(product.catalog.mixin)| This mixin should be inherited when the model should be able to work
    with the product catalog.
    It assumes the model using this mixin has a O2M field where the products are added/removed and
    this field's co-related model should has a method named `_get_product_catalog_lines_data`.
    
purchase.order.line is Dòng đơn mua hàng (Purchase Order Line)|(purchase.order.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order.option is Tùy chọn bán hàng (Sale Options)|(sale.order.option)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sms.account.phone is Công cụ đăng ký số điện thoại (SMS Account Registration Phone Number Wizard)|(sms.account.phone)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
link.tracker.click is Liên kết theo dõi số lần nhấn (Link Tracker Click)|(link.tracker.click)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.account.report_hash_integrity is Nhận kết quả toàn vẹn dữ liệu hash dưới dạng PDF. (Get hash integrity result as PDF.)|(report.account.report_hash_integrity)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
stock.lot is Lô/sê-ri (Lot/Serial)|(stock.lot)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
choose.delivery.carrier is Công cụ chọn đơn vị giao hàng (Delivery Carrier Selection Wizard)|(choose.delivery.carrier)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.incoterms is Incoterm (Incoterms)|(account.incoterms)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.return.picking.line is Dòng phiếu trả hàng (Return Picking Line)|(stock.return.picking.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
delivery.zip.prefix is Tiền tố zip giao hàng (Delivery Zip Prefix)|(delivery.zip.prefix)| Zip prefix that a delivery.carrier will deliver to. 
product.attribute.custom.value is Giá trị tùy chỉnh của thuộc tính sản phẩm (Product Attribute Custom Value)|(product.attribute.custom.value)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.invoice.report is Thống kê hóa đơn (Invoices Statistics)|(account.invoice.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.tracking.value is Giá trị Theo vết Thư (Mail Tracking Value)|(mail.tracking.value)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
link.tracker.code is Link Tracker Code (Link Tracker Code)|(link.tracker.code)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
wizard.ir.model.menu.create is Tạo Trình Menu (Create Menu Wizard)|(wizard.ir.model.menu.create)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
format.vat.label.mixin is Nhãn thuế GTGT cụ thể theo quốc gia (Country Specific VAT Label)|(format.vat.label.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
report.product.report_producttemplatelabel2x7 is Báo cáo nhãn sản phẩm 2x7 (Product Label Report 2x7)|(report.product.report_producttemplatelabel2x7)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
report.product.report_producttemplatelabel4x7 is Báo cáo nhãn sản phẩm 4x7 (Product Label Report 4x7)|(report.product.report_producttemplatelabel4x7)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
mail.resend.message is Tính năng gửi lại email (Email resend wizard)|(mail.resend.message)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
choose.delivery.package is Công cụ chọn kiện giao hàng (Delivery Package Selection Wizard)|(choose.delivery.package)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
delivery.price.rule is Quy tắc phí vận chuyển (Delivery Price Rules)|(delivery.price.rule)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.move.line is Dòng bút toán (Journal Item)|(account.move.line)| Override AccountInvoice_line to add the link to the purchase order line it is related to
sms.tracker is Liên kết SMS với các mô hình theo dõi thư/sms (Link SMS to mailing/sms tracking models)|(sms.tracker)|Relationship between a sent SMS and tracking records such as notifications and traces.

    This model acts as an extension of a `mail.notification` or a `mailing.trace` and allows to
    update those based on the SMS provider responses both at sending and when later receiving
    sent/delivery reports (see `SmsController`).
    SMS trackers are supposed to be created manually when necessary, and tied to their related
    SMS through the SMS UUID field. (They are not tied to the SMS records directly as those can
    be deleted when sent).

    Note: Only admins/system user should need to access (a fortiori modify) these technical
      records so no "sudo" is used nor should be required here.
    
sale.advance.payment.inv is Hoá đơn tạm ứng bán hàng (Sales Advance Payment Invoice)|(sale.advance.payment.inv)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
tiki.config is TikiConfig|(tiki.config)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
tiki.config.line is TikiConfigLine|(tiki.config.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.move.line is Điều chuyển sản phẩm (Dòng điều chuyển tồn kho) (Product Moves (Stock Move Line))|(stock.move.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.code.mapping is Mapping mã tài khoản theo từng công ty (Mapping of account codes per company)|(account.code.mapping)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.daybook.report is Day Book Report|(account.daybook.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.cashbook.report is Cash Book Report|(account.cashbook.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
account.bankbook.report is Bank Book Report|(account.bankbook.report)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.om_account_daily_reports.report_daybook is Day Book|(report.om_account_daily_reports.report_daybook)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
onboarding.onboarding is Onboarding (Onboarding)|(onboarding.onboarding)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.account.type is Loại tài khoản (Account Account Type)|(account.account.type)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
loyalty.generate.wizard is Tạo phiếu giảm giá (Generate Coupons)|(loyalty.generate.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
utm.stage is Giai đoạn của chiến dịch (Campaign Stage)|(utm.stage)|Stage for utm campaigns.
rating.mixin is Rating Mixin (Rating Mixin)|(rating.mixin)|This mixin adds rating statistics to mail.thread that already support ratings.
crm.team.member is Thành viên bộ phận sales (Sales Team Member)|(crm.team.member)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
rating.parent.mixin is Rating Parent Mixin (Rating Parent Mixin)|(rating.parent.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
onboarding.onboarding.step is Trình tự onboarding (Onboarding Step)|(onboarding.onboarding.step)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.bom.line is Dòng định mức nguyên liệu (Bill of Material Line)|(mrp.bom.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
loyalty.history is Lịch sử của thẻ khách hàng thân thiết và ví điện tử (History for Loyalty cards and Ewallets)|(loyalty.history)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order.line is Dòng đơn bán hàng (Sales Order Line)|(sale.order.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
utm.mixin is Mixin UTM (UTM Mixin)|(utm.mixin)| Mixin class for objects which can be tracked by marketing. 
delivery.carrier is Phương thức vận chuyển (Shipping Methods)|(delivery.carrier)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.payment.term.line is Chi tiết điều khoản thanh toán (Payment Terms Line)|(account.payment.term.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.route is Mọi tuyến website (All Website Route)|(website.route)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.reconcile.model is Thiết lập trước để tạo bút toán trong quá trình khớp hóa đơn và thanh toán (Preset to create journal entries during a invoices and payments matching)|(account.reconcile.model)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.order.cancel is Đơn bán hàng Huỷ (Sales Order Cancel)|(sale.order.cancel)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
loyalty.mail is Trao đổi về khách hàng thân thiết (Loyalty Communication)|(loyalty.mail)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.financial.report is Báo cáo kế toán (Account Report)|(account.financial.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
report.accounting_pdf_reports.report_partnerledger is Báo cáo sổ chi tiết công nợ đối tác (Partner Ledger Report)|(report.accounting_pdf_reports.report_partnerledger)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
res.brand is ResBrand|(res.brand)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.multi.mixin is Đa trang web Mixin (Multi Website Mixin)|(website.multi.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
followup.followup is Theo dõi công nợ (Account Follow-up)|(followup.followup)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
utm.tag is Thẻ UTM (UTM Tag)|(utm.tag)|Model of categories of utm campaigns, i.e. marketing, newsletter, ...
change.production.qty is Thay đổi SL sản xuất (Change Production Qty)|(change.production.qty)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.mrp.report_mo_overview is Báo cáo tổng quan ĐMNL (MO Overview Report)|(report.mrp.report_mo_overview)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
website.published.multi.mixin is Nhiều trang web được xuất bản Mixin (Multi Website Published Mixin)|(website.published.multi.mixin)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
account.asset.category is Loại tài sản (Asset category)|(account.asset.category)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.journal.group is Nhóm sổ nhật kí tài khoản (Account Journal Group)|(account.journal.group)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
payment.provider is Nhà cung cấp dịch vụ thanh toán (Payment Provider)|(payment.provider)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.account.tag is Thẻ tài khoản (Account Tag)|(account.account.tag)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sms.template is Mẫu SMS (SMS Templates)|(sms.template)|Templates for sending SMS
snailmail.letter.missing.required.fields is Cập nhật địa chỉ của đối tác (Update address of partner)|(snailmail.letter.missing.required.fields)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
stock.scrap is Phế phẩm (Scrap)|(stock.scrap)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.fiscal.position.account is Mapping tài khoản của vị trí tài chính (Accounts Mapping of Fiscal Position)|(account.fiscal.position.account)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
hr.departure.reason is Lý do nghỉ việc (Departure Reason)|(hr.departure.reason)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
product.template is Mẫu sản phẩm (Product Template)|(product.template)|This mixin adds rating statistics to mail.thread that already support ratings.
sms.composer is Công cụ gửi SMS (Send SMS Wizard)|(sms.composer)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
mrp.production.split.line is Chi tiết tách sản lượng (Split Production Detail)|(mrp.production.split.line)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
loyalty.card.update.balance is Cập nhật điểm thẻ khách hàng thân thiết (Update Loyalty Card Points)|(loyalty.card.update.balance)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
loyalty.rule is Quy tắc khách hàng thân thiết (Loyalty Rule)|(loyalty.rule)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
followup.line is Tiêu chí theo dõi (Follow-up Criteria)|(followup.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
res.partner is Liên hệ (Contact)|(res.partner)| Purpose of this mixin is to offer two services

      * compute a sanitized phone number based on _phone_get_number_fields.
        It takes first sanitized value, trying each field returned by the
        method (see ``BaseModel._phone_get_number_fields()´´ for more details
        about the usage of this method);
      * compute blacklist state of records. It is based on phone.blacklist
        model and give an easy-to-use field and API to manipulate blacklisted
        records;

    Main API methods

      * ``_phone_set_blacklisted``: set recordset as blacklisted;
      * ``_phone_reset_blacklisted``: reactivate recordset (even if not blacklisted
        this method can be called safely);
    
product.product is Biến thể sản phẩm (Product Variant)|(product.product)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.accrued.orders.wizard is Công cụ đơn hàng dồn tích (Accrued Orders Wizard)|(account.accrued.orders.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
sequence.mixin is Số thứ tự tự động (Automatic sequence)|(sequence.mixin)|Mechanism used to have an editable sequence number.

    Be careful of how you use this regarding the prefixes. More info in the
    docstring of _get_last_sequence.
    
account.bank.statement is Sao kê ngân hàng (Bank Statement)|(account.bank.statement)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mrp.production.backorder is Công cụ để đánh dấu là đã hoàn tất hoặc tạo đơn hàng chậm trễ (Wizard to mark as done or create back order)|(mrp.production.backorder)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
_unknown is Không xác định (Unknown)|(_unknown)|
    Abstract model used as a substitute for relational fields with an unknown
    comodel.
    
loyalty.reward is Phần thưởng khách hàng thân thiết (Loyalty Reward)|(loyalty.reward)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
mail.alias.mixin is Email Mixin bí danh (Email Aliases Mixin)|(mail.alias.mixin)| A mixin for models that inherits mail.alias to have a one-to-one relation
    between the model and its alias. 
loyalty.card is Phiếu giảm giá khách hàng thân thiết (Loyalty Coupon)|(loyalty.card)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.inventory.warning is Cảnh báo điều chỉnh tồn kho (Inventory Adjustment Warning)|(stock.inventory.warning)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
ecommerce.order is Đơn hàng TMĐT (Ecommerce Order)|(ecommerce.order)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
loyalty.program is Chương trình khách hàng thân thiết (Loyalty Program)|(loyalty.program)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.order.line is Dòng đơn hàng TMĐT (Ecommerce Order Line)|(ecommerce.order.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.tax is Thuế (Tax)|(account.tax)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.product.product is Sản phẩm TMĐT (EcommerceProductProduct)|(ecommerce.product.product)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
update.product.attribute.value is Cập nhật giá trị thuộc tính sản phẩm (Update product attribute value)|(update.product.attribute.value)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
lazada.config is Cấu hình Lazada (LazadaConfig)|(lazada.config)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
lazada.config.line is Dòng cấu hình Lazada (LazadaConfigLine)|(lazada.config.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
shopee.config is Cấu hình Shopee (Shopee Config)|(shopee.config)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
shopee.config.line is Dòng cấu hình Shopee (ShopeeConfigLine)|(shopee.config.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
tiktok.config is Cấu hình TikTok (TiktokConfig)|(tiktok.config)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.shop is Cửa hàng TMĐT (EcommerceShop)|(ecommerce.shop)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
ecommerce.warehouse.mapping is Ánh xạ kho TMĐT (EcommerceWarehouseMapping)|(ecommerce.warehouse.mapping)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.asset.depreciation.line is Dòng khấu hao tài sản (Asset depreciation line)|(account.asset.depreciation.line)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.asset.asset is Tài sản/Ghi nhận doanh thu (Asset/Revenue Recognition)|(account.asset.asset)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
asset.asset.report is Phân tích tài sản (Assets Analysis)|(asset.asset.report)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
stock.forecasted_product_product is Báo cáo bổ sung tồn kho (Stock Replenishment Report)|(stock.forecasted_product_product)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
crossovered.budget is Ngân sách (Budget)|(crossovered.budget)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
crossovered.budget.lines is Dòng ngân sách (Budget Line)|(crossovered.budget.lines)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
website.page is Trang (Page)|(website.page)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.budget.post is Hạng mục ngân sách (Budgetary Position)|(account.budget.post)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
sale.loyalty.coupon.wizard is Khách hàng thân thiết bán hàng - Công cụ áp dụng phiếu giảm giá (Sale Loyalty - Apply Coupon Wizard)|(sale.loyalty.coupon.wizard)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
report.product.report_producttemplatelabel4x12 is Báo cáo nhãn sản phẩm 4x12 (Product Label Report 4x12)|(report.product.report_producttemplatelabel4x12)|The base model, which is implicitly inherited by all models.

    A new :meth:`~with_delay` method is added on all Odoo Models, allowing to
    postpone the execution of a job method in an asynchronous process.
    
followup.stat is Thống kê theo dõi (Follow-up Statistics)|(followup.stat)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
followup.stat.by.partner is Thống kê theo dõi theo đối tác (Follow-up Statistics by Partner)|(followup.stat.by.partner)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
account.fiscal.position.tax is Mapping thuế của vị trí tài chính (Tax Mapping of Fiscal Position)|(account.fiscal.position.tax)| Main super-class for regular database-persisted Odoo models.

    Odoo models are created by inheriting from this class::

        class user(Model):
            ...

    The system will later instantiate the class once per database (on
    which the class' module is installed).
    
asset.modify is Sửa đổi tài sản (Modify Asset)|(asset.modify)| Model super-class for transient records, meant to be temporarily
    persistent, and regularly vacuum-cleaned.

    A TransientModel has a simplified access rights management, all users can
    create new records, and may only access the records they created. The
    superuser has unrestricted access to all TransientModel records.
    
web_editor.assets is Assets Utils (Assets Utils)|(web_editor.assets)|The base model, which is implicitly inherited by all models.

