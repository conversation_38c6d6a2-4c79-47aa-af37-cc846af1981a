#!/bin/bash

# Script để khởi động SQL Tool với giao diện Gradio

echo "🚀 Đang khởi động SQL Tool với giao diện Gradio..."

# Kiểm tra Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 không được tìm thấy. Vui lòng cài đặt Python3."
    exit 1
fi

# Kiểm tra pip
if ! command -v pip &> /dev/null; then
    echo "❌ pip không được tìm thấy. Vui lòng cài đặt pip."
    exit 1
fi

# Cài đặt dependencies nếu cần
echo "📦 Kiểm tra dependencies..."
pip install -r requirements.txt

# Chạy ứng dụng
echo "🌐 Khởi động giao diện web..."
echo "📍 Ứng dụng sẽ khả dụng tại: http://localhost:7860"
echo "🛑 Nhấn Ctrl+C để dừng ứng dụng"
echo ""

python3 gradio_app.py
