{"account_move": {"columns": {"id": {"type": "integer"}, "partner_id": {"type": "many2one"}, "invoice_date": {"type": "date"}, "state": {"type": "selection"}}}, "account_move_line": {"columns": {"id": {"type": "integer"}, "move_id": {"type": "many2one"}, "account_id": {"type": "many2one"}, "credit": {"type": "monetary"}, "debit": {"type": "monetary"}}}, "res_partner": {"columns": {"id": {"type": "integer"}, "name": {"type": "char"}}}, "account_account": {"columns": {"id": {"type": "integer"}, "code": {"type": "char"}, "name": {"type": "char"}}}}