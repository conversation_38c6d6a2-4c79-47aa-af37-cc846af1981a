# main.py
import os
import argparse
from translation.translator import Translator
from nl2sql.schema_linker import <PERSON><PERSON>a<PERSON><PERSON><PERSON>
from nl2sql.duckdb_sql_executor import DuckDBSQLExecutor
from response.answer_generator import AnswerGenerator
from utils.text_utils import TextUtils
from config.settings import Settings

def build_db_uri():
    return (
        f"postgresql+psycopg2://{Settings.ODOO_DB_USER}:{Settings.ODOO_DB_PASSWORD}"
        f"@{Settings.ODOO_DB_HOST}/{Settings.ODOO_DB_NAME}"
    )

def main():
  parser = argparse.ArgumentParser()
  parser.add_argument("--refresh-schema", action="store_true",
                      help="Force update schema from Odoo DB")
  parser.add_argument("--question", type=str, default="Số sale order năm 2025?",
                      help="Câu hỏi của người dùng")
  parser.add_argument("--slm", type=str, default="duckdb-nsql:latest",
                      help="SQL LLM Model")
  parser.add_argument("--flm", type=str, default="sqlcoder:7b",
                      help="Fix LLM Model")
  parser.add_argument("--alm", type=str, default="gemma3:1b",
                      help="Answer LLM Model")
  parser.add_argument("--tm", type=str, default="VietAI/envit5-translation",
                      help="Translation Model")
  args = parser.parse_args()
  # Khởi tạo lại nếu cần
  if args.refresh_schema:
      from init import main as init_main
      init_main()

  # Câu hỏi mẫu (hoặc bạn có thể dùng input())
  question_vi = args.question

  # 1. Chuẩn hóa & dịch
  try:
    # 1. Chuẩn hóa & dịch
    glossary = TextUtils.load_glossary("data/glossary.csv")
    question_vi_norm = TextUtils.normalize_business_terms(question_vi, glossary)
    translator = Translator(model_name=args.tm or "VietAI/envit5-translation")
    question_en = translator.vi_to_en(question_vi_norm)
    print(f"🌐 Câu hỏi (EN): {question_en}")

    # 2. Schema linking + DDL
    linker = SchemaLinker()
    relevant_tables, ddl = linker.get_relevant_tables_and_ddl(question_en)

    # 3. Sinh SQL + execute + retry
    # executor = DuckDBSQLExecutor(
    #     db_uri=build_db_uri(),
    #     sql_llm_model= args.slm or "duckdb-nsql:latest",
    #     fix_llm_model= args.flm or "sqlcoder:7b",
    #     max_retries=2
    # )
    # result = executor.run(ddl, question_en)

    # print("\n✅ SQL:")
    # print(result["sql"])
    # print("\n📊 Kết quả:")
    # print(result["result"])

    # # 4. Trả lời tiếng Việt
    # if result["success"]:
    #   answer_gen = AnswerGenerator(model=args.alm or "gemma3:1b")
    #   final_answer = answer_gen.generate_vi(question_vi, result["result"])
    #   print("\n💬 Trả lời:")
    #   print(final_answer)

  finally:
    # Đảm bảo đóng kết nối KG
    if 'linker' in locals():
      linker.neo4j_client.close()

if __name__ == "__main__":
    main()