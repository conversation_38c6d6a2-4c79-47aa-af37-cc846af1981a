from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
import torch

# Ensure the safetensors library is installed
# pip install safetensors

device = "cuda" if torch.cuda.is_available() else "cpu"

model_name = "VietAI/envit5-translation"
tokenizer = AutoTokenizer.from_pretrained(model_name)
# The library will now automatically check for and prefer the `safetensors` version
model = AutoModelForSeq2SeqLM.from_pretrained(model_name).to(device)

inputs = [
    "vi: VietAI là tổ chức phi lợi nhuận với sứ mệnh ươm mầm tài năng về trí tuệ nhân tạo...",
    "en: We're on a journey to advance and democratize artificial intelligence through open source and open science."
]

encoded = tokenizer(inputs, return_tensors="pt", padding=True).to(device)
outputs = model.generate(**encoded, max_length=512)
print(tokenizer.batch_decode(outputs, skip_special_tokens=True))


# ['en: VietAI is a non-profit organization with the mission of nurturing artificial intelligence talents and building an international - class community of artificial intelligence experts in Vietnam.',
#  'en: According to the latest LinkedIn report on the 2020 list of attractive and promising jobs, AI - related job titles such as AI Specialist, ML Engineer and ML Engineer all rank high.',
#  'vi: Nhóm chúng tôi khao khát tạo ra những khám phá có ảnh hưởng đến mọi người, và cốt lõi trong cách tiếp cận của chúng tôi là chia sẻ nghiên cứu và công cụ để thúc đẩy sự tiến bộ trong lĩnh vực này.',
#  'vi: Chúng ta đang trên hành trình tiến bộ và dân chủ hoá trí tuệ nhân tạo thông qua mã nguồn mở và khoa học mở.']
