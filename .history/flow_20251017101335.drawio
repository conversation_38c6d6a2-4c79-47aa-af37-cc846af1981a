<mxfile host="65bd71144e">
    <diagram id="jLt1InUp8jDxtfBZRfpr" name="Page-1">
        <mxGraphModel dx="1058" dy="583" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="4" value="" style="edgeStyle=none;html=1;" parent="1" source="2" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="Local model:&lt;div&gt;VietAI/envit5 dùng để dịch từ tiếng Việt qua tiếng Anh&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;align=left;spacingLeft=8;spacingRight=8;" parent="1" vertex="1">
                    <mxGeometry x="40" y="50" width="220" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="3" target="5">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="- Sử dụng thuật toán để tách các keywords của câu hỏi. Có thể sử dụng LLM hoặc thủ công tách từng chữ.&lt;div&gt;- Query KG =&amp;gt; truy vấn node Term để lấy các từ điển của doanh nghiệp có liên quan.&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=1;align=left;spacingLeft=8;spacingRight=8;" parent="1" vertex="1">
                    <mxGeometry x="315" y="50" width="220" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="7">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="635" y="200"/>
                            <mxPoint x="190" y="200"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="- Sử dụng keywords đã extract =&amp;gt; join lại sau đó chuyển thành vector embedding =&amp;gt; truy vấn KG của node Table, Field" style="whiteSpace=wrap;html=1;align=left;rounded=1;spacingLeft=8;spacingRight=8;" vertex="1" parent="1">
                    <mxGeometry x="600" y="50" width="220" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="7" target="9">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="&lt;font style=&quot;color: rgb(0, 0, 0);&quot;&gt;Sử dụng LLM để đánh giá những table có liên quan nhất của kết quả KG trả về&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;color: rgb(0, 0, 0);&quot;&gt;Giảm số lượng table và token trong truy vấn&lt;/font&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;align=left;rounded=1;spacingLeft=8;spacingRight=8;" vertex="1" parent="1">
                    <mxGeometry x="40" y="230" width="220" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;Extract những tables đó ra cấu trúc để đưa vào prompt cho LLM DuckDBSql&lt;/span&gt;" style="whiteSpace=wrap;html=1;align=left;rounded=1;spacingLeft=8;spacingRight=8;" vertex="1" parent="1">
                    <mxGeometry x="320" y="230" width="220" height="110" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>