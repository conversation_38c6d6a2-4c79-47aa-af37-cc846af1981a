# main.py
from translation.translator import Translator
from nl2sql.schema_linker import <PERSON>hem<PERSON><PERSON>inker
from nl2sql.duckdb_sql_executor import DuckDBSQLExecutor
from response.answer_generator import AnswerGenerator
from utils.text_utils import TextUtils
from config.settings import Settings
import os

def build_db_uri():
    return (
        f"postgresql+psycopg2://{Settings.ODOO_DB_USER}:{Settings.ODOO_DB_PASSWORD}"
        f"@{Settings.ODOO_DB_HOST}/{Settings.ODOO_DB_NAME}"
    )

def main():
    question_vi = "Doanh thu từ khách hàng ABC tháng 10 năm 2024?"

    # 1. Chuẩn hóa & dịch
    glossary = TextUtils.load_glossary("data/glossary.csv")
    question_vi_norm = TextUtils.normalize_business_terms(question_vi, glossary)
    translator = Translator()
    question_en = translator.vi_to_en(question_vi_norm)

    # 2. Lấy DDL
    linker = SchemaLinker()
    _, ddl = linker.get_relevant_tables_and_ddl(question_en)

    # 3. Execute với retry
    executor = DuckDBSQLExecutor(
        db_uri=build_db_uri(),
        sql_llm_model="nsql:7b",
        fix_llm_model="llama3:8b",  # hoặc "gpt-4o" nếu có OpenAI API
        max_retries=2
    )
    result = executor.run(ddl, question_en)

    print("✅ SQL cuối cùng:")
    print(result["sql"])
    print("\n📊 Kết quả:")
    print(result["result"])

    # 4. Trả lời tiếng Việt (nếu thành công)
    if result["success"]:
        answer_gen = AnswerGenerator()
        final_answer = answer_gen.generate_vi(question_vi, result["result"])
        print("\n💬 Trả lời người dùng:")
        print(final_answer)

if __name__ == "__main__":
    main()