<mxfile host="65bd71144e">
    <diagram id="jLt1InUp8jDxtfBZRfpr" name="Page-1">
        <mxGraphModel dx="1058" dy="583" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="4" value="" style="edgeStyle=none;html=1;" parent="1" source="2" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="Local model:&lt;div&gt;VietAI/envit5 dùng để dịch từ tiếng Việt qua tiếng Anh&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="70" y="80" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="- Sử dụng thuật toán để tách các keywords của câu hỏi. Có thể sử dụng LLM hoặc thủ công tách từng chữ.&lt;div&gt;- Query KG =&amp;gt; truy vấn node Term để lấy các từ điển của doanh nghiệp có liên quan.&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=1;align=left;spacingLeft=8;spacingRight=8;" parent="1" vertex="1">
                    <mxGeometry x="260" y="55" width="220" height="110" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>