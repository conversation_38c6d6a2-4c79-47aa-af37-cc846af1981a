# nl2sql/duckdb_sql_executor.py
from langchain_community.utilities import SQLDatabase
from langchain_community.llms import Ollama
from typing import Optional
import re
from config.settings import Settings

class DuckDBSQLExecutor:
    def __init__(
        self,
        db_uri: str,
        sql_llm_model: str = "kimi-k2:1t-cloud",        # duckdb-nsql
        fix_llm_model: str = "glm-4.6:cloud",      # LLM sửa lỗi
        max_retries: int = 3
    ):
        self.db = SQLDatabase.from_uri(db_uri)
        self.sql_llm = Ollama(base_url="https://ollama.com", headers={'Authorization': 'Bearer ' + Settings.OLLAMA_API_KEY}, model=sql_llm_model, temperature=0)
        self.fix_llm = Ollama(base_url="https://ollama.com", headers={'Authorization': 'Bearer ' + Settings.OLLAMA_API_KEY}, model=fix_llm_model, temperature=0)
        self.max_retries = max_retries

    def _generate_sql(self, ddl: str, question: str, business_concepts: Optional[str] = None) -> str:
        """Sinh SQL bằng duckdb-nsql"""
        prompt = f"""
            You are an expert in Odoo 18 and PostgreSQL databases.
            Your task is to translate a natural language question and business concepts into a valid SQL query that can be executed within an Odoo 18 environment.

            ### System context:
            - Odoo version: 18
            - Database: PostgreSQL
            - Table names usually start with prefixes like `res_`, `hr_`, `account_`, `sale_`, `stock_`, `purchase_`, `mrp_`, `product_`, `crm_`, `project_`, `report_`, `ecommerce_`, etc.
            - Always follow standard PostgreSQL SQL syntax.
            - Avoid using any data or columns not defined in the provided DDL.
            - When column are in type character, text, or selection:
                - accent sensitivity
                - lower case
                - use the `ILIKE` operator instead of `=`
            - When table has column state use it to filter data.
            - When table has column active use it to filter data.

            ### Database schema (DDL of relevant tables):
            {ddl}

            ### User question:
            {question}

            ### Business concepts:
            {business_concepts}

            ### Output requirements:
            - Return **only one valid SQL query** that correctly answers the question.
            - Use proper table joins based on foreign key relationships shown in the DDL.
            - Avoid assumptions beyond what’s available in the DDL.
            - If the question is ambiguous, interpret it in the most common Odoo business context.

            ### Expected output format:
            ```sql ```
        """
        response = self.sql_llm.invoke(prompt)
        return self._clean_sql(response)

    def _clean_sql(self, sql: str) -> str:
        """Làm sạch SQL (loại bỏ markdown, comment)"""
        # Loại bỏ ```sql ... ```
        sql = re.sub(r"```sql?\s*", "", sql)
        sql = re.sub(r"```", "", sql)
        # Loại bỏ dòng bắt đầu bằng --
        sql = "\n".join(line for line in sql.split("\n") if not line.strip().startswith("--"))
        return sql.strip()

    def _fix_sql(self, sql: str, error: str, ddl: str, question: str) -> str:
        """Dùng LLM mạnh sửa lỗi SQL"""
        prompt = f""" 
        ### Instructions:
        Your task is to convert a question into a SQL query, given a Postgres database schema.
        Adhere to these rules:
        - **Deliberately go through the question and database schema word by word** to appropriately answer the question
        - **Use Table Aliases** to prevent ambiguity. For example, `SELECT table1.col1, table2.col1 FROM table1 JOIN table2 ON table1.id = table2.id`.
        - When column is a character field, use the `ILIKE` operator instead of `=`, upper/lower case and accent sensitivity to perform a case-insensitive search.

        ### Input:
        Generate a SQL query that answers the question `{question}`.
        This query will run on a database whose schema is represented in this string:
        Fix this SQL query.
        Error: {error}

        Schema:
        {ddl}

        Incorrect SQL:
        {sql}

        ### Response:
        Based on your instructions, here is the SQL query I have generated to answer the question `{question}`:
        ```sql
        """
        response = self.fix_llm.invoke(prompt)
        return self._clean_sql(response)

    def run(self, ddl: str, question: str, business_concepts: Optional[str] = None) -> dict:
        """
        Trả về dict: {"sql": "...", "result": "...", "success": True/False}
        """
        sql = self._generate_sql(ddl, question, business_concepts)
        last_error = None

        for attempt in range(self.max_retries + 1):
            try:
                # LangChain tự động kết nối và execute
                result = self.db.run(sql)
                return {
                    "sql": sql,
                    "result": result,
                    "success": True
                }
            except Exception as e:
                last_error = str(e)
                if attempt >= self.max_retries:
                    break
                # Sửa lỗi và thử lại
                sql = self._fix_sql(sql, last_error, ddl, question)

        return {
            "sql": sql,
            "result": f"❌ Lỗi sau {self.max_retries} lần thử: {last_error}",
            "success": False
        }