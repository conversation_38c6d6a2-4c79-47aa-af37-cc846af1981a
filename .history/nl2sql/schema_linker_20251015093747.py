# nl2sql/schema_linker.py
import json
import re
from typing import List, Tuple
from utils.text_utils import TextUtils
from utils.ddl_generator import generate_ddl_from_schema

class SchemaLinker:
    def __init__(
        self,
        glossary_path: str = "data/glossary.csv",
        descriptions_path: str = "data/table_descriptions_en.json",
        schema_path: str = "data/table_schema.json"
    ):
        self.glossary = TextUtils.load_glossary(glossary_path)
        with open(descriptions_path, "r", encoding="utf-8") as f:
            self.descriptions = json.load(f)
        with open(schema_path, "r", encoding="utf-8") as f:
            self.schema = json.load(f)

    def get_relevant_tables_and_ddl(self, question_en: str) -> Tuple[List[str], str]:
        """
        Tr<PERSON> về (danh sách bảng liên quan, DDL string)
        """
        # Bước 1: <PERSON>r<PERSON><PERSON> từ khóa
        keywords = set(re.findall(r"\b\w+\b", question_en.lower()))
        relevant_tables = []

        # Bước 2: So khớp với mô tả (tiếng Anh)
        for table, meta in self.descriptions.items():
            desc = meta["description"].lower()
            if any(kw in desc for kw in keywords):
                relevant_tables.append(table)
                continue
            for col_desc in meta["columns"].values():
                if any(kw in col_desc.lower() for kw in keywords):
                    relevant_tables.append(table)
                    break

        # Fallback: nếu không tìm thấy, lấy tất cả bảng có trong schema
        if not relevant_tables:
            relevant_tables = list(self.schema.keys())

        # Loại bỏ trùng lặp
        relevant_tables = list(set(relevant_tables))

        # Bước 3: Tạo DDL
        ddl = generate_ddl_from_schema(self.schema, relevant_tables)
        
        return relevant_tables, ddl