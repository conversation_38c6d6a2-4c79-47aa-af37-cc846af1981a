import json
import re
from typing import List
from utils.text_utils import TextUtils

class SchemaLinker:
    def __init__(
        self,
        glossary_path: str = "data/glossary.csv",
        descriptions_path: str = "data/table_descriptions_en.json"
    ):
        self.glossary = TextUtils.load_glossary(glossary_path)
        with open(descriptions_path, "r", encoding="utf-8") as f:
            self.descriptions = json.load(f)

    def get_relevant_tables(self, question_en: str) -> List[str]:
        # Trích từ khóa tiếng Anh
        keywords = set(re.findall(r"\b\w+\b", question_en.lower()))
        relevant = []

        for table, meta in self.descriptions.items():
            desc = meta["description"].lower()
            if any(kw in desc for kw in keywords):
                relevant.append(table)
                continue
            for col_desc in meta["columns"].values():
                if any(kw in col_desc.lower() for kw in keywords):
                    relevant.append(table)
                    break

        return list(set(relevant)) or list(self.descriptions.keys())