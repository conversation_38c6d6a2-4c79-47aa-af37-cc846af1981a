# nl2sql/schema_linker.py (phi<PERSON><PERSON> bản kết hợp KG + DDL)
import json
import re
from typing import List, Tuple
from utils.text_utils import TextUtils
from utils.ddl_generator import generate_ddl_from_schema
from graph.neo4j_client import Neo4jClient  # <-- DÙNG KG
from utils.embedding import OllamaEmbedder  # <-- DÙNG VECTOR EMBEDDING
import ollama
import json

class SchemaLinker:
    def __init__(
        self,
        glossary_path: str = "data/glossary.csv",
        schema_path: str = "data/table_schema.json"
    ):
        self.glossary = TextUtils.load_glossary(glossary_path)
        with open(schema_path, "r", encoding="utf-8") as f:
            self.schema = json.load(f)
        self.neo4j_client = Neo4jClient()  # <-- KHỞI TẠO KG
        self.embedding = OllamaEmbedder()  # <-- DÙNG VECTOR EMBEDDING
        # self.query_expander = ollama.chat(model="gemma3:1b", temperature=0.3)

    def _expand_question(self, question_vi: str) -> List[str]:
        """Sinh 3 câu hỏi tiếng Anh liên quan"""
        prompt = f"""
            You are a business analyst. Given a Vietnamese question, generate 3 related English questions that help retrieve database schema.

            Vietnamese question: "{question_vi}"

            Requirements:
            - Keep the original intent
            - Use business terms (revenue, customer, product)
            - Output only JSON list of strings

            Example output:
            ["What is the total revenue?", "Show sales by customer", "List products sold"]

            Output:
        """
        try:
            response = ollama.generate(model="gemma3:1b", prompt=prompt, stream=False)
            match = re.search(r'(\[.*\]|\{.*\})', response.response, re.S)
            if match:
                json_str = match.group(1)
                return json.loads(json_str)[:3]
            return [question_vi]  # Fallback: dùng câu gốc
        except Exception as e:
            print(f"⚠️ Lỗi khi mở rộng câu hỏi: {e}")
            return [question_vi]  # Fallback: dùng câu gốc

    def _extract_keywords(self, text: str) -> List[str]:
        """Trích từ khóa và mở rộng bằng semantic search"""
        words = re.findall(r"\b\w+\b", text.lower())
        expanded = set()

        for w in words:
            if len(w) < 2:
                continue
            if w in ['en', '2en']:
                continue

            # Luôn thêm từ gốc
            expanded.add(w.lower())

            # Mở rộng bằng semantic search
            concept = self.neo4j_client.get_business_concept_semantic(w, threshold=0.5)
            if concept.lower() != w.lower():
                expanded.add(concept.lower())

        return list(expanded)

    def get_relevant_tables_and_ddl(self, question_vi: str) -> Tuple[List[str], str]:
        """
        DÙNG KG ĐỂ XÁC ĐỊNH BẢNG LIÊN QUAN, sau đó tạo DDL.
        """
        # Bước 1: Trích từ khóa
        expanded_questions = self._expand_question(question_vi)
        print(f"🔍 Câu hỏi mở rộng: {expanded_questions}")
        keywords = self._extract_keywords(', '.join(expanded_questions))

        # Bước 2: DÙNG KG ĐỂ TÌM SUBGRAPH
        subgraph = self.neo4j_client.retrieve_subgraph(keywords)

        # Bước 3: Trích tên bảng từ KG
        relevant_tables = self.neo4j_client.extract_tables_from_subgraph(subgraph)

        # Fallback: nếu KG không trả về bảng nào
        if not relevant_tables:
            print("⚠️ KG không tìm thấy bảng, dùng toàn bộ schema")
            relevant_tables = list(self.schema.keys())

        print(f"🔍 KG tìm thấy {len(relevant_tables)} bảng: {relevant_tables}")

        # Bước 4: Tạo DDL từ bảng liên quan
        ddl = generate_ddl_from_schema(self.schema, relevant_tables)

        return relevant_tables, ddl