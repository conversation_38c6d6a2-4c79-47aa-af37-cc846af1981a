# nl2sql/schema_linker.py (phi<PERSON><PERSON> bản kết hợp KG + DDL)
import json
import re
from typing import List, Tuple
from utils.text_utils import TextUtils
from utils.ddl_generator import generate_ddl_from_schema
from graph.neo4j_client import Neo4jClient  # <-- DÙNG KG
from utils.embedding import OllamaEmbedder  # <-- DÙNG VECTOR EMBEDDING
from llm.ollama.ollama import OllamaLLM
import json
from config.settings import Settings

class SchemaLinker:
    def __init__(
        self,
        glossary_path: str = "data/glossary.csv",
        schema_path: str = "data/table_schema.json"
    ):
        self.glossary = TextUtils.load_glossary(glossary_path)
        with open(schema_path, "r", encoding="utf-8") as f:
            self.schema = json.load(f)
        self.neo4j_client = Neo4jClient()
        self.embedding = OllamaEmbedder()
        self.ollama_client = OllamaLLM(model="kimi-k2:1t-cloud")
        # self.query_expander = ollama.chat(model="gemma3:1b", temperature=0.3)

    def _expand_question(self, question_vi: str) -> List[str]:
        """Sinh 3 câu hỏi tiếng Anh liên quan"""
        prompt = f"""
            You are a business analyst. Given a Vietnamese question, generate 3 related English questions that help retrieve database schema.

            Vietnamese question: "{question_vi}"

            Requirements:
            - Keep the original intent
            - Use business terms (revenue, customer, product)
            - Output only JSON list of strings

            Example output:
            ["What is the total revenue?", "Show sales by customer", "List products sold"]

            Output:
        """
        try:
            response = self.ollama_client.generate(prompt=prompt, stream=False)
            match = re.search(r'(\[.*\]|\{.*\})', response, re.S)
            if match:
                json_str = match.group(1)
                return json.loads(json_str)[:3]
            return [question_vi]  # Fallback: dùng câu gốc
        except Exception as e:
            print(f"⚠️ Lỗi khi mở rộng câu hỏi: {e}")
            return [question_vi]  # Fallback: dùng câu gốc

    def _extract_keywords(self, questions: list) -> List[str]:
        expand = []
        for q in questions.replace(",", "").split(" "):
            concept = self.neo4j_client.get_business_concept_semantic(q, threshold=0.5)
            expand.append(concept)
        prompt = f"""
            Extract the most relevant keywords or entities from the following list of user questions and business concepts.
            Focus on identifying important business entities, metrics, and time values that could be used as parameters for a Neo4j query.
            Return the result strictly in JSON format like:
            [
                {{
                    "question": "...",
                    "keywords": ["keyword1", "keyword2", "keyword3"]
                }}
            ]

            Questions:
            {questions}

            Business concepts:
            {expand}
            """
        """Trích từ khóa và mở rộng bằng semantic search"""
        # words = re.findall(r"\b\w+\b", text.lower())
        # expanded = set()

        # for w in words:
        #     if len(w) < 2:
        #         continue
        #     if w in ['en', '2en']:
        #         continue

        #     # Luôn thêm từ gốc
        #     expanded.add(w.lower())

        #     # Mở rộng bằng semantic search
        #     concept = self.neo4j_client.get_business_concept_semantic(w, threshold=0.5)
        #     if concept.lower() != w.lower():
        #         expanded.add(concept.lower())

        # return list(expanded)
        try:
            response = self.ollama_client.generate(prompt=prompt, stream=False)
            match = re.search(r'(\[.*\]|\{.*\})', response, re.S)
            if match:
                json_str = match.group(1)
                keywords = []
                for item in json.loads(json_str)[:3]:
                    keywords.extend(item["keywords"])
            return list(set(keywords))
        except Exception as e:
            print(f"⚠️ Lỗi khi trích từ khóa: {e}")
            return []  # Fallback: không tìm thấy từ khóa

    def get_relevant_tables_and_ddl(self, question_vi: str) -> Tuple[List[str], str]:
        """
        DÙNG KG ĐỂ XÁC ĐỊNH BẢNG LIÊN QUAN, sau đó tạo DDL.
        """
        # Bước 1: Trích từ khóa
        expanded_questions = self._expand_question(question_vi)
        expanded_questions_text = ', '.join(expanded_questions)
        print(f"🔍 Câu hỏi mở rộng: {expanded_questions}")
        keywords = self._extract_keywords(expanded_questions)

        # Bước 2: DÙNG KG ĐỂ TÌM SUBGRAPH
        subgraph = self.neo4j_client.retrieve_subgraph(keywords)

        # Bước 3: Trích tên bảng từ KG
        relevant_tables = self.neo4j_client.extract_tables_from_subgraph(subgraph)

        # Fallback: nếu KG không trả về bảng nào
        if not relevant_tables:
            print("⚠️ KG không tìm thấy bảng, dùng toàn bộ schema")
            relevant_tables = list(self.schema.keys())

        print(f"🔍 KG tìm thấy {len(relevant_tables)} bảng: {relevant_tables}")

        # Bước 4: Tạo DDL từ bảng liên quan
        ddl = generate_ddl_from_schema(self.schema, relevant_tables)
        business_concepts = self.neo4j_client.get_business_concept_semantic(question_vi)

        return relevant_tables, ddl, expanded_questions_text, business_concepts