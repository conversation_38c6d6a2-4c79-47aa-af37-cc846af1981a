# nl2sql/schema_linker.py (phi<PERSON>n bả<PERSON> kết hợp KG + DDL)
import json
import re
from typing import List, Tuple
from utils.text_utils import TextUtils
from utils.ddl_generator import generate_ddl_from_schema
from graph.neo4j_client import Neo4jClient  # <-- DÙNG KG
from utils.embedding import OllamaEmbedder  # <-- DÙNG VECTOR EMBEDDING

class SchemaLinker:
    def __init__(
        self,
        glossary_path: str = "data/glossary.csv",
        schema_path: str = "data/table_schema.json"
    ):
        self.glossary = TextUtils.load_glossary(glossary_path)
        with open(schema_path, "r", encoding="utf-8") as f:
            self.schema = json.load(f)
        self.neo4j_client = Neo4jClient()  # <-- KHỞI TẠO KG
        self.embedding = OllamaEmbedder()  # <-- DÙNG VECTOR EMBEDDING

    def _extract_keywords(self, text: str) -> List[str]:
        """Trí<PERSON> từ khóa và mở rộng bằng semantic search"""
        words = re.findall(r"\b\w+\b", text.lower())
        expanded = set()

        for w in words:
            if len(w) < 2:
                continue
            if w in ['en', '2en']:
                continue

            # Luôn thêm từ gốc
            expanded.add(w.lower())

            # Mở rộng bằng semantic search
            concept = self.neo4j_client.get_business_concept_semantic(w, threshold=0.5)
            if concept.lower() != w.lower():
                expanded.add(concept.lower())

        return list(expanded)

    def get_relevant_tables_and_ddl(self, question_en: str) -> Tuple[List[str], str]:
        """
        DÙNG KG ĐỂ XÁC ĐỊNH BẢNG LIÊN QUAN, sau đó tạo DDL.
        """
        # Bước 1: Trích từ khóa
        keywords = self._extract_keywords(question_en)

        # Bước 2: DÙNG KG ĐỂ TÌM SUBGRAPH
        subgraph = self.neo4j_client.retrieve_subgraph(keywords)

        # Bước 3: Trích tên bảng từ KG
        relevant_tables = self.neo4j_client.extract_tables_from_subgraph(subgraph)

        # Fallback: nếu KG không trả về bảng nào
        if not relevant_tables:
            print("⚠️ KG không tìm thấy bảng, dùng toàn bộ schema")
            relevant_tables = list(self.schema.keys())

        print(f"🔍 KG tìm thấy {len(relevant_tables)} bảng: {relevant_tables}")

        # Bước 4: Tạo DDL từ bảng liên quan
        ddl = generate_ddl_from_schema(self.schema, relevant_tables)

        return relevant_tables, ddl