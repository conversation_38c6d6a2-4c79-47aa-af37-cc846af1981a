# nl2sql/duckdb_sql_executor.py
from langchain_community.utilities import SQLDatabase
from langchain_community.llms import Ollama
from typing import Optional
import re

class DuckDBSQLExecutor:
    def __init__(
        self,
        db_uri: str,
        sql_llm_model: str = "nsql:7b",        # duckdb-nsql
        fix_llm_model: str = "llama3:8b",      # LLM sửa lỗi
        max_retries: int = 3
    ):
        self.db = SQLDatabase.from_uri(db_uri)
        self.sql_llm = Ollama(model=sql_llm_model, temperature=0)
        self.fix_llm = Ollama(model=fix_llm_model, temperature=0)
        self.max_retries = max_retries

    def _generate_sql(self, ddl: str, question: str) -> str:
        """Sinh SQL bằng duckdb-nsql"""
        prompt = f"""Provided this schema:

{ddl}

{question}
"""
        response = self.sql_llm.invoke(prompt)
        return self._clean_sql(response)

    def _clean_sql(self, sql: str) -> str:
        """<PERSON>àm sạch SQL (loại bỏ markdown, comment)"""
        # Loại bỏ ```sql ... ```
        sql = re.sub(r"```sql?\s*", "", sql)
        sql = re.sub(r"```", "", sql)
        # Loại bỏ dòng bắt đầu bằng --
        sql = "\n".join(line for line in sql.split("\n") if not line.strip().startswith("--"))
        return sql.strip()

    def _fix_sql(self, sql: str, error: str, ddl: str, question: str) -> str:
        """Dùng LLM mạnh sửa lỗi SQL"""
        prompt = f"""You are a PostgreSQL expert. Fix this SQL query.

Error: {error}

Original question: {question}

Schema:
{ddl}

Incorrect SQL:
{sql}

Fixed SQL (only SQL, no explanation):
"""
        response = self.fix_llm.invoke(prompt)
        return self._clean_sql(response)

    def run(self, ddl: str, question: str) -> dict:
        """
        Trả về dict: {"sql": "...", "result": "...", "success": True/False}
        """
        sql = self._generate_sql(ddl, question)
        last_error = None

        for attempt in range(self.max_retries + 1):
            try:
                # LangChain tự động kết nối và execute
                result = self.db.run(sql)
                return {
                    "sql": sql,
                    "result": result,
                    "success": True
                }
            except Exception as e:
                last_error = str(e)
                if attempt >= self.max_retries:
                    break
                # Sửa lỗi và thử lại
                sql = self._fix_sql(sql, last_error, ddl, question)

        return {
            "sql": sql,
            "result": f"❌ Lỗi sau {self.max_retries} lần thử: {last_error}",
            "success": False
        }