from langchain_community.llms import Ollama
from config.settings import Settings

class AnswerGenerator:
    def __init__(self, model: str = None):
        self.model = model or Settings.ANSWER_LLM_MODEL
        self.llm = Ollama(base_url="https://ollama.com", headers={'Authorization': 'Bearer ' + Settings.OLLAMA_API_KEY}, model=model, temperature=0)

    def generate_vi(self, question_vi: str, sql_result: str) -> str:
        prompt = f"""
        Bạn là trợ lý thông minh. Hãy trả lời câu hỏi sau bằng tiếng Việt một cách tự nhiên, ngắn gọn.

        Câu hỏi: {question_vi}
        Kết quả truy vấn: {sql_result}

        Tr<PERSON> lời:
        """
        return self.llm.invoke(prompt).strip()