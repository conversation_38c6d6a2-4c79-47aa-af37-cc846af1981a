# main.py
from translation.translator import Translator
from nl2sql.schema_linker import <PERSON>hema<PERSON>inker
from nl2sql.duckdb_sql_executor import DuckDBS<PERSON>Executor
from response.answer_generator import AnswerGenerator
from utils.text_utils import TextUtils
from config.settings import Settings
import os
from core.schema_extractor import extract_schema_to_json
import argparse

def build_db_uri():
  return (
      f"postgresql+psycopg2://{Settings.ODOO_DB_USER}:{Settings.ODOO_DB_PASSWORD}"
      f"@{Settings.ODOO_DB_HOST}/{Settings.ODOO_DB_NAME}"
  )

SCHEMA_PATH = "data/table_schema.json"

def ensure_schema_exists():
  if not os.path.exists(SCHEMA_PATH):
      print(f"🔄 Schema chưa tồn tại. Đang tạo: {SCHEMA_PATH}")
      extract_schema_to_json(SCHEMA_PATH)
  else:
      print(f"✅ Đã tìm thấy schema: {SCHEMA_PATH}")

def main():
  parser = argparse.ArgumentParser()
  parser.add_argument("--refresh-schema", action="store_true",
                      help="Force update schema from Odoo DB")
  parser.add_argument("--question", type=str, default="Doanh thu từ khách hàng ABC tháng 10 năm 2024?",
                      help="Câu hỏi của người dùng")
  parser.add_argument("--slm", type=str, default="duckdb-nsql:latest",
                      help="SQL LLM Model")
  parser.add_argument("--flm", type=str, default="sqlcoder:7b",
                      help="Fix LLM Model")
  parser.add_argument("--alm", type=str, default="gemma3:1b",
                      help="Answer LLM Model")
  parser.add_argument("--tm", type=str, default="VietAI/envit5-translation",
                      help="Translation Model")
  args = parser.parse_args()
  if args.refresh_schema or not os.path.exists(SCHEMA_PATH):
    print("🔄 Đang cập nhật schema từ Odoo DB...")
    extract_schema_to_json(SCHEMA_PATH)
  question_vi = args.question

  # 1. Chuẩn hóa & dịch
  glossary = TextUtils.load_glossary("data/glossary.csv")
  question_vi_norm = TextUtils.normalize_business_terms(question_vi, glossary)
  translator = Translator(model_name=args.tm)
  question_en = translator.vi_to_en(question_vi_norm)

  # 2. Lấy DDL
  linker = SchemaLinker()
  _, ddl = linker.get_relevant_tables_and_ddl(question_en)
  print("📜 DDL:\n", ddl)
  # 3. Execute với retry
  # executor = DuckDBSQLExecutor(
  #     db_uri=build_db_uri(),
  #     sql_llm_model= args.slm or "duckdb-nsql:7b",
  #     fix_llm_model= args.flm or "llama3:8b",  # hoặc "gpt-4o" nếu có OpenAI API
  #     max_retries=2
  # )
  # result = executor.run(ddl, question_en)

  # print("✅ SQL cuối cùng:")
  # print(result["sql"])
  # print("\n📊 Kết quả:")
  # print(result["result"])

  # # 4. Trả lời tiếng Việt (nếu thành công)
  # if result["success"]:
  #     answer_gen = AnswerGenerator(model=args.alm or "gemma3:1b")
  #     final_answer = answer_gen.generate_vi(question_vi, result["result"])
  #     print("\n💬 Trả lời người dùng:")
  #     print(final_answer)

if __name__ == "__main__":
  main()



"""
CREATE TABLE account_account (
    display_mapping_tab BOOLEAN,
    code VARCHAR,
    currency_id BIGINT,
    placeholder_code VARCHAR,
    current_balance DOUBLE,
    message_needaction_counter BIGINT,
    message_has_error BOOLEAN,
    message_has_error_counter BIGINT,
    message_has_sms_error BOOLEAN,
    account_type VARCHAR,
    opening_credit DOUBLE,
    opening_balance DOUBLE,
    related_taxes_amount BIGINT,
    non_trade BOOLEAN,
    root_id BIGINT,
    group_id BIGINT,
    display_name VARCHAR,
    used BOOLEAN,
    opening_debit DOUBLE,
    message_is_follower BOOLEAN,
    has_message BOOLEAN,
    message_needaction BOOLEAN,
    message_attachment_count BIGINT,
    name VARCHAR,
    company_currency_id BIGINT,
    company_fiscal_country_code VARCHAR,
    code_store VARCHAR,
    deprecated BOOLEAN,
    include_initial_balance BOOLEAN,
    internal_group VARCHAR,
    reconcile BOOLEAN,
    note VARCHAR
);

CREATE TABLE account_move_line (
    price_unit DOUBLE,
    discount DOUBLE,
    cogs_origin_id BIGINT,
    purchase_line_id BIGINT,
    purchase_order_id BIGINT,
    payment_id BIGINT,
    is_account_reconcile BOOLEAN,
    asset_category_id BIGINT,
    asset_start_date DATE,
    asset_end_date DATE,
    asset_mrr DOUBLE,
    followup_line_id BIGINT,
    followup_date DATE,
    result DOUBLE,
    is_same_currency BOOLEAN,
    tax_tag_invert BOOLEAN,
    discount_balance DOUBLE,
    is_downpayment BOOLEAN,
    account_id BIGINT,
    debit DOUBLE,
    credit DOUBLE,
    balance DOUBLE,
    cumulated_balance DOUBLE,
    is_imported BOOLEAN,
    tax_base_amount DOUBLE,
    account_type VARCHAR,
    account_internal_group VARCHAR,
    account_root_id BIGINT,
    display_type VARCHAR,
    product_uom_category_id BIGINT,
    date_maturity DATE,
    discount_allocation_dirty BOOLEAN,
    discount_date DATE,
    discount_amount_currency DOUBLE,
    is_refund BOOLEAN,
    display_name VARCHAR,
    journal_group_id BIGINT,
    parent_state VARCHAR,
    ref VARCHAR,
    sequence BIGINT,
    move_type VARCHAR,
    name VARCHAR,
    partner_id BIGINT,
    reconcile_model_id BIGINT,
    statement_line_id BIGINT,
    amount_residual DOUBLE,
    matching_number VARCHAR,
    product_category_id BIGINT,
    quantity DOUBLE,
    price_subtotal DOUBLE,
    price_total DOUBLE,
    tax_calculation_rounding_method VARCHAR,
    payment_date DATE,
    currency_id BIGINT,
    analytic_precision BIGINT,
    move_id BIGINT,
    journal_id BIGINT,
    company_id BIGINT,
    company_currency_id BIGINT,
    date DATE,
    invoice_date DATE,
    is_storno BOOLEAN,
    currency_rate DOUBLE,
    epd_dirty BOOLEAN,
    amount_currency DOUBLE,
    amount_residual_currency DOUBLE,
    reconciled BOOLEAN,
    full_reconcile_id BIGINT,
    move_name VARCHAR,
    statement_id BIGINT,
    group_tax_id BIGINT,
    tax_line_id BIGINT,
    tax_group_id BIGINT,
    tax_repartition_line_id BIGINT,
    product_id BIGINT,
    product_uom_id BIGINT
);

CREATE TABLE account_move (
    amount_total DOUBLE,
    wip_production_count BIGINT,
    source_id BIGINT,
    stock_move_id BIGINT,
    medium_id BIGINT,
    l10n_vn_e_invoice_number VARCHAR,
    type_name VARCHAR,
    campaign_id BIGINT,
    company_currency_id BIGINT,
    currency_id BIGINT,
    amount_residual_signed DOUBLE,
    invoice_incoterm_id BIGINT,
    bank_partner_id BIGINT,
    display_inactive_currency_warning BOOLEAN,
    has_reconciled_entries BOOLEAN,
    access_url VARCHAR,
    sale_order_count BIGINT,
    activity_type_icon VARCHAR,
    highest_name VARCHAR,
    message_needaction BOOLEAN,
    auto_post VARCHAR,
    auto_post_until DATE,
    auto_post_origin_id BIGINT,
    hide_post_button BOOLEAN,
    checked BOOLEAN,
    country_code VARCHAR,
    company_price_include VARCHAR,
    secure_sequence_number BIGINT,
    inalterable_hash VARCHAR,
    invoice_date DATE,
    invoice_date_due DATE,
    delivery_date DATE,
    commercial_partner_id BIGINT,
    partner_shipping_id BIGINT,
    fiscal_position_id BIGINT,
    display_qr_code BOOLEAN,
    invoice_has_outstanding BOOLEAN,
    invoice_currency_rate DOUBLE,
    direction_sign BIGINT,
    amount_total_words VARCHAR,
    invoice_partner_display_name VARCHAR,
    is_manually_modified BOOLEAN,
    is_move_sent BOOLEAN,
    is_being_sent BOOLEAN,
    purchase_order_count BIGINT,
    incoterm_location VARCHAR,
    invoice_cash_rounding_id BIGINT,
    show_update_fpos BOOLEAN,
    abnormal_amount_warning VARCHAR,
    invoice_filter_type_domain VARCHAR,
    message_has_error BOOLEAN,
    message_has_error_counter BIGINT,
    message_has_sms_error BOOLEAN,
    message_main_attachment_id BIGINT,
    access_token VARCHAR,
    name VARCHAR,
    ref VARCHAR,
    state VARCHAR,
    move_type VARCHAR,
    journal_group_id BIGINT,
    origin_payment_id BIGINT,
    payment_count BIGINT,
    statement_line_id BIGINT,
    statement_id BIGINT,
    tax_cash_basis_rec_id BIGINT,
    posted_before BOOLEAN,
    made_sequence_gap BOOLEAN,
    show_name_warning BOOLEAN,
    restrict_mode_hash_table BOOLEAN,
    secured BOOLEAN,
    show_delivery_date BOOLEAN,
    invoice_payment_term_id BIGINT,
    needed_terms_dirty BOOLEAN,
    tax_calculation_rounding_method VARCHAR,
    partner_id BIGINT,
    partner_bank_id BIGINT,
    payment_reference VARCHAR,
    qr_code_method VARCHAR,
    preferred_payment_method_line_id BIGINT,
    amount_untaxed DOUBLE,
    amount_tax DOUBLE,
    amount_untaxed_signed DOUBLE,
    amount_untaxed_in_currency_signed DOUBLE,
    amount_tax_signed DOUBLE,
    amount_total_signed DOUBLE,
    amount_total_in_currency_signed DOUBLE,
    payment_state VARCHAR,
    status_in_payment VARCHAR,
    reversed_entry_id BIGINT,
    invoice_vendor_bill_id BIGINT,
    invoice_source_email VARCHAR,
    quick_edit_mode BOOLEAN,
    quick_edit_total_amount DOUBLE,
    move_sent_values VARCHAR,
    invoice_user_id BIGINT,
    user_id BIGINT,
    invoice_origin VARCHAR,
    invoice_pdf_report_id BIGINT,
    tax_lock_date_message VARCHAR,
    tax_country_id BIGINT,
    tax_country_code VARCHAR,
    show_reset_to_draft_button BOOLEAN,
    partner_credit_warning VARCHAR,
    partner_credit DOUBLE,
    need_cancel_request BOOLEAN,
    show_payment_term_details BOOLEAN,
    show_discount_details BOOLEAN,
    next_payment_date DATE,
    sequence_number BIGINT,
    activity_state VARCHAR,
    message_is_follower BOOLEAN,
    has_message BOOLEAN,
    message_attachment_count BIGINT,
    access_warning VARCHAR,
    is_storno BOOLEAN,
    journal_id BIGINT,
    company_id BIGINT,
    tax_cash_basis_origin_move_id BIGINT,
    always_tax_exigible BOOLEAN,
    amount_residual DOUBLE,
    ubl_cii_xml_id BIGINT,
    transaction_count BIGINT,
    amount_paid DOUBLE,
    purchase_order_name VARCHAR,
    is_purchase_matched BOOLEAN,
    team_id BIGINT,
    activity_exception_decoration VARCHAR,
    activity_exception_icon VARCHAR,
    date DATE,
    abnormal_date_warning VARCHAR,
    display_name VARCHAR,
    purchase_vendor_bill_id BIGINT,
    purchase_id BIGINT,
    sequence_prefix VARCHAR,
    activity_user_id BIGINT,
    activity_type_id BIGINT,
    activity_date_deadline DATE,
    my_activity_date_deadline DATE,
    activity_summary VARCHAR,
    message_needaction_counter BIGINT,
    website_id BIGINT
);
"""