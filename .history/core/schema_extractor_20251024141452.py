# core/schema_extractor.py
import psycopg2
import json
from config.settings import Settings

# Ánh xạ kiểu <PERSON> → ki<PERSON><PERSON> chu<PERSON>n (dùng cho DDL)
ODOO_TYPE_MAP = {
    "integer": "integer",
    "float": "float",
    "monetary": "monetary",
    "char": "char",
    "text": "text",
    "boolean": "boolean",
    "date": "date",
    "datetime": "datetime",
    "selection": "selection",
    "many2one": "many2one",
    # Bỏ qua many2many, one2many vì không dùng trực tiếp trong SQL
}

def extract_schema_to_json(output_path: str = "data/table_schema.json"):
    """
    Trích xuất schema từ Odoo DB và lưu thành JSON.
    Chỉ lấy các bảng có trong ir_model và kiểu dữ liệu hợp lệ.
    """
    conn = psycopg2.connect(
        host=Settings.ODOO_DB_HOST,
        database=Settings.ODOO_DB_NAME,
        user=Settings.ODOO_DB_USER,
        password=Settings.ODOO_DB_PASSWORD
    )
    cur = conn.cursor()

    # Lấy danh sách model hợp lệ (bỏ model trừu tượng)
    cur.execute("""
        SELECT model FROM ir_model;
    """)
    valid_models = {row[0] for row in cur.fetchall()}

    # Lấy fields
    cur.execute("""
        SELECT model, name, ttype
        FROM ir_model_fields
        WHERE model = ANY(%s)
          AND ttype = ANY(%s)
          AND name NOT IN ('create_uid', 'write_uid', 'create_date', 'write_date', 'id');
    """, (list(valid_models), list(ODOO_TYPE_MAP.keys())))

    schema = {}
    for model, field, ttype in cur.fetchall():
        table_name = model.replace(".", "_")  # Odoo model → table name
        if table_name not in schema:
            schema[table_name] = {"columns": {}}
        schema[table_name]["columns"][field] = {"type": ttype}
        if ttype == "selection":
            #Get selection fields
            cur.execute("""
                 select
                    imf.model "odoo_model",
                    imf."name" "field_name",
                    imfs.value "key",
                    imfs."name" "label"
                from
                    ir_model_fields_selection imfs
                inner join ir_model_fields imf on
                    imfs.field_id = imf.id
                where
                    imf.model = %s
                    and imf."name" = %s;
            """, (model, field))
            selections = cur.fetchall()
            schema[table_name]["columns"][field]["selection"] = selections

    conn.close()

    # Lưu file
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(schema, f, ensure_ascii=False, indent=2)

    print(f"✅ Đã lưu schema vào: {output_path}")
    print(f"📊 Số bảng trích xuất: {len(schema)}")

if __name__ == "__main__":
    extract_schema_to_json()