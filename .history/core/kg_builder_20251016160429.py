# core/kg_builder.py
import psycopg2
from neo4j import GraphDatabase
from config.settings import Settings
import logging
from tqdm import tqdm

class KGBuild:
    ODOO_FIELD_TYPES = (
        "char", "text", "integer", "float", "monetary",
        "date", "datetime", "boolean", "selection", "many2one"
    )

    def __init__(self):
        self.odoo_conn = psycopg2.connect(
            host=Settings.ODOO_DB_HOST,
            database=Settings.ODOO_DB_NAME,
            user=Settings.ODOO_DB_USER,
            password=Settings.ODOO_DB_PASSWORD
        )
        self.neo4j_driver = GraphDatabase.driver(
            Settings.NEO4J_URI,
            auth=(Settings.NEO4J_USER, Settings.NEO4J_PASSWORD)
        )

    def build(self):
        self._clear_graph()
        self._create_models()
        self._create_fields()
        self._create_selections()
        self._create_glossary_terms()
        self._create_vector_index()
        logging.info("✅ KG đã được xây dựng xong!")

    def _clear_graph(self):
        with self.neo4j_driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")

    def _extract_label(self, value):
        if not value:
            return ""
        if isinstance(value, dict):
            parts = []
            if "vi_VN" in value and value["vi_VN"]:
                parts.append(str(value["vi_VN"]))
            if "en_US" in value and value["en_US"]:
                parts.append(str(value["en_US"]))
            if not parts:
                parts = [str(v) for v in value.values()
                        if v and isinstance(v, str) and v.strip()]
            if len(parts) == 0:
                return ""
            elif len(parts) == 1:
                return parts[0]
            else:
                return f"{parts[0]} ({parts[1]})"
        return str(value).strip()

    def _create_models(self):
        cur = self.odoo_conn.cursor()
        cur.execute("""
            SELECT model, name
            FROM ir_model
            WHERE model NOT LIKE 'ir.%'
              AND model NOT LIKE 'base_%'
              AND model NOT LIKE '\_%' ESCAPE '\'
        """)
        models = cur.fetchall()  # ✅ LẤY DỮ LIỆU MỘT LẦN
        print(f"🔍 Sẽ xử lý {len(models)} model")

        created = 0
        with self.neo4j_driver.session() as session:
            for i, (odoo_model, name) in enumerate(tqdm(models, desc="Creating models", unit="model")):
                label = self._extract_label(name)
                if label.strip():
                    session.run("""
                        CREATE (:Model {
                            odoo_model: $odoo_model,
                            rdfs__label: $label
                        })
                    """, odoo_model=odoo_model, label=label)
                    created += 1
        print(f"✅ Đã tạo {created}/{len(models)} node Model")

    def _create_fields(self):
        cur = self.odoo_conn.cursor()
        cur.execute("""
            SELECT model, name, field_description, ttype
            FROM ir_model_fields
            WHERE ttype IN %s
              AND name NOT IN ('id', 'create_uid', 'write_uid', 'create_date', 'write_date')
        """, (self.ODOO_FIELD_TYPES,))
        fields = cur.fetchall()  # ✅ LẤY DỮ LIỆU MỘT LẦN
        print(f"🔍 Sẽ xử lý {len(fields)} field")

        created = 0
        with self.neo4j_driver.session() as session:
            for i, (odoo_model, field_name, desc, ttype) in enumerate(tqdm(fields, desc="Creating fields", unit="field")):
                label = self._extract_label(desc) or field_name
                if label.strip():
                    session.run("""
                        MATCH (m:Model {odoo_model: $odoo_model})
                        CREATE (m)<-[:BELONGS_TO]-(f:Field {
                            odoo_model: $odoo_model,
                            odoo_field: $field_name,
                            rdfs__label: $label,
                            odoo_type: $ttype
                        })
                    """,
                    odoo_model=odoo_model,
                    field_name=field_name,
                    label=label,
                    ttype=ttype)
                    created += 1
        print(f"✅ Đã tạo {created}/{len(fields)} node Field")

    def _create_selections(self):
        cur = self.odoo_conn.cursor()
        cur.execute("""
        select
            imf.model "odoo_model",
            imf."name" "field_name",
            imfs.value "key",
            imfs."name" "label"
        from
            ir_model_fields_selection imfs
        inner join ir_model_fields imf on
            imfs.field_id = imf.id
        """)
        selections = cur.fetchall()  # ✅ LẤY DỮ LIỆU MỘT LẦN
        print(f"🔍 Sẽ xử lý {len(selections)} selection value")

        created = 0
        with self.neo4j_driver.session() as session:
            for i, (odoo_model, field_name, key, label) in enumerate(tqdm(selections, desc="Creating selections", unit="selection")):
                clean_label = self._extract_label(label)
                # print(f"clean_label: {clean_label} | odoo_model: {odoo_model} | field_name: {field_name} | key: {key}")
                if clean_label.strip():
                    session.run("""
                        MATCH (f:Field {odoo_model: $odoo_model, odoo_field: $field_name})
                        CREATE (f)<-[:VALUE_OF]-(s:SelectionValue {
                            odoo__key: $key,
                            rdfs__label: $clean_label
                        })
                    """,
                    odoo_model=odoo_model,
                    field_name=field_name,
                    key=key,
                    clean_label=clean_label)
                    created += 1
        print(f"✅ Đã tạo {created}/{len(selections)} node SelectionValue")

    def _create_glossary_terms(self):
        from utils.text_utils import TextUtils
        glossary = TextUtils.load_glossary("data/glossary.csv")
        print(f"🔍 Sẽ tạo {len(glossary)} Term từ glossary")

        created = 0
        with self.neo4j_driver.session() as session:
            for i, (term, concept) in enumerate(tqdm(glossary.items(), desc="Creating glossary terms", unit="term")):
                if term.strip() and concept.strip():
                    session.run("""
                        CREATE (:Term {
                            name: $term,
                            concept: $concept
                        })
                    """, term=term, concept=concept)
                    created += 1
        print(f"✅ Đã tạo {created} node Term")

    def _create_vector_index(self):
        """Tạo vector index (hỗ trợ đa label)"""
        try:
            with self.neo4j_driver.session() as session:
                # Kiểm tra số node
                total_result = session.run("MATCH (n) RETURN count(n) AS total")
                total = total_result.single()["total"]
                print(f"📊 Tổng số node trong KG: {total}")

                labeled_result = session.run("""
                    MATCH (n)
                    WHERE n.rdfs__label IS NOT NULL AND n.rdfs__label <> ""
                    RETURN count(n) AS labeled
                """)
                labeled = labeled_result.single()["labeled"]
                print(f"🏷️ Số node có rdfs__label: {labeled}")

                if labeled == 0:
                    print("❌ KHÔNG CÓ NODE NÀO CÓ LABEL → DỪNG TẠO EMBEDDING")
                    return

                self._create_vector_index_v2(session)
        except Exception as e:
            print(f"⚠️ Lỗi tổng quát khi tạo vector index: {e}")

    def _create_vector_index_v2(self, session):
        """Tạo embedding và vector index cho từng label"""
        labels = ["Model", "Field", "Term", "SelectionValue"]
        # labels = ["Field", "Term", "SelectionValue"]
        # labels = ["Model"]
        from utils.embedding import OllamaEmbedder
        embedder = OllamaEmbedder()

        for label in labels:
            print(f"\n🔄 Xử lý label: {label}")
            try:
                # Lấy node có rdfs__label
                result = session.run(f"""
                    MATCH (n:`{label}`)
                    WHERE n.rdfs__label IS NOT NULL AND n.rdfs__label <> ""
                    RETURN elementId(n) AS elem_id, n.rdfs__label AS label_text
                """)
                nodes = [(record["elem_id"], record["label_text"]) for record in result]
                print(f"   📌 Tìm thấy {len(nodes)} node")

                if not nodes:
                    continue

                # Tạo embedding
                for i, (elem_id, label_text) in enumerate(tqdm(nodes, desc="Embedding nodes", unit="node")):
                    try:
                        embedding = embedder.embed(label_text)
                        # print(f"✅ Embedding {label_text}: {embedding}")
                        session.run("""
                            MATCH (n) WHERE elementId(n) = $elem_id
                            SET n.embedding = $embedding
                        """, elem_id=elem_id, embedding=embedding)
                    except Exception as e:
                        print(f"   ❌ Lỗi embedding: {e}")

                # Tạo vector index
                session.run(f"""
                    CREATE VECTOR INDEX node_embedding_{label} IF NOT EXISTS
                    FOR (n:`{label}`)
                    ON (n.embedding)
                    OPTIONS {{
                      indexConfig: {{
                        `vector.dimensions`: 768,
                        `vector.similarity_function`: 'cosine'
                      }}
                    }}
                """)
                print(f"   ✅ Đã tạo vector index cho `{label}`")

            except Exception as e:
                print(f"⚠️ Lỗi xử lý label `{label}`: {e}")

    def close(self):
        self.odoo_conn.close()
        self.neo4j_driver.close()