# core/schema_extractor.py
import psycopg2
import json
from config.settings import Settings
from tqdm import tqdm

# Ánh xạ kiểu <PERSON> → ki<PERSON><PERSON>u<PERSON> (dùng cho DDL)
ODOO_TYPE_MAP = {
    "integer": "integer",
    "float": "float",
    "monetary": "monetary",
    "char": "char",
    "text": "text",
    "boolean": "boolean",
    "date": "date",
    "datetime": "datetime",
    "selection": "selection",
    "many2one": "many2one",
    # Bỏ qua many2many, one2many vì không dùng trực tiếp trong SQL
}

def extract_schema_to_json(output_path: str = "data/table_schema.json"):
    """
    Trích xuất schema từ Odoo DB và lưu thành JSON.
    Chỉ lấy các bảng có trong ir_model và kiểu dữ liệu hợp lệ.
    """
    conn = psycopg2.connect(
        host=Settings.ODOO_DB_HOST,
        database=Settings.ODOO_DB_NAME,
        user=Settings.ODOO_DB_USER,
        password=Settings.ODOO_DB_PASSWORD
    )
    cur = conn.cursor()

    # Lấy danh sách model hợp lệ (bỏ model trừu tượng)
    cur.execute("""
        SELECT model FROM ir_model
        where model not like 'ir%'
            and model not like 'base_%'
            and model not like '\_%' escape '\'
            and model not like '%mail%'
            and model not like '%mixin%'
            and model not like '%website%'
            and model not like '%web%'
            and model not like '%theme%'
            and model not like '%mail%'
            and model not like '%%xml%%'
            and model not like '%%wizard%%'
            and model not like '%%layout%%'
            and model not like '%%sms%%'
            and model not like '%%spreadsheet%%'
            and model not like '%%template%%'
            and model not like '%%report%%'
            and model not like '%%followup%%'
            and model not like '%%config%%'
            and model not like '%%auto%%'
            and model not like '%%print%%'
            and model not like '%%queue%%'
            and model not like '%%ecommerce%%package%%'
            and model not like '%%password%%'
            and model not like '%%image%%'
            and model not like '%%audit%%'
            and model not like '%%iap%%'
            and model not like '%%token%%'
            and model not like '%%iap%%'
            and model not like '%%key%%'
            and model not like '%%auth%%'
            and model not like '%%test%%'
            and model not like '%%discuss%%'
            and model not like '%%quotation%%';
    """)
    valid_models = {row[0] for row in cur.fetchall()}

    # Lấy fields
    cur.execute("""
        SELECT model, name, ttype
        FROM ir_model_fields
        WHERE model = ANY(%s)
            AND ttype = ANY(%s)
            AND name NOT IN ('create_uid', 'write_uid', 'create_date', 'write_date', 'id');
    """, (list(valid_models), list(ODOO_TYPE_MAP.keys())))

    schema = {}
    fields = cur.fetchall()
    for model, field, ttype in tqdm(fields, desc="Extracting fields", unit="field", unit_scale=1, colour="green"):
        table_name = model.replace(".", "_")  # Odoo model → table name
        if table_name not in schema:
            schema[table_name] = {"columns": {}}
        schema[table_name]["columns"][field] = {"type": ttype}
        schema[table_name]["columns"][field]["foreign_keys"] = []
        if ttype == "selection":
            #Get selection fields
            cur.execute("""
                 select
                    imfs.value "key"
                from
                    ir_model_fields_selection imfs
                inner join ir_model_fields imf on
                    imfs.field_id = imf.id
                where
                    imf.model = %s
                    and imf."name" = %s;
            """, (model, field))
            selections = cur.fetchall()
            schema[table_name]["columns"][field]["selection"] = [selection[0] for selection in selections]

        if ttype in ("many2many", "one2many"):
            #Get relation fields
            cur.execute("""
                select
                    imf.relation
                from
                    ir_model_fields imf
                where
                    imf.model = %s
                    and imf."name" = %s;
            """, (model, field))
            relation = cur.fetchone()
            table_relation_name = relation[0].replace(".", "_")
            schema[table_name]["columns"][field]["relation_table"] = table_relation_name
        if ttype == "many2one":
            cur.execute("""
                select
                    tc.table_name,
                    kcu.column_name,
                    ccu.table_name as foreign_table_name,
                    ccu.column_name as foreign_column_name
                from
                    information_schema.table_constraints as tc
                join information_schema.key_column_usage as kcu
                        on
                    tc.constraint_name = kcu.constraint_name
                    and tc.table_schema = kcu.table_schema
                join information_schema.constraint_column_usage as ccu
                        on
                    ccu.constraint_name = tc.constraint_name
                    and ccu.table_schema = tc.table_schema
                where
                    tc.constraint_type = 'FOREIGN KEY'
                    and tc.table_name = %s
                    and kcu.column_name = %s;
            """, (table_name, field))
            fk_info = cur.fetchall()
            for fk in fk_info:
                schema[table_name]['columns'][field]["foreign_keys"].append({
                    "foreign_table": fk[2],
                    "foreign_column": fk[3]
                })

    conn.close()

    # Lưu file
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(schema, f, ensure_ascii=False, indent=2)

    print(f"✅ Đã lưu schema vào: {output_path}")
    print(f"📊 Số bảng trích xuất: {len(schema)}")

if __name__ == "__main__":
    extract_schema_to_json()