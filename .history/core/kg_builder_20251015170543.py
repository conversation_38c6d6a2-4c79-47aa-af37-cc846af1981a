# core/kg_builder.py
import psycopg2
from neo4j import GraphDatabase
from config.settings import Settings
import logging

class KGBuild:
    # <PERSON><PERSON><PERSON> kiểu trường Odoo cần trích xuất
    ODOO_FIELD_TYPES = (
        "char", "text", "integer", "float", "monetary",
        "date", "datetime", "boolean", "selection", "many2one"
    )

    def __init__(self):
        # Kết nối Odoo DB
        self.odoo_conn = psycopg2.connect(
            host=Settings.ODOO_DB_HOST,
            database=Settings.ODOO_DB_NAME,
            user=Settings.ODOO_DB_USER,
            password=Settings.ODOO_DB_PASSWORD
        )
        # Kết nối Neo4j
        self.neo4j_driver = GraphDatabase.driver(
            Settings.NEO4J_URI,
            auth=(Settings.NEO4J_USER, Settings.NEO4J_PASSWORD)
        )

    def build(self):
        self._clear_graph()
        self._create_models()
        self._create_fields()
        self._create_selections()
        self._create_glossary_terms()
        self._create_vector_index()
        logging.info("✅ KG đã được xây dựng xong!")

    def _clear_graph(self):
        with self.neo4j_driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")

    def _extract_label(self, value):
        """
        Xử lý giá trị đa ngôn ngữ bằng cách concat.
        Ví dụ: {"vi_VN": "Hóa đơn", "en_US": "Invoice"} → "Hóa đơn (Invoice)"
        """
        if isinstance(value, dict):
            parts = []
            # Ưu tiên tiếng Việt
            if "vi_VN" in value and value["vi_VN"]:
                parts.append(str(value["vi_VN"]))
            # Rồi đến tiếng Anh
            if "en_US" in value and value["en_US"]:
                parts.append(str(value["en_US"]))
            
            # Nếu không có vi_VN/en_US, lấy tất cả giá trị hợp lệ
            if not parts:
                parts = [str(v) for v in value.values() 
                        if v and isinstance(v, str) and v.strip()]
            
            # Ghép kết quả
            if len(parts) == 0:
                return ""
            elif len(parts) == 1:
                return parts[0]
            else:
                return f"{parts[0]} ({parts[1]})"
        
        # Xử lý giá trị None hoặc rỗng
        return str(value).strip() if value else ""

    def _create_models(self):
        """Tạo node Model với odoo_model = 'account.move'"""
        cur = self.odoo_conn.cursor()
        cur.execute("""
            SELECT model, name 
            FROM ir_model 
            WHERE model NOT LIKE 'ir.%' 
              AND model NOT LIKE 'base_%'
              AND model NOT LIKE '\_%' ESCAPE '\'
        """)
        with self.neo4j_driver.session() as session:
            for odoo_model, name in cur.fetchall():
                label = self._extract_label(name)
                session.run("""
                    CREATE (:Model {
                        odoo_model: $odoo_model,
                        rdfs__label: $label
                    })
                """, odoo_model=odoo_model, label=label)

    def _create_fields(self):
        """Tạo node Field với odoo_model và odoo_field"""
        cur = self.odoo_conn.cursor()
        cur.execute("""
            SELECT model, name, field_description, ttype
            FROM ir_model_fields
            WHERE ttype IN %s
              AND name NOT IN ('id', 'create_uid', 'write_uid', 'create_date', 'write_date')
        """, (self.ODOO_FIELD_TYPES,))
        
        with self.neo4j_driver.session() as session:
            for odoo_model, field_name, desc, ttype in cur.fetchall():
                label = self._extract_label(desc) or field_name
                session.run("""
                    MATCH (m:Model {odoo_model: $odoo_model})
                    CREATE (m)<-[:BELONGS_TO]-(f:Field {
                        odoo_model: $odoo_model,
                        odoo_field: $field_name,
                        rdfs__label: $label,
                        odoo_type: $ttype
                    })
                """, 
                odoo_model=odoo_model,
                field_name=field_name,
                label=label,
                ttype=ttype)

    def _create_selections(self):
        """Tạo SelectionValue từ ir_model_fields_selection"""
        cur = self.odoo_conn.cursor()
        cur.execute("""
        select
            imf.model "model",
            imf."name" "field",
            imfs.value "value",
            imfs."name" "name"
        from
            ir_model_fields_selection imfs
        inner join ir_model_fields imf on
            imfs.field_id = imf.id
        """)
        with self.neo4j_driver.session() as session:
            for odoo_model, field_name, key, label in cur.fetchall():
                clean_label = self._extract_label(label)
                session.run("""
                    MATCH (f:Field {odoo_model: $odoo_model, odoo_field: $field_name})
                    CREATE (f)<-[:VALUE_OF]-(s:SelectionValue {
                        odoo__key: $key,
                        rdfs__label: $clean_label
                    })
                """, 
                odoo_model=odoo_model,
                field_name=field_name,
                key=key,
                clean_label=clean_label)

    def _create_glossary_terms(self):
        """Tạo Term từ glossary.csv"""
        from utils.text_utils import TextUtils
        glossary = TextUtils.load_glossary("data/glossary.csv")
        with self.neo4j_driver.session() as session:
            for term, concept in glossary.items():
                session.run("""
                    CREATE (:Term {
                        name: $term,
                        concept: $concept
                    })
                """, term=term, concept=concept)

    def _create_vector_index(self):
        """Tạo vector index cho semantic search (Neo4j >= 5.11)"""
        try:
            with self.neo4j_driver.session() as session:
                # Lấy tất cả node có rdfs__label
                result = session.run("""
                    MATCH (n)
                    WHERE n.rdfs__label IS NOT NULL AND n.rdfs__label <> ""
                    RETURN id(n) AS id, n.rdfs__label AS label
                """)
                nodes = [(record["id"], record["label"]) for record in result]

                if not nodes:
                    print("⚠️ Không có node nào để tạo embedding")
                    return

                # Tạo embedding
                from utils.embedding import OllamaEmbedder
                embedder = OllamaEmbedder()

                print(f"🔄 Đang tạo embedding cho {len(nodes)} node...")
                for i, (node_id, label) in enumerate(nodes):
                    if i % 50 == 0:
                        print(f"   Đã xử lý {i}/{len(nodes)} node")
                    try:
                        embedding = embedder.embed(label)
                        session.run("""
                            MATCH (n) WHERE id(n) = $id
                            SET n.embedding = $embedding
                        """, id=node_id, embedding=embedding)
                    except Exception as e:
                        print(f"   ❌ Lỗi embedding node {node_id}: {e}")

                # Tạo vector index
                session.run("""
                    CREATE VECTOR INDEX node_embedding IF NOT EXISTS
                    FOR (n) ON (n.embedding)
                    OPTIONS {
                      indexConfig: {
                        `vector.dimensions`: 768,
                        `vector.similarity_function`: 'cosine'
                      }
                    }
                """)
            print("✅ Vector index đã được tạo!")
        except Exception as e:
            print(f"⚠️ Không tạo được vector index: {e}")
            print("💡 Đảm bảo bạn dùng Neo4j >= 5.11 và đã cài Ollama")

    def _create_vector_index_v2(self, session):
        """Tạo vector index cho Neo4j >= 5.18 (dùng elementId)"""
        # Lấy node với elementId
        result = session.run("""
            MATCH (n)
            WHERE n.rdfs__label IS NOT NULL AND n.rdfs__label <> ""
            RETURN elementId(n) AS elem_id, n.rdfs__label AS label
        """)
        nodes = [(record["elem_id"], record["label"]) for record in result]

        if not nodes:
            print("⚠️ Không có node nào để tạo embedding")
            return

        # Tạo embedding
        from utils.embedding import OllamaEmbedder
        embedder = OllamaEmbedder()

        print(f"🔄 Đang tạo embedding cho {len(nodes)} node...")
        for i, (elem_id, label) in enumerate(nodes):
            if i % 50 == 0:
                print(f"   Đã xử lý {i}/{len(nodes)} node")
            try:
                embedding = embedder.embed(label)
                session.run("""
                    MATCH (n) WHERE elementId(n) = $elem_id
                    SET n.embedding = $embedding
                """, elem_id=elem_id, embedding=embedding)
            except Exception as e:
                print(f"   ❌ Lỗi embedding node {elem_id}: {e}")

        # Tạo vector index (cú pháp Neo4j 5.18+)
        session.run("""
            CREATE VECTOR INDEX node_embedding IF NOT EXISTS
            FOR n IN NODES
            ON n.embedding
            OPTIONS {
            indexConfig: {
                `vector.dimensions`: 768,
                `vector.similarity_function`: 'cosine'
            }
            }
        """)
        print("✅ Vector index đã được tạo!")

    def close(self):
        self.odoo_conn.close()
        self.neo4j_driver.close()