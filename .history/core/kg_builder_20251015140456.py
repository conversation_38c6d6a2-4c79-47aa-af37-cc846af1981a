# core/kg_builder.py
import psycopg2
from neo4j import GraphDatabase
from config.settings import Settings
import logging

class KGBuild:
    def __init__(self):
        # <PERSON>ết nối Odoo DB
        self.odoo_conn = psycopg2.connect(
            host=Settings.ODOO_DB_HOST,
            database=Settings.ODOO_DB_NAME,
            user=Settings.ODOO_DB_USER,
            password=Settings.ODOO_DB_PASSWORD
        )
        # Kết nối Neo4j
        self.neo4j_driver = GraphDatabase.driver(
            Settings.NEO4J_URI,
            auth=(Settings.NEO4J_USER, Settings.NEO4J_PASSWORD)
        )

    def build(self):
        self._clear_graph()
        self._create_models()
        self._create_fields()
        self._create_selections()
        self._create_glossary_terms()
        self._create_vector_index()
        logging.info("✅ KG đã được xây dựng xong!")

    def _clear_graph(self):
        with self.neo4j_driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")

    def _create_models(self):
        """Tạo node Model với odoo_model = 'account.move'"""
        cur = self.odoo_conn.cursor()
        cur.execute("""
            SELECT model, name 
            FROM ir_model 
            WHERE model NOT LIKE 'ir.%' 
              AND model NOT LIKE 'base_%'
        """)
        with self.neo4j_driver.session() as session:
            for odoo_model, name in cur.fetchall():
                session.run("""
                    CREATE (:Model {
                        odoo_model: $odoo_model,
                        rdfs__label: $name
                    })
                """, odoo_model=odoo_model, name=name)

    def _create_fields(self):
        """Tạo node Field với odoo_model và odoo_field"""
        cur = self.odoo_conn.cursor()
        cur.execute("""
            SELECT model, name, field_description, ttype
            FROM ir_model_fields 
            WHERE ttype IN ('char', 'text', 'integer', 'float', 'monetary', 
                           'date', 'datetime', 'boolean', 'selection', 'many2one')
              AND name NOT IN ('id', 'create_uid', 'write_uid', 'create_date', 'write_date')
        """)
        with self.neo4j_driver.session() as session:
            for odoo_model, field_name, desc, ttype in cur.fetchall():
                session.run("""
                    MATCH (m:Model {odoo_model: $odoo_model})
                    CREATE (m)<-[:BELONGS_TO]-(f:Field {
                        odoo_model: $odoo_model,
                        odoo_field: $field_name,
                        rdfs__label: $desc,
                        odoo_type: $ttype
                    })
                """, 
                odoo_model=odoo_model,
                field_name=field_name,
                desc=desc or field_name,
                ttype=ttype)

    def _create_selections(self):
        """Tạo SelectionValue từ ir_model_fields_selection"""
        cur = self.odoo_conn.cursor()
        cur.execute("""
            SELECT model, field, value, name 
            FROM ir_model_fields_selection
        """)
        with self.neo4j_driver.session() as session:
            for odoo_model, field_name, key, label in cur.fetchall():
                session.run("""
                    MATCH (f:Field {odoo_model: $odoo_model, odoo_field: $field_name})
                    CREATE (f)<-[:VALUE_OF]-(s:SelectionValue {
                        odoo__key: $key,
                        rdfs__label: $label
                    })
                """, 
                odoo_model=odoo_model,
                field_name=field_name,
                key=key,
                label=label)

    def _create_glossary_terms(self):
        """Tạo Term từ glossary.csv"""
        from utils.text_utils import TextUtils
        glossary = TextUtils.load_glossary("data/glossary.csv")
        with self.neo4j_driver.session() as session:
            for term, concept in glossary.items():
                session.run("""
                    CREATE (:Term {
                        name: $term,
                        concept: $concept
                    })
                """, term=term, concept=concept)

    def _create_vector_index(self):
        """Tạo vector index cho semantic search (Neo4j >= 5.11)"""
        try:
            with self.neo4j_driver.session() as session:
                # Tạo embedding cho mọi node có rdfs__label
                result = session.run("""
                    MATCH (n)
                    WHERE n.rdfs__label IS NOT NULL
                    RETURN id(n) AS id, n.rdfs__label AS label
                """)
                nodes = [(record["id"], record["label"]) for record in result]

                # Dùng Ollama để embed (bạn cần cài ollama server)
                from utils.embedding import OllamaEmbedder
                embedder = OllamaEmbedder()

                for node_id, label in nodes:
                    embedding = embedder.embed(label)
                    session.run("""
                        MATCH (n) WHERE id(n) = $id
                        SET n.embedding = $embedding
                    """, id=node_id, embedding=embedding)

                # Tạo vector index
                session.run("""
                    CREATE VECTOR INDEX node_embedding IF NOT EXISTS
                    FOR (n) ON (n.embedding)
                    OPTIONS {
                      indexConfig: {
                        `vector.dimensions`: 768,
                        `vector.similarity_function`: 'cosine'
                      }
                    }
                """)
            print("✅ Vector index đã được tạo!")
        except Exception as e:
            print(f"⚠️ Không tạo được vector index: {e}")
            print("💡 Đảm bảo bạn dùng Neo4j >= 5.11 và đã cài Ollama")

    def close(self):
        self.odoo_conn.close()
        self.neo4j_driver.close()