# graph/neo4j_client.py (<PERSON><PERSON><PERSON><PERSON> bản hoàn chỉnh)
from neo4j import GraphDatabase
from typing import List, Dict, Any
from config.settings import Settings
from utils.embedding import OllamaEmbedder  # g<PERSON><PERSON> sử bạn đã có

class Neo4jClient:
    def __init__(self):
        self.driver = GraphDatabase.driver(
            Settings.NEO4J_URI,
            auth=(Settings.NEO4J_USER, Settings.NEO4J_PASSWORD)
        )
        self.embedder = OllamaEmbedder()

    def close(self):
        self.driver.close()

    def retrieve_subgraph(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """Truy vấn vector index cho từng label"""
        if not keywords:
            return self._fuzzy_search(keywords)

        query_embedding = self.embedder.embed(" ".join(keywords))
        all_records = []

        # Danh sách label tương ứng với index
        labels = ["Model", "Field", "Term", "SelectionValue"]

        with self.driver.session() as session:
            for label in labels:
                try:
                    result = session.run(f"""
                        CALL db.index.vector.queryNodes('node_embedding_{label}', 5, $embedding)
                        YIELD node, score
                        WHERE score > 0.5
                        OPTIONAL MATCH (node)-[r]-(m)
                        RETURN DISTINCT node, type(r) AS rel_type, m
                    """, embedding=query_embedding)
                    all_records.extend([r.data() for r in result])
                except Exception as e:
                    print(f"⚠️ Lỗi truy vấn index {label}: {e}")

        # Nếu không có kết quả từ vector search, dùng fuzzy
        if not all_records:
            return self._fuzzy_search(keywords)

        return all_records[:30]  # giới hạn kết quả

    def extract_tables_from_subgraph(self, subgraph: List[Dict]) -> List[str]:
        """
        Trích xuất danh sách tên bảng (Odoo table name) từ subgraph.
        Ví dụ: "account.move" → "account_move"
        """
        tables = set()
        for record in subgraph:
            node = record.get("node")
            if not node:
                continue

            labels = node.get("rdfs__label", [])
            # Nếu là Model node, lấy odoo_model
            if "Model" in labels:
                odoo_model = node.get("odoo_model")
                if odoo_model:
                    # Chuyển account.move → account_move
                    table_name = odoo_model.replace(".", "_")
                    tables.add(table_name)

            # Nếu là Field node, lấy model của nó
            elif "Field" in labels:
                odoo_model = node.get("odoo_model")
                if odoo_model:
                    table_name = odoo_model.replace(".", "_")
                    tables.add(table_name)

        return list(tables)