# graph/neo4j_client.py (<PERSON><PERSON><PERSON><PERSON> bản hoàn chỉnh)
from neo4j import GraphDatabase
from typing import List, Dict, Any
from config.settings import Settings
from utils.embedding import OllamaEmbedder  # gi<PERSON> sử bạn đã có

class Neo4jClient:
    def __init__(self):
        self.driver = GraphDatabase.driver(
            Settings.NEO4J_URI,
            auth=(Settings.NEO4J_USER, Settings.NEO4J_PASSWORD)
        )
        self.embedder = OllamaEmbedder()

    def close(self):
        self.driver.close()

    def retrieve_subgraph(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """Truy vấn subgraph liên quan đến từ khóa"""
        # Thử vector search trước
        if keywords:
            query_embedding = self.embedder.embed(" ".join(keywords))
            semantic_query = """
            CALL db.index.vector.queryNodes('node_embedding', 10, $embedding)
            YIELD node, score
            WHERE score > 0.5
            OPTIONAL MATCH (node)-[r]-(m)
            RETURN DISTINCT node, type(r) AS rel_type, m
            """
            with self.driver.session() as session:
                result = session.run(semantic_query, embedding=query_embedding)
                records = [r.data() for r in result]
                if records:
                    return records

        # Fallback: tìm theo từ khóa
        fuzzy_query = """
        MATCH (n)
        WHERE any(k IN $keywords WHERE 
            toLower(coalesce(n.rdfs__label, '')) CONTAINS toLower(k) OR
            toLower(coalesce(n.name, '')) CONTAINS toLower(k) OR
            toLower(coalesce(n.concept, '')) CONTAINS toLower(k)
        )
        OPTIONAL MATCH (n)-[r]-(m)
        RETURN DISTINCT node, type(r), m
        LIMIT 30
        """
        with self.driver.session() as session:
            result = session.run(fuzzy_query, keywords=keywords)
            return [r.data() for r in result]

    def extract_tables_from_subgraph(self, subgraph: List[Dict]) -> List[str]:
        """
        Trích xuất danh sách tên bảng (Odoo table name) từ subgraph.
        Ví dụ: "account.move" → "account_move"
        """
        tables = set()
        for record in subgraph:
            node = record.get("node")
            if not node:
                continue

            labels = node.get("labels", [])
            # Nếu là Model node, lấy odoo_model
            if "Model" in labels:
                odoo_model = node.get("odoo_model")
                if odoo_model:
                    # Chuyển account.move → account_move
                    table_name = odoo_model.replace(".", "_")
                    tables.add(table_name)
            
            # Nếu là Field node, lấy model của nó
            elif "Field" in labels:
                odoo_model = node.get("odoo_model")
                if odoo_model:
                    table_name = odoo_model.replace(".", "_")
                    tables.add(table_name)

        return list(tables)