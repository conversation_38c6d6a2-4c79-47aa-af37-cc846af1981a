from translation.translator import Translator
from nl2sql.schema_linker import <PERSON>hem<PERSON><PERSON>inker
from nl2sql.agent_sql import SQLAgentWrapper
from response.answer_generator import AnswerGenerator
from utils.text_utils import TextUtils
from config.settings import Settings
import os

# Thiết lập OpenAI API (nếu dùng)
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY", "")

def build_db_uri():
    return (
        f"postgresql+psycopg2://{Settings.ODOO_DB_USER}:{Settings.ODOO_DB_PASSWORD}"
        f"@{Settings.ODOO_DB_HOST}/{Settings.ODOO_DB_NAME}"
    )

def main():
    question_vi = "Doanh thu từ khách hàng ABC tháng 10 năm 2024 là bao nhiêu?"

    # 1. <PERSON><PERSON><PERSON> hóa thuật ngữ doanh nghiệp
    glossary = TextUtils.load_glossary("data/glossary.csv")
    question_vi_norm = TextUtils.normalize_business_terms(question_vi, glossary)
    print("📝 Câu hỏi đã chuẩn hóa:", question_vi_norm)

    # 2. Dịch sang tiếng Anh
    translator = Translator()
    question_en = translator.vi_to_en(question_vi_norm)
    print("🌐 Câu hỏi (EN):", question_en)

    # 3. Xác định bảng liên quan
    linker = SchemaLinker()
    relevant_tables = linker.get_relevant_tables(question_en)
    print("🔍 Bảng liên quan:", relevant_tables)

    # 4. Sinh và thực thi SQL
    agent = SQLAgentWrapper(
        db_uri=build_db_uri(),
        include_tables=relevant_tables
    )
    sql_result = agent.run(question_en)
    print("📊 Kết quả SQL:", sql_result)

    # 5. Trả lời tiếng Việt
    answer_gen = AnswerGenerator()
    final_answer = answer_gen.generate_vi(question_vi, sql_result)
    print("\n✅ Trả lời cuối cùng:")
    print(final_answer)

if __name__ == "__main__":
    main()