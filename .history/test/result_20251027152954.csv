cửa hàng có doanh số cao nhất trong tháng,"SELECT
    es.ecommerce_shop_id,
    COALESCE(SUM(es.order_selling_price), 0) AS monthly_revenue
FROM ecommerce_statement es
JOIN ecommerce_order eo ON eo.id = es.ecommerce_order_id
WHERE eo.state NOT IN ('cancelled', 'cancel')
  AND es.ecommerce_shop_id IS NOT NULL
  AND date_trunc('month', eo.created_at) = date_trunc('month', CURRENT_DATE)
GROUP BY es.ecommerce_shop_id
ORDER BY monthly_revenue DESC;",
Ba<PERSON> nhi<PERSON>u đơn hàng shopee đã giao hàng,"SELECT
    COUNT(DISTINCT eo.id) AS total_shopee_orders,
    COUNT(DISTINCT eo.res_partner_id) AS customers_with_delivered,
    COUNT(DISTINCT eol.ecommerce_product_product_id) AS products_sold
FROM ecommerce_order eo
JOIN utm_medium um ON um.id = eo.utm_medium_id
LEFT JOIN ecommerce_order_line eol ON eol.ecommerce_order_id = eo.id
WHERE unaccent(LOWER(um.name)) ILIKE unaccent(LOWER('%shopee%'))
  AND eo.state = 'done';","[(232, 0, 58)]"
cửa hàng có doanh số cao nhất trong tháng,"SELECT
    eo.utm_name AS store,
    SUM(eol.price_subtotal) AS monthly_revenue
FROM ecommerce_order eo
JOIN ecommerce_order_line eol ON eol.model_id = CAST(eo.id AS VARCHAR)
WHERE date_trunc('month', eo.date_order) = date_trunc('month', CURRENT_DATE)
  AND eo.state NOT IN ('cancel')
  AND eo.active = TRUE
GROUP BY eo.utm_name
ORDER BY monthly_revenue DESC;","❌ Lỗi sau 2 lần thử: (psycopg2.errors.UndefinedColumn) column eo.utm_name does not exist
LINE 2:     eo.utm_name AS store,
            ^

[SQL: SELECT
    eo.utm_name AS store,
    SUM(eol.price_subtotal) AS monthly_revenue
FROM ecommerce_order eo
JOIN ecommerce_order_line eol ON eol.model_id = CAST(eo.id AS VARCHAR)
WHERE date_trunc('month', eo.date_order) = date_trunc('month', CURRENT_DATE)
  AND eo.state NOT IN ('cancel')
  AND eo.active = TRUE
GROUP BY eo.utm_name
ORDER BY monthly_revenue DESC;]
(Background on this error at: https://sqlalche.me/e/20/f405)"
