# utils/embedding.py
from typing import List
from ollama import Client, embeddings

class OllamaEmbedder:
    """
    Trình tạo embedding dùng Ollama API.
    Yêu cầu: Ollama server đang chạy (mặc định: http://localhost:11434)
    """
    def __init__(self, model: str = "nomic-embed-text:latest"):
        self.model = model

    def embed(self, text: str) -> List[float]:
        """
        Tạo embedding cho văn bản.
        Trả về: list[float] có độ dài 768 (với nomic-embed-text)
        """
        try:
            # Sử dụng thư viện ollama để tạo embedding
            response = embeddings(model=self.model, prompt=text)
            embedding = response["embedding"]
            return embedding
        except Exception as e:
            print(f"❌ Lỗi khi tạo embedding: {e}")
            # Tr<PERSON> về embedding mặc định (tránh crash)
            return [0.0] * 768

class OllamaLLM:
    """
    Tr<PERSON><PERSON> sinh văn bản dùng Ollama API.
    Yêu cầu: Ollama server đang chạy (mặc định: http://localhost:11434)
    """
    def __init__(self, api_key: str, model: str = "llama3:8b"):
        self.model = model
        self.ollama_client = Client(
            host="https://ollama.com",
            headers={'Authorization': 'Bearer ' + api_key}
        )

    def generate(self, prompt: str, temperature: float = 0.7, top_p: float = 0.95, stream: bool = False) -> str:
        """
        Sinh văn bản dựa trên prompt.
        """
        try:
            response = self.ollama_client.generate(model=self.model, prompt=prompt, temperature=temperature, top_p=top_p, stream=stream)
            return response.response
        except Exception as e:
            print(f"❌ Lỗi khi sinh văn bản: {e}")
            return ""