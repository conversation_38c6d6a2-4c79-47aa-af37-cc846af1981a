# utils/ddl_generator.py
from typing import Dict, List

# Ánh xạ kiểu Odoo → DuckDB
ODOO_TO_DUCKDB = {
    "char": "VARCHAR",
    "text": "VARCHAR",
    "selection": "VARCHAR",
    "integer": "BIGINT",
    "many2one": "BIGINT",
    "float": "DOUBLE",
    "monetary": "DOUBLE",
    "date": "DATE",
    "datetime": "TIMESTAMP",
    "boolean": "BOOLEAN",
    "many2many": "VARCHAR",  # không dùng trực tiếp
    "one2many": "VARCHAR",   # không dùng trực tiếp
}

def generate_ddl_from_schema(schema: Dict[str, dict], tables: List[str]) -> str:
    """
    Tạo DDL từ schema cho danh sách bảng chỉ định.

    :param schema: Dict từ file table_schema.json
    :param tables: <PERSON>h sách tên bảng cần tạo DDL
    :return: Chuỗi DDL hoàn chỉnh
    """
    ddl_parts = []
    for table in tables:
        if table not in schema:
            continue

        meta = schema[table]
        cols = []
        for col_name, col_info in meta["columns"].items():
            odoo_type = col_info.get("type", "char")
            duckdb_type = ODOO_TO_DUCKDB.get(odoo_type, "VARCHAR")
            cols.append(f"    {col_name} {duckdb_type}")

            if odoo_type == "selection":
                # Thêm mô tả cho column selection
                selections = col_info.get("selection", [])
                if selections:
                    cols.append(f"    -- {col_name} has values: {', '.join(selections)}")

            if odoo_type in ("many2many", "one2many"):
                # Thêm mô tả cho column relation
                relation_table = col_info.get("relation_table", "")
                if relation_table:
                    cols.append(f"    -- {col_name} is a relation to {relation_table}")

            if odoo_type == "many2one":
                # Thêm mô tả cho column relation
                foreign_keys = col_info.get("foreign_keys", [])
                for foreign_key in foreign_keys:
                    cols.append(f"    -- {col_name} is a foreign key to {foreign_key['foreign_table']}.{foreign_key['foreign_column']}")

        if cols:  # chỉ tạo bảng nếu có cột
            ddl = f"-- {meta.get('description', '')}\nCREATE TABLE {table} (\n" + ",\n".join(cols) + "\n);"
            ddl_parts.append(ddl)

    return "\n\n".join(ddl_parts)