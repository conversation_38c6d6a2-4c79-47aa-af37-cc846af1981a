# utils/ddl_generator.py
from typing import Dict, List

# Ánh xạ kiểu Odoo → DuckDB
ODOO_TO_DUCKDB = {
    "char": "VARCHAR",
    "text": "VARCHAR",
    "selection": "VARCHAR",
    "integer": "BIGINT",
    "many2one": "BIGINT",
    "float": "DOUBLE",
    "monetary": "DOUBLE",
    "date": "DATE",
    "datetime": "TIMESTAMP",
    "boolean": "BOOLEAN",
    "many2many": "VARCHAR",  # không dùng trực tiếp
    "one2many": "VARCHAR",   # không dùng trực tiếp
}

def generate_ddl_from_schema(schema: Dict[str, dict], tables: List[str]) -> str:
    """
    Tạo DDL từ schema cho danh sách bảng chỉ định.
    
    :param schema: Dict từ file table_schema.json
    :param tables: <PERSON>h sách tên bảng cần tạo DDL
    :return: Chuỗi DDL hoàn chỉnh
    """
    ddl_parts = []
    for table in tables:
        if table not in schema:
            continue
            
        meta = schema[table]
        cols = []
        for col_name, col_info in meta["columns"].items():
            odoo_type = col_info.get("type", "char")
            duckdb_type = ODOO_TO_DUCKDB.get(odoo_type, "VARCHAR")
            cols.append(f"    {col_name} {duckdb_type}")
        
        if cols:  # chỉ tạo bảng nếu có cột
            ddl = f"CREATE TABLE {table} (\n" + ",\n".join(cols) + "\n);"
            ddl_parts.append(ddl)
    
    return "\n\n".join(ddl_parts)