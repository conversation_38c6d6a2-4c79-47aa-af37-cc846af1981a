# utils/embedding.py
import requests
import json
from typing import List

class OllamaEmbedder:
    """
    Trình tạo embedding dùng Ollama API.
    Yêu cầu: Ollama server đang chạy (mặc định: http://localhost:11434)
    """
    def __init__(self, model: str = "nomic-embed-text:latest"):
        self.model = model
        self.base_url = "http://localhost:11434"  # URL mặc định của Ollama

    def embed(self, text: str) -> List[float]:
        """
        Tạo embedding cho văn bản.
        Trả về: list[float] có độ dài 768 (với nomic-embed-text)
        """
        payload = {
            "model": self.model,
            "input": text
        }
        try:
            response = requests.post(
                f"{self.base_url}/api/embeddings",
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            data = response.json()
            return data["embedding"]
        except Exception as e:
            print(f"❌ Lỗi khi tạo embedding: {e}")
            # Tr<PERSON> về embedding mặc định (tr<PERSON>h crash)
            return [0.0] * 768