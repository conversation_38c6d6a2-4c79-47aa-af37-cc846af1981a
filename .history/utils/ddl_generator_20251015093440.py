# utils/ddl_generator.py
TYPE_MAPPING = {
    "char": "VARCHAR",
    "text": "VARCHAR",
    "selection": "VARCHAR",
    "integer": "BIGINT",
    "many2one": "BIGINT",
    "float": "DOUBLE",
    "monetary": "DOUBLE",
    "date": "DATE",
    "datetime": "TIMESTAMP",
    "boolean": "BOOLEAN",
    # Mặc định
    "default": "VARCHAR"
}

def generate_ddl_from_schema(schema: dict) -> str:
    """
    schema = {
        "account_move": {
            "columns": [
                {"name": "id", "type": "integer"},
                {"name": "partner_id", "type": "many2one"},
                {"name": "invoice_date", "type": "date"},
                ...
            ]
        },
        ...
    }
    """
    ddl_lines = []
    for table_name, meta in schema.items():
        cols = []
        for col in meta["columns"]:
            sql_type = TYPE_MAPPING.get(col["type"], TYPE_MAPPING["default"])
            cols.append(f"    {col['name']} {sql_type}")
        ddl = f"CREATE TABLE {table_name} (\n" + ",\n".join(cols) + "\n);"
        ddl_lines.append(ddl)
    return "\n\n".join(ddl_lines)