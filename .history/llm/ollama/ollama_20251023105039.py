class OllamaLLM:
    """
    Trình sinh văn bản dùng Ollama API.
    Yêu cầu: Ollama server đ<PERSON> ch<PERSON> (mặc định: http://localhost:11434)
    """
    def __init__(self, api_key: str, model: str = "llama3:8b"):
        self.model = model
        self.ollama_client = Client(
            host="https://ollama.com",
            headers={'Authorization': 'Bearer ' + api_key}
        )

    def generate(self, prompt: str, temperature: float = 0.7, top_p: float = 0.95, stream: bool = False) -> str:
        """
        Sinh văn bản dựa trên prompt.
        """
        try:
            response = self.ollama_client.generate(model=self.model, prompt=prompt, temperature=temperature, top_p=top_p, stream=stream)
            return response.response
        except Exception as e:
            print(f"❌ Lỗi khi sinh văn bản: {e}")
            return ""