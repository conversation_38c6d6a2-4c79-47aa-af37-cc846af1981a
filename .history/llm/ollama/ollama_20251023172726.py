from config.settings import Settings
from ollama import Client

class OllamaLLM:
    """
    Trình sinh văn bản dùng Ollama API.
    Yêu cầu: Ollama server đang chạy (mặc định: http://localhost:11434)
    """
    def __init__(self, model: str = "kimi-k2:1t-cloud"):
        self.model = model
        self.ollama_client = Client(
            host="https://ollama.com",
            headers={'Authorization': 'Bearer ' + Settings.OLLAMA_API_KEY}
        )

    def generate(self, prompt: str, temperature: float = 0.7, top_p: float = 0.95, stream: bool = False) -> str:
        """
        Sinh văn bản dựa trên prompt.
        """
        try:
            response = self.ollama_client.generate(model=self.model, prompt=prompt, temperature=temperature, top_p=top_p, stream=stream)
            return response.response
        except Exception as e:
            print(f"❌ Lỗi khi sinh văn bản: {e}")
            return ""