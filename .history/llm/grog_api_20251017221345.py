import requests
import json
from typing import List, Dict, Any

class GroqAPI:
    """
    Lớp dùng để gọi Groq Chat API (tương thích OpenAI format)
    Docs: https://console.groq.com/docs/api-reference
    """

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.groq.com/openai/v1"

    def chat(
        self,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 1024,
    ) -> str:
        """
        Gọi Groq API để sinh phản hồi hội thoại.

        Args:
            model: <PERSON><PERSON><PERSON> <PERSON>, ví dụ "llama-3.3-70b-versatile"
            messages: Danh sách message theo format OpenAI
            temperature: <PERSON><PERSON><PERSON> đ<PERSON> sáng tạo (0–1)
            max_tokens: Giới hạn token trả về

        Returns:
            Nội dung trả lời của assistant (str)
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
        }

        response = requests.post(f'{self.base_url}/chat/completions', headers=headers, json=payload)

        if response.status_code != 200:
            raise Exception(f"Groq API error {response.status_code}: {response.text}")

        data = response.json()
        return data["choices"][0]["message"]["content"]
