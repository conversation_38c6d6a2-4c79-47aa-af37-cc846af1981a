import requests
from typing import List, Dict
from grog.chat_model import GroqAPIResponse

class GroqAPI:
    """
    Lớp dùng để gọi Groq Chat API (tương thích OpenAI format)
    Docs: https://console.groq.com/docs/api-reference
    """

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.groq.com/openai/v1"

    def chat(
        self,
        model: str,
        messages: List[Dict[str, str]],
        temperature: float = 0.7,
        max_tokens: int = 1024,
        parse: bool = True
    ) -> str:
        """
        Gọi Groq API để sinh phản hồi hội thoại.

        Args:
            model: <PERSON><PERSON><PERSON> mô h<PERSON>nh, ví dụ "llama-3.3-70b-versatile"
            messages: Danh sách message theo format OpenAI
            temperature: Mức độ sáng tạo (0–1)
            max_tokens: <PERSON><PERSON><PERSON><PERSON> hạn token trả về

        Returns:
            <PERSON><PERSON><PERSON> dung trả lời của assistant (str)
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
        }

        response = requests.post(f'{self.base_url}/chat/completions', headers=headers, json=payload)

        if response.status_code != 200:
            raise Exception(f"Groq API error {response.status_code}: {response.text}")

        data = response.json()
        return GroqAPIResponse(**data) if parse else None
