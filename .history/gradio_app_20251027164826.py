# gradio_app.py
import gradio as gr
import os
import csv
from nl2sql.schema_linker import Schema<PERSON>inker
from nl2sql.duckdb_sql_executor import DuckDBSQLExecutor
from config.settings import Settings
from core.kg_builder import KGBuild
from response.answer_generator import AnswerGenerator

def build_db_uri():
    """Xây dựng URI kết nối database"""
    return (
        f"postgresql+psycopg2://{Settings.ODOO_DB_USER}:{Settings.ODOO_DB_PASSWORD}"
        f"@{Settings.ODOO_DB_HOST}/{Settings.ODOO_DB_NAME}"
    )

def process_question(question, slm_model, flm_model, alm_model, tm_model):
    """
    Xử lý câu hỏi và trả về kết quả

    Args:
        question (str): Câu hỏi của người dùng
        slm_model (str): SQL LLM Model
        flm_model (str): Fix LLM Model
        alm_model (str): Answer LLM Model
        tm_model (str): Translation Model

    Returns:
        tuple: (sql_query, result_data, final_answer, status)
    """
    linker = None
    try:
        if not question.strip():
            return "", "", "Vui lòng nhập câu hỏi!", "❌ Lỗi"

        # 1. Schema linking + DDL
        linker = SchemaLinker()
        _, ddl, expanded_questions_text, business_concepts = linker.get_relevant_tables_and_ddl(question)

        # 2. Sinh SQL + execute + retry
        executor = DuckDBSQLExecutor(
            db_uri=build_db_uri(),
            max_retries=2
        )
        result = executor.run(ddl, expanded_questions_text, business_concepts)

        # Lưu kết quả vào file CSV
        with open("test/result.csv", "a+", newline="", encoding="utf-8") as f:
            writer = csv.writer(f)
            writer.writerow([question, result["prompt"], result["sql"], result["result"]])

        sql_query = result["sql"]
        result_data = str(result["result"])

        # 3. Tạo câu trả lời tiếng Việt
        if result["success"]:
            answer_gen = AnswerGenerator(model=alm_model)
            final_answer = answer_gen.generate_vi(question, result["result"])
            status = "✅ Thành công"
        else:
            final_answer = f"Không thể thực thi SQL. Lỗi: {result.get('error', 'Unknown error')}"
            status = "❌ Lỗi"

        return sql_query, result_data, final_answer, status

    except Exception as e:
        error_msg = f"Lỗi xử lý: {str(e)}"
        return "", "", error_msg, "❌ Lỗi"

    finally:
        # Đảm bảo đóng kết nối KG
        if linker:
            linker.neo4j_client.close()

def refresh_schema():
    """Làm mới schema và knowledge graph"""
    try:
        print("🔄 Đang xây dựng Knowledge Graph...")
        kg = KGBuild()
        kg.build()
        kg.close()
        return "✅ Khởi tạo knowledge graph hoàn tất!"
    except Exception as e:
        return f"❌ Lỗi khi làm mới schema: {str(e)}"

def create_gradio_interface():
    """Tạo giao diện Gradio"""

    # CSS tùy chỉnh
    custom_css = """
    .gradio-container {
        max-width: 1200px !important;
    }
    .question-input {
        font-size: 16px !important;
    }
    .result-output {
        font-family: monospace !important;
    }
    """

    with gr.Blocks(css=custom_css, title="SQL Tool - Natural Language to SQL") as demo:
        gr.Markdown("""
        # 🤖 SQL Tool - Natural Language to SQL

        Công cụ chuyển đổi câu hỏi tiếng Việt thành SQL và thực thi trên database Odoo.
        """)

        with gr.Row():
            with gr.Column(scale=2):
                # Input section
                question_input = gr.Textbox(
                    label="💬 Câu hỏi của bạn",
                    placeholder="Ví dụ: Doanh thu của sản phẩm Shopee trong năm 2025?",
                    lines=3,
                    elem_classes=["question-input"]
                )

                with gr.Row():
                    submit_btn = gr.Button("🚀 Thực hiện", variant="primary", scale=2)
                    refresh_btn = gr.Button("🔄 Làm mới Schema", scale=1)

            with gr.Column(scale=1):
                # Model configuration
                gr.Markdown("### ⚙️ Cấu hình Model")
                slm_model = gr.Textbox(
                    label="SQL LLM Model",
                    value="deepseek-v3.1:671b-cloud",
                    lines=1
                )
                flm_model = gr.Textbox(
                    label="Fix LLM Model",
                    value="qwen3-coder:480b-cloud",
                    lines=1
                )
                alm_model = gr.Textbox(
                    label="Answer LLM Model",
                    value="kimi-k2:1t-cloud",
                    lines=1
                )
                tm_model = gr.Textbox(
                    label="Translation Model",
                    value="VietAI/envit5-translation",
                    lines=1
                )

        # Output section
        with gr.Row():
            with gr.Column():
                status_output = gr.Textbox(
                    label="📊 Trạng thái",
                    interactive=False,
                    lines=1
                )

                sql_output = gr.Code(
                    label="🔍 SQL Query",
                    language="sql",
                    interactive=False,
                    lines=5
                )

                result_output = gr.Textbox(
                    label="📋 Kết quả thô",
                    interactive=False,
                    lines=5,
                    elem_classes=["result-output"]
                )

                answer_output = gr.Textbox(
                    label="💬 Câu trả lời",
                    interactive=False,
                    lines=5
                )

        # Schema refresh output
        refresh_output = gr.Textbox(
            label="🔄 Kết quả làm mới Schema",
            interactive=False,
            visible=False
        )

        # Event handlers
        submit_btn.click(
            fn=process_question,
            inputs=[question_input, slm_model, flm_model, alm_model, tm_model],
            outputs=[sql_output, result_output, answer_output, status_output]
        )

        refresh_btn.click(
            fn=refresh_schema,
            outputs=[refresh_output]
        ).then(
            lambda: gr.update(visible=True),
            outputs=[refresh_output]
        )

        # Examples
        gr.Examples(
            examples=[
                ["Doanh thu của sản phẩm Shopee trong năm 2025?"],
                ["Có bao nhiêu khách hàng đã mua hàng trong tháng này?"],
                ["Top 5 sản phẩm bán chạy nhất?"],
                ["Tổng số đơn hàng trong quý 1 năm 2024?"]
            ],
            inputs=[question_input]
        )

        gr.Markdown("""
        ### 📝 Hướng dẫn sử dụng:
        1. **Nhập câu hỏi**: Gõ câu hỏi tiếng Việt vào ô "Câu hỏi của bạn"
        2. **Cấu hình Model** (tùy chọn): Thay đổi các model AI nếu cần
        3. **Thực hiện**: Nhấn nút "🚀 Thực hiện" để xử lý
        4. **Xem kết quả**: Kiểm tra SQL query, kết quả thô và câu trả lời cuối cùng
        5. **Làm mới Schema**: Nhấn "🔄 Làm mới Schema" nếu cần cập nhật cấu trúc database
        """)

    return demo

def main():
    """Chạy ứng dụng Gradio"""
    demo = create_gradio_interface()
    demo.launch(
        server_name="0.0.0.0",  # Cho phép truy cập từ bên ngoài
        server_port=7860,       # Port mặc định của Gradio
        share=False,            # Không tạo public link
        debug=True              # Bật debug mode
    )

if __name__ == "__main__":
    main()
