# main.py
import os
import argparse
from translation.translator import Translator
from nl2sql.schema_linker import <PERSON>hemaLinker
from nl2sql.duckdb_sql_executor import DuckDBSQLExecutor
from response.answer_generator import AnswerGenerator
from utils.text_utils import TextUtils
from config.settings import Settings
from core.kg_builder import KGBuild
from core.schema_extractor import extract_schema_to_json
from llm.grog.grog_api import GroqAP<PERSON>

def build_db_uri():
    return (
        f"postgresql+psycopg2://{Settings.ODOO_DB_USER}:{Settings.ODOO_DB_PASSWORD}"
        f"@{Settings.ODOO_DB_HOST}/{Settings.ODOO_DB_NAME}"
    )

def initSchema():
  print("🔄 Đang trích xuất schema từ Odoo DB...")
  extract_schema_to_json()

  print("✅ Khởi tạo hoàn tất! Chạy: python main.py")

def initKG():
  print("🔄 Đang xây dựng Knowledge Graph...")
  kg = KGBuild()
  kg.build()
  kg.close()

  print("✅ Khởi tạo hoàn tất! Chạy: python main.py")

def main():
  parser = argparse.ArgumentParser()
  parser.add_argument("--refresh-schema", action="store_true",
                      help="Force update schema from Odoo DB")
  parser.add_argument("--question", type=str, default="Doanh thu của sản Shopee trong năm 2025?",
                      help="Câu hỏi của người dùng")
  parser.add_argument("--slm", type=str, default="kimi-k2:1t-cloud",
                      help="SQL LLM Model")
  parser.add_argument("--flm", type=str, default="glm-4.6:cloud",
                      help="Fix LLM Model")
  parser.add_argument("--alm", type=str, default="glm-4.6:cloud",
                      help="Answer LLM Model")
  parser.add_argument("--tm", type=str, default="VietAI/envit5-translation",
                      help="Translation Model")
  args = parser.parse_args()
  # Khởi tạo lại nếu cần
  if args.refresh_schema:
    initSchema()

  # groq = GroqAPI(api_key=Settings.GROQ_API_KEY)
  # Câu hỏi mẫu (hoặc bạn có thể dùng input())
  question_vi = args.question

  # 1. Chuẩn hóa & dịch
  try:
    # 1. Chuẩn hóa & dịch
    # glossary = TextUtils.load_glossary("data/glossary.csv")
    # question_vi_norm = TextUtils.normalize_business_terms(question_vi, glossary)
    # translator = Translator(model_name=args.tm or "VietAI/envit5-translation")
    # question_en = translator.vi_to_en(question_vi_norm)
    # print(f"🌐 Câu hỏi (EN): {question_en}")

  #   # 2. Schema linking + DDL
  #   linker = SchemaLinker()
  #   _, ddl, expanded_questions_text = linker.get_relevant_tables_and_ddl(question_vi)

  #   # 3. Sinh SQL + execute + retry
  #   executor = DuckDBSQLExecutor(
  #       db_uri=build_db_uri(),
  #       max_retries=2
  #   )
  #   result = executor.run(ddl, expanded_questions_text)

  #   print("\n✅ SQL:")
  #   print(result["sql"])
  #   print("\n📊 Kết quả:")
  #   print(result["result"])

  #   # # 4. Trả lời tiếng Việt
  #   # if result["success"]:
  #   #   answer_gen = AnswerGenerator(model=args.alm or "gemma3:1b")
  #   #   final_answer = answer_gen.generate_vi(question_vi, result["result"])
  #   #   print("\n💬 Trả lời:")
  #   #   print(final_answer)

  # finally:
  #   # Đảm bảo đóng kết nối KG
  #   if 'linker' in locals():
  #     linker.neo4j_client.close()

if __name__ == "__main__":
    main()