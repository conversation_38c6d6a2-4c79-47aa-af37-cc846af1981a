# main.py
from translation.translator import Translator
from nl2sql.schema_linker import <PERSON>hema<PERSON>inker
from nl2sql.duckdb_sql_executor import DuckDBS<PERSON>Executor
from response.answer_generator import AnswerGenerator
from utils.text_utils import TextUtils
from config.settings import Settings
import os
from core.schema_extractor import extract_schema_to_json
import argparse

def build_db_uri():
  return (
      f"postgresql+psycopg2://{Settings.ODOO_DB_USER}:{Settings.ODOO_DB_PASSWORD}"
      f"@{Settings.ODOO_DB_HOST}/{Settings.ODOO_DB_NAME}"
  )

SCHEMA_PATH = "data/table_schema.json"

def ensure_schema_exists():
  if not os.path.exists(SCHEMA_PATH):
      print(f"🔄 Schema chưa tồn tại. Đang tạo: {SCHEMA_PATH}")
      extract_schema_to_json(SCHEMA_PATH)
  else:
      print(f"✅ Đã tìm thấy schema: {SCHEMA_PATH}")

def main():
  parser = argparse.ArgumentParser()
  parser.add_argument("--refresh-schema", action="store_true",
                      help="Force update schema from Odoo DB")
  parser.add_argument("--question", type=str, default="Doanh thu từ khách hàng ABC tháng 10 năm 2024?",
                      help="Câu hỏi của người dùng")
  parser.add_argument("--slm", type=str, default="duckdb-nsql:latest",
                      help="SQL LLM Model")
  parser.add_argument("--flm", type=str, default="sqlcoder:7b",
                      help="Fix LLM Model")
  parser.add_argument("--alm", type=str, default="gemma3:1b",
                      help="Answer LLM Model")
  parser.add_argument("--tm", type=str, default="VietAI/envit5-translation",
                      help="Translation Model")
  args = parser.parse_args()
  if args.refresh_schema or not os.path.exists(SCHEMA_PATH):
    print("🔄 Đang cập nhật schema từ Odoo DB...")
    extract_schema_to_json(SCHEMA_PATH)
  question_vi = args.question

  # 1. Chuẩn hóa & dịch
  glossary = TextUtils.load_glossary("data/glossary.csv")
  question_vi_norm = TextUtils.normalize_business_terms(question_vi, glossary)
  translator = Translator(model_name=args.tm)
  question_en = translator.vi_to_en(question_vi_norm)
  print("🌐 Câu hỏi (EN):", question_en)

  # 2. Lấy DDL
  linker = SchemaLinker()
  tables, ddl = linker.get_relevant_tables_and_ddl(question_en)
  # print("📜 DDL:\n", ddl)
  print("🔍 Bảng liên quan:", tables)
  # 3. Execute với retry
  # executor = DuckDBSQLExecutor(
  #     db_uri=build_db_uri(),
  #     sql_llm_model= args.slm or "duckdb-nsql:7b",
  #     fix_llm_model= args.flm or "llama3:8b",  # hoặc "gpt-4o" nếu có OpenAI API
  #     max_retries=2
  # )
  # result = executor.run(ddl, question_en)

  # print("✅ SQL cuối cùng:")
  # print(result["sql"])
  # print("\n📊 Kết quả:")
  # print(result["result"])

  # # 4. Trả lời tiếng Việt (nếu thành công)
  # if result["success"]:
  #     answer_gen = AnswerGenerator(model=args.alm or "gemma3:1b")
  #     final_answer = answer_gen.generate_vi(question_vi, result["result"])
  #     print("\n💬 Trả lời người dùng:")
  #     print(final_answer)

if __name__ == "__main__":
  main()
