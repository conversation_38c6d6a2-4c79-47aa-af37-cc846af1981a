import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # Odoo DB
    ODOO_DB_HOST = os.getenv("ODOO_DB_HOST", "localhost")
    ODOO_DB_NAME = os.getenv("ODOO_DB_NAME", "odoo")
    ODOO_DB_USER = os.getenv("ODOO_DB_USER", "odoo")
    ODOO_DB_PASSWORD = os.getenv("ODOO_DB_PASSWORD", "odoo")

    # LLM Models
    SQL_LLM_MODEL = os.getenv("SQL_LLM_MODEL", "llama3:8b")
    ANSWER_LLM_MODEL = os.getenv("ANSWER_LLM_MODEL", "jan-nano")