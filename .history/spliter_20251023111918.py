def split_blocks_safe(input_file: str, max_lines: int = 1000):
    with open(input_file, "r", encoding="utf-8") as f:
        lines = f.readlines()

    # Mỗi bản ghi = 3 dòng
    block_size = 3
    blocks_per_file = (max_lines // block_size)  # 1000 // 3 = 333
    lines_per_file = blocks_per_file * block_size  # 999

    total_lines = len(lines)
    num_blocks = total_lines // block_size

    part = 1
    for start_block in range(0, num_blocks, blocks_per_file):
        end_block = min(start_block + blocks_per_file, num_blocks)
        start_line = start_block * block_size
        end_line = end_block * block_size

        chunk = lines[start_line:end_line]
        if not chunk:
            break

        output_name = f"{input_file.rsplit('.', 1)[0]}_part{part:03d}.txt"
        with open(output_name, "w", encoding="utf-8") as out:
            out.writelines(chunk)
        print(f"✅ {output_name} - {len(chunk)} dòng ({end_block - start_block} bản ghi)")
        part += 1

    # Xử lý dòng thừa (nếu có, nhưng với dữ liệu chuẩn thì không có)
    remaining = lines[num_blocks * block_size:]
    if remaining:
        output_name = f"{input_file.rsplit('.', 1)[0]}_part{part:03d}.txt"
        with open(output_name, "w", encoding="utf-8") as out:
            out.writelines(remaining)
        print(f"⚠️  {output_name} - {len(remaining)} dòng thừa (không đủ 1 bản ghi)")

# Dùng cho file của bạn
split_blocks_safe("data/models.txt", 1000)