def split_file_by_lines(input_file: str, lines_per_file: int = 1000):
    with open(input_file, "r", encoding="utf-8", errors="ignore") as f:
        part = 1
        while True:
            lines = f.readlines(lines_per_file)
            if not lines:
                break
            output_file = f"model/{input_file.rsplit('.', 1)[0]}_part{part:03d}.txt"
            with open(output_file, "w", encoding="utf-8") as out:
                out.writelines(lines)
            print(f"Đã tạo: {output_file} ({len(lines)} dòng)")
            part += 1

# Sử dụng
split_file_by_lines("data/models.txt", 1000)