from langchain_community.agent_toolkits import create_sql_agent
from langchain_community.utilities import SQLDatabase
from langchain_community.llms import Ollama
from langchain_core.callbacks import StdOutCallbackHandler
from config.settings import Settings
import json

class SQLAgentWrapper:
    def __init__(
        self,
        model: str = None,
        db_uri: str = None,
        include_tables: list[str] = None
    ):
        self.model = model or Settings.SQL_LLM_MODEL
        self.db = SQLDatabase.from_uri(
            db_uri,
            include_tables=include_tables,
            sample_rows_in_table_info=0
        )
        self._load_table_descriptions()

        llm = Ollama(model=self.model, temperature=0)
        self.agent = create_sql_agent(
            llm=llm,
            toolkit=self.db,
            verbose=True,
            agent_type="openai-tools",
            max_iterations=5,
            handle_parsing_errors=True,
            callbacks=[StdOutCallbackHandler()]
        )

    def _load_table_descriptions(self):
        try:
            with open("data/table_descriptions_en.json", "r") as f:
                self.descriptions = json.load(f)
        except:
            self.descriptions = {}

    def run(self, question_en: str) -> str:
        return self.agent.invoke({"input": question_en})["output"]