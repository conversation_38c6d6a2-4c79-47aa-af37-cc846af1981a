#!/usr/bin/env python3
"""
Script để chạy ứng dụng Gradio SQL Tool
"""

import sys
import os
import subprocess

def check_dependencies():
    """Kiểm tra và cài đặt dependencies nếu cần"""
    try:
        import gradio
        print("✅ Gradio đã được cài đặt")
    except ImportError:
        print("📦 Đang cài đặt Gradio...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "gradio>=4.0.0"])
        print("✅ Gradio đã được cài đặt thành công")

def main():
    """Chạy ứng dụng"""
    print("🚀 Đang khởi động SQL Tool với giao diện Gradio...")
    
    # Kiểm tra dependencies
    check_dependencies()
    
    # Import và chạy ứng dụng
    try:
        from gradio_app import main as run_gradio_app
        run_gradio_app()
    except Exception as e:
        print(f"❌ Lỗi khi chạy ứng dụng: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
